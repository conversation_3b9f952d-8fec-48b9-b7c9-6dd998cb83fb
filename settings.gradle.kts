pluginManagement {
    repositories {
        maven {
            isAllowInsecureProtocol = true
            url = uri(providers.gradleProperty("prop_oppoMavenUrl").get())
            credentials {
                username = providers.gradleProperty("sonatypeUsername").get()
                password = providers.gradleProperty("sonatypeUsername").get()
            }
        }
        maven {
            isAllowInsecureProtocol = true
            url = uri(providers.gradleProperty("prop_sdkMavenUrlRelease").get())
            credentials {
                username = providers.gradleProperty("sonatypeUsername").get()
                password = providers.gradleProperty("sonatypeUsername").get()
            }
        }
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        maven {
            isAllowInsecureProtocol = true
            url = uri(providers.gradleProperty("prop_oppoMavenUrl").get())
            credentials {
                username = providers.gradleProperty("sonatypeUsername").get()
                password = providers.gradleProperty("sonatypeUsername").get()
            }
        }
        maven {
            isAllowInsecureProtocol = true
            url = uri(providers.gradleProperty("prop_sdkMavenUrlRelease").get())
            credentials {
                username = providers.gradleProperty("sonatypeUsername").get()
                password = providers.gradleProperty("sonatypeUsername").get()
            }
        }
    }
}
rootProject.name = "HuDemo"
include(":app")
include(":base")
include(":utils")
include(":buffer")
