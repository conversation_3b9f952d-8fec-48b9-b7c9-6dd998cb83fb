#
# Copyright 2022 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
[versions]
minSdk = "29"
targetSdk = "36"
compileSdk = "36"
cmake = "3.22.1"
ndk = "27.1.12297006"

#test
junit4 = "4.13.2"
androidxEspresso = "3.6.1"

guava = "33.4.0-android"
activityKtx = "1.10.1"
androidx-window = "1.4.0"
agp = "8.11.0"
asynclayoutinflater = "1.1.0"
coil = "2.4.0"
collectionKtx = "1.5.0"
commonplayer = "1.6.34PRO"
commonplayerRetriever = "1.6.20PRO"
commonsImaging = "1.0-SNAPSHOT"
concurrentFutures = "1.2.0"
constraintlayoutVersion = "2.2.1"
contentpager = "1.0.0"
coreKtx = "1.16.0"
fragmentKtx = "1.8.7"
graphicsCore = "1.0.3"
gridlayout = "1.1.0"
gson = "2.10.1"
heifwriter = "1.1.0-beta01"
itext7Core = "7.1.13"
junit = "1.2.1"
kotlin = "2.0.0"
kotlin-serialization = "1.6.0"
coroutines = "1.7.3"
lifecycleLivedataKtx = "2.9.0"
lifecycleRuntimeKtx = "2.9.0"
navigationFragmentKtx = "2.9.0"
okhttp = "4.12.0"
pagingRuntimeKtx = "3.3.6"
material = "1.12.0"
recyclerview = "1.4.0"
xmpcore = "5.1.3"

[libraries]
# Testing
androidx-test-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "androidxEspresso" }

# kt
kotlin-coroutines-android = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-android", version.ref = "coroutines" }
kotlin-serialization-json = { module = "org.jetbrains.kotlinx:kotlinx-serialization-json", version.ref = "kotlin-serialization" }

androidx-activity-ktx = { module = "androidx.activity:activity-ktx", version.ref = "activityKtx" }
androidx-asynclayoutinflater = { module = "androidx.asynclayoutinflater:asynclayoutinflater", version.ref = "asynclayoutinflater" }
androidx-collection-ktx = { module = "androidx.collection:collection-ktx", version.ref = "collectionKtx" }
androidx-concurrent-futures = { module = "androidx.concurrent:concurrent-futures", version.ref = "concurrentFutures" }
androidx-concurrent-futures-ktx = { module = "androidx.concurrent:concurrent-futures-ktx", version.ref = "concurrentFutures" }
androidx-constraintlayout = { module = "androidx.constraintlayout:constraintlayout", version.ref = "constraintlayoutVersion" }
androidx-contentpager = { module = "androidx.contentpager:contentpager", version.ref = "contentpager" }
androidx-appcompat = "androidx.appcompat:appcompat:1.7.0"
androidx-core-ktx = { module = "androidx.core:core-ktx", version.ref = "coreKtx" }
androidx-exifinterface = "androidx.exifinterface:exifinterface:1.4.1"
androidx-fragment-ktx = { module = "androidx.fragment:fragment-ktx", version.ref = "fragmentKtx" }
androidx-graphics-core = { module = "androidx.graphics:graphics-core", version.ref = "graphicsCore" }
androidx-gridlayout = { module = "androidx.gridlayout:gridlayout", version.ref = "gridlayout" }
androidx-heifwriter = { module = "androidx.heifwriter:heifwriter", version.ref = "heifwriter" }
androidx-junit = { module = "androidx.test.ext:junit", version.ref = "junit" }
androidx-lifecycle-livedata-ktx = { module = "androidx.lifecycle:lifecycle-livedata-ktx", version.ref = "lifecycleLivedataKtx" }
androidx-lifecycle-runtime-ktx = { module = "androidx.lifecycle:lifecycle-runtime-ktx", version.ref = "lifecycleRuntimeKtx" }
androidx-lifecycle-viewmodel-ktx = { module = "androidx.lifecycle:lifecycle-viewmodel-ktx", version.ref = "lifecycleRuntimeKtx" }
androidx-navigation-fragment-ktx = { module = "androidx.navigation:navigation-fragment-ktx", version.ref = "navigationFragmentKtx" }
androidx-navigation-ui-ktx = { module = "androidx.navigation:navigation-ui-ktx", version.ref = "navigationFragmentKtx" }
androidx-paging-guava = { module = "androidx.paging:paging-guava", version.ref = "pagingRuntimeKtx" }
androidx-paging-runtime-ktx = { module = "androidx.paging:paging-runtime-ktx", version.ref = "pagingRuntimeKtx" }
androidx-recyclerview = { module = "androidx.recyclerview:recyclerview", version.ref = "recyclerview" }

commonplayer = { group = "com.oplus.TBLPlayer", name = "CommonPlayer", version.ref = "commonplayer" }
commonplayer-retriever = { module = "com.oplus.TBLPlayer:CommonPlayer-retriever", version.ref = "commonplayerRetriever" }
commons-imaging = { module = "org.apache.commons:commons-imaging", version.ref = "commonsImaging" }

gson = { module = "com.google.code.gson:gson", version.ref = "gson" }

itext7-core = { module = "com.itextpdf:itext7-core", version.ref = "itext7Core" }

junit4 = { group = "junit", name = "junit", version.ref = "junit4" }

okhttp = { module = "com.squareup.okhttp3:okhttp", version.ref = "okhttp" }

androidx-work-runtime-ktx = "androidx.work:work-runtime-ktx:2.10.1"
androidx-startup = 'androidx.startup:startup-runtime:1.2.0'
androidx-window = { module = "androidx.window:window", version.ref = "androidx-window" }
androidx-draganddrop = "androidx.draganddrop:draganddrop:1.0.0"

glide = "com.github.bumptech.glide:glide:4.15.1"

material = { group = "com.google.android.material", name = "material", version.ref = "material" }
xmpcore = { module = "com.adobe.xmp:xmpcore", version.ref = "xmpcore" }
guava = { module = "com.google.guava:guava", version.ref = "guava" }

org-mp4parser = "org.mp4parser:isoparser:1.9.56"

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
android-library = { id = "com.android.library", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
