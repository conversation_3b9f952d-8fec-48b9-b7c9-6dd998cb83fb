package com.hu.demo.utils

import androidx.annotation.Size
import java.nio.charset.Charset


fun ByteArray.toInt(reverse: Boolean = false): Int {
    if (size > Int.SIZE_BYTES) {
        throw IllegalArgumentException("byteArray must less 4")
    }
    return if (reverse) {
        foldRight(0) { byte, acc ->
            acc.shl(Byte.SIZE_BITS) or (byte.toInt() and 0xFF)
        }
    } else {
        fold(0) { acc, byte ->
            acc.shl(Byte.SIZE_BITS) or (byte.toInt() and 0xFF)
        }
    }
}

fun ByteArray.toUInt(reverse: Boolean = false): UInt {
    if (size > Int.SIZE_BYTES) {
        throw IllegalArgumentException("byteArray must less 4")
    }
    return if (reverse) {
        foldRight(0u) { byte, acc ->
            acc.shl(Byte.SIZE_BITS) or (byte.toInt() and 0xFF).toUInt()
        }
    } else {
        fold(0u) { acc, byte ->
            acc.shl(Byte.SIZE_BITS) or (byte.toInt() and 0xFF).toUInt()
        }
    }
}

fun Int.toBytes(): ByteArray {
    val array = ByteArray(4)
    array[0] = ((this shr 24) and 0xFF).toByte()
    array[1] = ((this shr 16) and 0xFF).toByte()
    array[2] = ((this shr 8) and 0xFF).toByte()
    array[3] = (this and 0xFF).toByte()
    return array
}

fun ByteArray.toInt(): Int {
    return (this[0].toUByte().toInt() shl 24) or (this[1].toUByte().toInt() shl 16) or (this[2].toUByte().toInt() shl 8) or (this[3].toUByte()
        .toInt())
}

fun ByteArray.contentHexString(): String {
    return joinToString(prefix = "[", postfix = "]") { it.toHexString() }
}

fun ByteArray.toIntArray(): IntArray {
    val array = IntArray(this.size / 4)
    for (index in array.indices) {
        array[index] = (this[index * 4].toUByte().toInt() shl 24) or
            (this[index * 4 + 1].toUByte().toInt() shl 16) or
            (this[index * 4 + 2].toUByte().toInt() shl 8) or
            (this[index * 4 + 3].toUByte().toInt())
    }
    return array
}

fun Int.toByteString(): String {
    return toBytes().decodeToString()
}

fun IntArray.toIntStringArray(): Array<String> {
    return Array(size) {
        this[it].toByteString()
    }
}

fun ByteArray.toString(start: Int, length: Int, charset: Charset): String {
    return String(this, start, length, charset)
}

fun <T> Array<T>.beautyToString(): String {
    return joinToString(",\n", "[\n", "\n]")
}


fun <T> List<T>.beautyToString(): String {
    return joinToString(",\n", "[\n", "\n]")
}

/**
 * 获取3x3矩阵相乘
 *
 * @return 返回3x3矩阵相乘的结果
 */
@Size(9)
infix fun FloatArray.mul3x3MM(@Size(9) rhs: FloatArray): FloatArray {
    val r = FloatArray(9)
    r[0] = this[0] * rhs[0] + this[3] * rhs[1] + this[6] * rhs[2]
    r[1] = this[1] * rhs[0] + this[4] * rhs[1] + this[7] * rhs[2]
    r[2] = this[2] * rhs[0] + this[5] * rhs[1] + this[8] * rhs[2]
    r[3] = this[0] * rhs[3] + this[3] * rhs[4] + this[6] * rhs[5]
    r[4] = this[1] * rhs[3] + this[4] * rhs[4] + this[7] * rhs[5]
    r[5] = this[2] * rhs[3] + this[5] * rhs[4] + this[8] * rhs[5]
    r[6] = this[0] * rhs[6] + this[3] * rhs[7] + this[6] * rhs[8]
    r[7] = this[1] * rhs[6] + this[4] * rhs[7] + this[7] * rhs[8]
    r[8] = this[2] * rhs[6] + this[5] * rhs[7] + this[8] * rhs[8]
    return r
}

/**
 * 将3x3矩阵与向量相乘
 *
 * @return 返回3x3矩阵与向量相乘的结果
 */
@Size(min = 3)
infix fun FloatArray.mul3x3MV(@Size(min = 3) rhs: FloatArray): FloatArray {
    val r0 = rhs[0]
    val r1 = rhs[1]
    val r2 = rhs[2]
    rhs[0] = this[0] * r0 + this[3] * r1 + this[6] * r2
    rhs[1] = this[1] * r0 + this[4] * r1 + this[7] * r2
    rhs[2] = this[2] * r0 + this[5] * r1 + this[8] * r2
    return rhs
}

/**
 * 将3x3向量与矩阵相乘
 *
 * @return 返回3x3向量与矩阵相乘的结果
 */
@Size(9)
infix fun FloatArray.mul3x3VM(@Size(9) rhs: FloatArray): FloatArray {
    return floatArrayOf(
        this[0] * rhs[0], this[1] * rhs[1], this[2] * rhs[2],
        this[0] * rhs[3], this[1] * rhs[4], this[2] * rhs[5],
        this[0] * rhs[6], this[1] * rhs[7], this[2] * rhs[8]
    )
}
