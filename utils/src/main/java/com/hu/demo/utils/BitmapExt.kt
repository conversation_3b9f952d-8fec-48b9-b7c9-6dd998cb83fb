package com.hu.demo.utils

import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.graphics.Bitmap.CompressFormat
import android.graphics.Bitmap.Config
import android.graphics.BitmapShader
import android.graphics.Canvas
import android.graphics.ColorSpace
import android.graphics.Gainmap
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.Shader
import android.os.Build
import android.util.Log
import android.util.Size
import androidx.annotation.RequiresApi
import androidx.core.graphics.createBitmap
import androidx.core.graphics.toRect
import java.io.File
import java.nio.ByteBuffer
import kotlin.math.min

private const val TAG = "BitmapExt"
private const val COLOR_SPACE_UNKNOWN = "Unknown"

private const val ANGLE_0 = 0
private const val ANGLE_90 = 90
private const val ANGLE_180 = 180
private const val ANGLE_270 = 270
private const val ANGLE_360 = 360

const val BYTE_SIZE_1 = 1
const val BYTE_SIZE_2 = 2
const val BYTE_SIZE_4 = 4
const val BYTE_SIZE_6 = 6
const val BYTE_SIZE_8 = 8
const val BYTE_SIZE_INVALID = -1

fun Bitmap.getRectF(): RectF {
    return RectF(0f, 0f, width.toFloat(), height.toFloat())
}

/**
 * Bitmap转ByteArray
 */
fun Bitmap.toByteArray(): ByteArray {
    return toBuffer().array()
}

/**
 * Bitmap获取pixels的buffer
 */
fun Bitmap.toBuffer(): ByteBuffer {
    return ByteBuffer.allocate(byteCount).also {
        copyPixelsToBuffer(it)
        it.rewind()
    }
}

fun Bitmap.toRgbaFile(file: File) {
    file.parentFile?.mkdirs()
    file.outputStream().use { oStream ->
        toByteArray().inputStream().use { iStream ->
            iStream.copyTo(oStream)
        }
    }
}

fun Bitmap.toFile(file: File, format: CompressFormat = CompressFormat.JPEG, quality: Int = 95) {
    file.parentFile?.mkdirs()
    file.outputStream().use { oStream ->
        compress(format, quality, oStream)
    }
}

/**
 * 通过指定的色彩空间[newC]与[Bitmap.getColorSpace]比对，判断是否可以修改为指定的色彩空间
 * @param newC 指定的色彩空间
 * @return 返回是否可以修改为指定的色彩空间
 */
private fun Bitmap.canChangeColorSpace(newC: ColorSpace): Boolean {
    if (isRecycled) return false
    if (config == Config.ALPHA_8) return false
    val oldC = colorSpace ?: return false
    if (oldC.componentCount != newC.componentCount) return false
    return (0 until oldC.componentCount).all { index ->
        !((oldC.getMinValue(index) < newC.getMinValue(index)) && (oldC.getMaxValue(index) > newC.getMaxValue(index)))
    }
}

/**
 * 修改[Bitmap]的[ColorSpace]设置为指定的[colorSpace]，如果[ColorSpace.getName]是[COLOR_SPACE_UNKNOWN]，则设置为[ColorSpace.Named.SRGB]，
 * 设置[ColorSpace]前会通过[canChangeColorSpace]判断是否可以设置色彩空间
 * @param colorSpace 指定色彩空间
 */
private fun Bitmap.changeColorSpace(colorSpace: ColorSpace?) {
    val realColorSpace = if ((colorSpace != null) && (colorSpace.name != COLOR_SPACE_UNKNOWN)) {
        colorSpace
    } else {
        ColorSpace.get(ColorSpace.Named.SRGB)
    }
    if (canChangeColorSpace(realColorSpace)) {
        setColorSpace(realColorSpace)
    }
}

/**
 * 当其的色彩空间[ColorSpace]为null或[ColorSpace.getName]是[COLOR_SPACE_UNKNOWN]时，修改[Bitmap]的[ColorSpace]为[ColorSpace.Named.SRGB]，反之不修改
 */
private fun Bitmap.setColorSpaceIfNeed() {
    val colorSpace = colorSpace
    if ((colorSpace == null) || (colorSpace.name == COLOR_SPACE_UNKNOWN)) {
        setColorSpace(ColorSpace.get(ColorSpace.Named.SRGB))
    }
}

/**
 * 将[Bitmap]对象转换为[BitmapShader]
 * @receiver Bitmap?
 * @param tileX TileMode
 * @param tileY TileMode
 * @return BitmapShader?
 */
fun Bitmap.toShader(tileX: Shader.TileMode = Shader.TileMode.CLAMP, tileY: Shader.TileMode = Shader.TileMode.CLAMP): BitmapShader? {
    if (isRecycled) return null
    return BitmapShader(this, tileX, tileY)
}

/**
 * 对[Bitmap]进行缩放，使用[Canvas]方式进行绘制
 * @param scale 缩放比例
 * @param recycle 执行操作后是否回收原Bitmap
 * @return 裁剪后的正方形图片
 */
fun Bitmap.scale1(scale: Float, recycle: Boolean = false): Bitmap? {
    if (isRecycled) return null
    setColorSpaceIfNeed()
    val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    val matrix = Matrix()
    val bitmap = Bitmap.createBitmap((width * scale).toInt(), (height * scale).toInt(), config!!).also {
        it.changeColorSpace(colorSpace)
    }
    matrix.setRectToRect(getRectF(), bitmap.getRectF(), Matrix.ScaleToFit.CENTER)
    Canvas(bitmap).drawBitmap(this, matrix, paint)
    if (ApiLevelUtil.isAtLeastAndroidU() && hasGainmap()) {
        val gainmap = gainmap!!
        val gainmapContent = gainmap.gainmapContents.scale1(scale, recycle)
        if (gainmapContent != null) {
            val outGainmap = Gainmap(gainmapContent).apply { copyInfo(gainmap) }
            bitmap.setGainmap(outGainmap)
        }
    }
    if (recycle) recycle()
    return bitmap
}

/**
 * 对Bitmap进行缩放，使用[Bitmap.createScaledBitmap]方式进行绘制
 * @param scale 缩放比例
 * @param recycle 执行操作后是否回收原[Bitmap]
 * @return 裁剪后的正方形图片
 */
fun Bitmap.scale2(scale: Float, filter: Boolean = false, recycle: Boolean = false): Bitmap? {
    if (isRecycled) return null
    setColorSpaceIfNeed()
    val bitmap = Bitmap.createScaledBitmap(this, (width * scale).toInt(), (height * scale).toInt(), filter).also {
        it.changeColorSpace(colorSpace)
    }
    if (ApiLevelUtil.isAtLeastAndroidU() && hasGainmap()) {
        val gainmap = gainmap!!
        val gainmapContent = gainmap.gainmapContents.scale2(scale, filter, recycle)
        if (gainmapContent != null) {
            val outGainmap = Gainmap(gainmapContent).apply { copyInfo(gainmap) }
            bitmap.setGainmap(outGainmap)
        }
    }
    if (recycle) recycle()
    return bitmap
}

/**
 * 对Bitmap进行缩放，通过指定[maxWidth]、[maxHeight]限制图片大小，使用[Canvas]进行绘制
 * @param maxWidth 最大宽度
 * @param maxHeight 最大高度
 * @param recycle 执行操作后是否回收原[Bitmap]
 * @return 裁剪后的正方形图片
 */
fun Bitmap.resize1(maxWidth: Int, maxHeight: Int, recycle: Boolean = false): Bitmap? {
    if (isRecycled) return null
    val scale: Float = min(maxWidth.toFloat() / width, maxHeight.toFloat() / height)
    return scale1(scale, recycle)
}

/**
 * 对Bitmap进行缩放，通过指定[maxWidth]、[maxHeight]限制图片大小，使用[Bitmap.createScaledBitmap]进行绘制
 * @param maxWidth 最大宽度
 * @param maxHeight 最大高度
 * @param recycle 执行操作后是否回收原Bitmap
 * @return 裁剪后的正方形图片
 */
fun Bitmap.resize2(maxWidth: Int, maxHeight: Int, filter: Boolean = false, recycle: Boolean = false): Bitmap? {
    if (isRecycled) return null
    val scale: Float = min(maxWidth.toFloat() / width, maxHeight.toFloat() / height)
    return scale2(scale, filter, recycle)
}

/**
 * 对[Bitmap]进行缩放并裁剪成正方形，裁剪后的图片变成为[sideSize]
 * @param sideSize 正方形的边长
 * @param recycle 执行操作后是否回收原[Bitmap]
 * @return 裁剪后的正方形图片
 */
fun Bitmap.resizeToSquare(sideSize: Int, recycle: Boolean = false): Bitmap? {
    if (isRecycled) return null
    setColorSpaceIfNeed()
    val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    val matrix = Matrix()
    val bitmap = Bitmap.createBitmap(sideSize, sideSize, config!!).also {
        it.changeColorSpace(colorSpace)
    }
    val scale = sideSize / min(width, height).toFloat()
    matrix.postScale(scale, scale)
    matrix.postTranslate((sideSize - width * scale) / 2, (sideSize - height * scale) / 2)
    Canvas(bitmap).drawBitmap(this, matrix, paint)
    if (ApiLevelUtil.isAtLeastAndroidU() && hasGainmap()) {
        val gainmap = gainmap!!
        val gainmapContent = gainmap.gainmapContents.resizeToSquare(sideSize, recycle)
        if (gainmapContent != null) {
            val outGainmap = Gainmap(gainmapContent).apply { copyInfo(gainmap) }
            bitmap.setGainmap(outGainmap)
        }
    }
    if (recycle) recycle()
    return bitmap
}

/**
 * 对图片进行裁剪，如果[rect]的范围超过了[Bitmap]的范围会返回null
 * @param rect 裁剪的范围
 * @param recycle 执行操作后是否回收原[Bitmap]
 * @return 裁剪后的图片
 */
fun Bitmap.crop(rect: Rect, recycle: Boolean = false): Bitmap? {
    if (isRecycled) return null
    setColorSpaceIfNeed()
    if (!getRectF().toRect().contains(rect)) return null
    val bitmap = runCatching {
        Bitmap.createBitmap(this, rect.left, rect.top, rect.width(), rect.height()).also {
            it.changeColorSpace(colorSpace)

            if (ApiLevelUtil.isAtLeastAndroidU() && hasGainmap()) {
                val gainmap = gainmap!!
                val gainmapContent = gainmap.gainmapContents.crop(rect, recycle)
                if (gainmapContent != null) {
                    val outGainmap = Gainmap(gainmapContent).apply { copyInfo(gainmap) }
                    it.setGainmap(outGainmap)
                }
            }
        }
    }.onFailure {
        Log.e(TAG, "crop: ", it)
    }.getOrNull()
    if (recycle) recycle()
    return bitmap
}

/**
 * 旋转图片，使用[Canvas]方式进行
 * @param angle 角度
 * @param recycle 执行操作后是否回收原Bitmap
 * @return 旋转后的Bitmap
 */
fun Bitmap.rotate1(angle: Int, recycle: Boolean = false): Bitmap? {
    if (isRecycled) return null
    setColorSpaceIfNeed()
    val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    val matrix = Matrix()
    val realAngle = ((angle % ANGLE_360 + ANGLE_360) % ANGLE_360)
    matrix.setRotate(realAngle.toFloat(), (width / 2).toFloat(), (height / 2).toFloat())
    val bitmap = if (realAngle % ANGLE_180 == 0) {
        createBitmap(width, height, config!!)
    } else {
        matrix.postTranslate((height - width).toFloat() / 2, (width - height).toFloat() / 2)
        createBitmap(height, width, config!!)
    }.also {
        it.changeColorSpace(colorSpace)
    }
    Canvas(bitmap).also {
        it.drawBitmap(this, matrix, paint)
    }

    if (ApiLevelUtil.isAtLeastAndroidU() && hasGainmap()) {
        val gainmap = gainmap!!
        val gainmapContent = gainmap.gainmapContents.rotate1(angle, recycle)
        if (gainmapContent != null) {
            val outGainmap = Gainmap(gainmapContent).apply { copyInfo(gainmap) }
            bitmap.setGainmap(outGainmap)
        }
    }
    if (recycle) recycle()
    return bitmap
}

/**
 * 旋转图片，使用[Bitmap.createBitmap]方式进行创建
 * @param angle 角度
 * @param recycle 执行操作后是否回收原Bitmap
 * @return 旋转后的Bitmap
 */
fun Bitmap.rotate2(angle: Int, recycle: Boolean = false): Bitmap? {
    if (isRecycled) return null
    setColorSpaceIfNeed()
    val matrix = Matrix()
    val realAngle = ((angle % ANGLE_360 + ANGLE_360) % ANGLE_360)
    matrix.setRotate(realAngle.toFloat(), (height / 2).toFloat(), (width / 2).toFloat())
    val bitmap = Bitmap.createBitmap(this, 0, 0, width, height, matrix, true).also {
        it.changeColorSpace(colorSpace)
    }

    if (ApiLevelUtil.isAtLeastAndroidU() && hasGainmap()) {
        val gainmap = gainmap!!
        val gainmapContent = gainmap.gainmapContents.rotate2(angle, recycle)
        if (gainmapContent != null) {
            val outGainmap = Gainmap(gainmapContent).apply { copyInfo(gainmap) }
            bitmap.setGainmap(outGainmap)
        }
    }
    if (recycle) recycle()
    return bitmap
}

@RequiresApi(Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
fun Gainmap.copyInfo(gainmap: Gainmap) {
    gainmap.ratioMin.also { setRatioMin(it[0], it[1], it[2]) }
    gainmap.ratioMax.also { setRatioMax(it[0], it[1], it[2]) }
    gainmap.gamma.also { setGamma(it[0], it[1], it[2]) }
    gainmap.epsilonSdr.also { setEpsilonSdr(it[0], it[1], it[2]) }
    gainmap.epsilonHdr.also { setEpsilonHdr(it[0], it[1], it[2]) }
    minDisplayRatioForHdrTransition = gainmap.minDisplayRatioForHdrTransition
    displayRatioForFullHdr = gainmap.displayRatioForFullHdr
}

/**
 * 获取Bitmap的宽高
 */
fun Bitmap.size(): Size {
    return Size(width, height)
}

/**
 * 获取像素的字节数占用
 */
fun Bitmap.byteSize(): Int {
    return config!!.byteSize()
}

/**
 * 获取像素的字节数占用
 */
@SuppressLint("NewApi")
fun Config.byteSize(): Int {
    return when {
        this == Config.HARDWARE -> BYTE_SIZE_INVALID
        this == Config.ALPHA_8 -> BYTE_SIZE_1
        this == Config.RGB_565 -> BYTE_SIZE_2
        this == Config.ARGB_8888 -> BYTE_SIZE_4
        this == Config.RGBA_1010102 -> BYTE_SIZE_4
        this == Config.RGBA_F16 -> BYTE_SIZE_8
        else -> BYTE_SIZE_INVALID
    }
}

val Bitmap.requireColorSpace
    get() = colorSpace ?: ColorSpace.get(ColorSpace.Named.SRGB)