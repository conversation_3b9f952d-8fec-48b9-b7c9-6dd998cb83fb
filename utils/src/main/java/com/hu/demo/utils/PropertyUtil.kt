package com.hu.demo.utils

import android.util.Log

object PropertyUtil {
    private const val TAG = "PropertyUtil"

    fun get(key: String, def: String? = null): String? {
        return runCatching {
            Runtime.getRuntime().exec("getprop $key").inputStream.bufferedReader().readLine()
        }.onFailure {
            Log.d(TAG, "get: ", it)
        }.getOrNull() ?: def
    }

    fun getInt(key: String, def: Int): Int {
        return get(key, def.toString())?.toIntOrNull() ?: def
    }
}