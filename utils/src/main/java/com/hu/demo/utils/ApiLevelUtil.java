/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: ApiLevelUtil.kt
 ** Description: android版本判断工具类
 **
 ** Version: 1.0
 ** Date:2022/04/18
 ** Author:<PERSON><PERSON><PERSON>@Apps.Gallery
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>@Apps.Gallery   	        2022/04/18   1.0         build this module
 ** <PERSON><PERSON><PERSON>@Apps.Gallery   	        2022/04/18   2.0         optimize
 ********************************************************************************/

package com.hu.demo.utils;

import android.os.Build;

import androidx.annotation.ChecksSdkIntAtLeast;

/**
 * Device-side compatibility utility class for reading device API level.
 */
public final class ApiLevelUtil {

    private ApiLevelUtil() {
    }

    public static int getApiLevel() {
        return Build.VERSION.SDK_INT;
    }

    public static boolean isAndroidS() {
        return Build.VERSION.SDK_INT == Build.VERSION_CODES.S;
    }

    public static boolean isAndroidR() {
        return Build.VERSION.SDK_INT == Build.VERSION_CODES.R;
    }

    @ChecksSdkIntAtLeast(api = Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
    public static boolean isAtLeastAndroidU() {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE;
    }

    @ChecksSdkIntAtLeast(api = Build.VERSION_CODES.TIRAMISU)
    public static boolean isAtLeastAndroidT() {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU;
    }

    @ChecksSdkIntAtLeast(api = Build.VERSION_CODES.S)
    public static boolean isAtLeastAndroidS() {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.S;
    }

    @ChecksSdkIntAtLeast(api = Build.VERSION_CODES.R)
    public static boolean isAtLeastAndroidR() {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.R;
    }

    public static boolean isAtMostAndroidS() {
        return Build.VERSION.SDK_INT <= Build.VERSION_CODES.S;
    }

    public static boolean isAtMostAndroidR() {
        return Build.VERSION.SDK_INT <= Build.VERSION_CODES.R;
    }

    public static boolean isApiLevelAtLeastV() {
        return Build.VERSION.SDK_INT > Build.VERSION_CODES.UPSIDE_DOWN_CAKE;
    }

    /**
     * 检查给定的firstAPILevel是否至少是Android U及以上版本
     *
     * @param firstAPILevel 项目的初始版本，即 出厂版本
     * @return 给定版本是否为Android U上的非升级项目
     */
    @ChecksSdkIntAtLeast(api = Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
    public static boolean isFirstApiLevelAtLeastU(int firstAPILevel) {
        return firstAPILevel >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE;
    }
}
