/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - StandardExt.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/04/26
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * hucanh<PERSON>@Apps.Gallery		2024/04/26		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/
package com.hu.demo.utils

import android.util.Log

/**
 * 如果[func]中方法返回值非null，则使用[func]的返回值，否则使用原值
 */
fun <T> T.takeUnlessOtherNotNull(func: (T) -> T?): T? {
    return this?.let(func) ?: this
}

/**
 * 当值与要比对的值相等时，执行[func]的逻辑
 *
 * @param compareValue 要比对的值
 * @param func lambda的逻辑回调
 */
inline fun <T> T.runIfEqual(compareValue: T, func: T.() -> Unit): T {
    if (this == compareValue) {
        func(this)
    }
    return this
}

/**
 * 获取对象本身，为空的时候打印日志
 */
inline fun <reified T> T.getOrLog(tag: String = "getOrLog", msg: String = ""): T? {
    return this ?: run {
        Log.w(tag, "getOrLog: $msg is null.")
        null
    }
}