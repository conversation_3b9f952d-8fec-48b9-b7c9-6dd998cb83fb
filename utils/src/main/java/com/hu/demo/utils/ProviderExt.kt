package com.hu.demo.utils

import android.content.ContentResolver
import android.net.Uri
import android.provider.MediaStore
import java.io.File


fun ContentResolver.getName(uri: Uri): String? {
    return query(uri, arrayOf(MediaStore.Files.FileColumns.DISPLAY_NAME), null, null)?.use {
        if (it.moveToNext()) {
            it.getString(0)
        } else null
    }
}

fun File.ensureFile(): File {
    parentFile?.mkdirs()
    createNewFile()
    return this
}