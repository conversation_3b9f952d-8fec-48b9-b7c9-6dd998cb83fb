package com.hu.demo.utils

import java.io.FilterOutputStream
import java.io.IOException
import java.io.OutputStream
import java.nio.ByteOrder

class ByteOrderedDataOutputStream(
    private val outputStream: OutputStream,
    private var byteOrder: ByteOrder = ByteOrder.BIG_ENDIAN
) : FilterOutputStream(outputStream) {
    fun setByteOrder(byteOrder: ByteOrder) {
        this.byteOrder = byteOrder
    }

    @Throws(IOException::class)
    override fun write(bytes: ByteArray) {
        outputStream.write(bytes)
    }

    @Throws(IOException::class)
    override fun write(bytes: ByteArray, offset: Int, length: Int) {
        outputStream.write(bytes, offset, length)
    }

    @Throws(IOException::class)
    fun writeByte(data: Int) {
        outputStream.write(data)
    }

    @Throws(IOException::class)
    fun writeShort(data: Short) {
        if (byteOrder == ByteOrder.LITTLE_ENDIAN) {
            outputStream.write(data.toInt() ushr 0 and 0xFF)
            outputStream.write(data.toInt() ushr 8 and 0xFF)
        } else if (byteOrder == ByteOrder.BIG_ENDIAN) {
            outputStream.write(data.toInt() ushr 8 and 0xFF)
            outputStream.write(data.toInt() ushr 0 and 0xFF)
        }
    }

    @Throws(IOException::class)
    fun writeInt(data: Int) {
        if (byteOrder == ByteOrder.LITTLE_ENDIAN) {
            outputStream.write(data ushr 0 and 0xFF)
            outputStream.write(data ushr 8 and 0xFF)
            outputStream.write(data ushr 16 and 0xFF)
            outputStream.write(data ushr 24 and 0xFF)
        } else if (byteOrder == ByteOrder.BIG_ENDIAN) {
            outputStream.write(data ushr 24 and 0xFF)
            outputStream.write(data ushr 16 and 0xFF)
            outputStream.write(data ushr 8 and 0xFF)
            outputStream.write(data ushr 0 and 0xFF)
        }
    }

    @Throws(IOException::class)
    fun writeUnsignedShort(data: Int) {
        writeShort(data.toShort())
    }

    @Throws(IOException::class)
    fun writeUnsignedInt(data: Long) {
        writeInt(data.toInt())
    }
}