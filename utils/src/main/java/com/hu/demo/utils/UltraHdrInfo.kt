package com.hu.demo.utils

import android.annotation.TargetApi
import android.graphics.Gainmap
import android.os.Build
import android.os.Parcel
import android.os.Parcelable

data class UltraHdrInfo(
    val ratioMin: FloatArray,
    val ratioMax: FloatArray,
    val gamma: FloatArray,
    val epsilonSdr: FloatArray,
    val epsilonHdr: FloatArray,
    val displayRatioSdr: Float,
    val displayRatioHdr: Float,
    val scale: Float,
    val baseImageType: Int = 1, // kHDR
    val type: Int = 5, // kHDRGM
) : Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.createFloatArray() ?: floatArrayOf(),
        parcel.createFloatArray() ?: floatArrayOf(),
        parcel.createFloatArray() ?: floatArrayOf(),
        parcel.createFloatArray() ?: floatArrayOf(),
        parcel.createFloatArray() ?: floatArrayOf(),
        parcel.readFloat(),
        parcel.readFloat(),
        parcel.readFloat(),
        parcel.readInt(),
        parcel.readInt()
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeFloatArray(ratioMin)
        parcel.writeFloatArray(ratioMax)
        parcel.writeFloatArray(gamma)
        parcel.writeFloatArray(epsilonSdr)
        parcel.writeFloatArray(epsilonHdr)
        parcel.writeFloat(displayRatioSdr)
        parcel.writeFloat(displayRatioHdr)
        parcel.writeFloat(scale)
        parcel.writeInt(baseImageType)
        parcel.writeInt(type)
    }

    override fun describeContents(): Int {
        return 0
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as UltraHdrInfo

        if (!ratioMin.contentEquals(other.ratioMin)) return false
        if (!ratioMax.contentEquals(other.ratioMax)) return false
        if (!gamma.contentEquals(other.gamma)) return false
        if (!epsilonSdr.contentEquals(other.epsilonSdr)) return false
        if (!epsilonHdr.contentEquals(other.epsilonHdr)) return false
        if (displayRatioSdr != other.displayRatioSdr) return false
        if (displayRatioHdr != other.displayRatioHdr) return false
        if (scale != other.scale) return false
        if (baseImageType != other.baseImageType) return false
        if (type != other.type) return false

        return true
    }

    override fun hashCode(): Int {
        var result = ratioMin.contentHashCode()
        result = 31 * result + ratioMax.contentHashCode()
        result = 31 * result + gamma.contentHashCode()
        result = 31 * result + epsilonSdr.contentHashCode()
        result = 31 * result + epsilonHdr.contentHashCode()
        result = 31 * result + displayRatioSdr.hashCode()
        result = 31 * result + displayRatioHdr.hashCode()
        result = 31 * result + scale.hashCode()
        result = 31 * result + baseImageType
        result = 31 * result + type
        return result
    }

    fun size(): Int {
        return ratioMin.size * Float.SIZE_BYTES +
            ratioMax.size * Float.SIZE_BYTES +
            gamma.size * Float.SIZE_BYTES +
            epsilonSdr.size * Float.SIZE_BYTES +
            epsilonHdr.size +
            Float.SIZE_BYTES +
            Float.SIZE_BYTES +
            Float.SIZE_BYTES +
            Float.SIZE_BYTES +
            Float.SIZE_BYTES
    }

    fun toDetailString(): String {
        return StringBuilder()
            .append("UltraHdrInfo:").appendLine()
            .append("ratioMin:${ratioMin.contentToString()}").appendLine()
            .append("ratioMax:${ratioMax.contentToString()},").appendLine()
            .append("gamma:${gamma.contentToString()},").appendLine()
            .append("epsilonSdr:${epsilonSdr.contentToString()},").appendLine()
            .append("epsilonHdr:${epsilonHdr.contentToString()},").appendLine()
            .append("displayRatioSdr:$displayRatioSdr,")
            .append("displayRatioHdr:$displayRatioHdr,")
            .append("scale:$scale,")
            .append("baseImageType:$baseImageType,")
            .append("type:$type,")
            .toString()
    }

    companion object CREATOR : Parcelable.Creator<UltraHdrInfo> {
        override fun createFromParcel(parcel: Parcel): UltraHdrInfo {
            return UltraHdrInfo(parcel)
        }

        override fun newArray(size: Int): Array<UltraHdrInfo?> {
            return arrayOfNulls(size)
        }
    }
}

@TargetApi(Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
fun Gainmap.toUltraHdrInfo(): UltraHdrInfo {
    return UltraHdrInfo(
        ratioMin,
        ratioMax,
        gamma,
        epsilonSdr,
        epsilonHdr,
        minDisplayRatioForHdrTransition,
        displayRatioForFullHdr,
        -1f,
    )
}