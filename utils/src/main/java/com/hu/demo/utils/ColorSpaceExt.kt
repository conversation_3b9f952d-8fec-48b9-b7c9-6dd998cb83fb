/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - ColorSpaceExt.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/09/27
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery		2024/09/27		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.utils

import android.graphics.ColorSpace
import android.graphics.ColorSpace.Named
import android.graphics.ColorSpace.Rgb
import android.os.Build
import androidx.annotation.RequiresApi

object ColorSpaceExt {
    /**
     * @see [Named.ADOBE_RGB]
     */
    @JvmStatic
    val ADOBE_SRGB by lazy { ColorSpace.get(Named.ADOBE_RGB) }

    /**
     * @see [Named.SRGB]
     */
    @JvmStatic
    val SRGB by lazy { ColorSpace.get(Named.SRGB) }

    /**
     * @see [Named.SRGB]
     */
    @JvmStatic
    val HDR_SRGB by lazy {
        val srgb = SRGB as Rgb
        Rgb("HDR sRGB", srgb.primaries, srgb.whitePoint, srgb.transferParameters!!)
    }

    /**
     * @see [Named.LINEAR_SRGB]
     */
    @JvmStatic
    val LINEAR_SRGB by lazy { ColorSpace.get(Named.LINEAR_SRGB) }

    /**
     * 颜色标准sRGB与gamma2.2组合的色域
     */
    @JvmStatic
    val SRGB_GAMMA_2_2 by lazy {
        val srgb = SRGB as Rgb
        Rgb("sRGB Gamma2.2", srgb.primaries, srgb.whitePoint, 2.2)
    }

    /**
     * 定义为HDR的颜色标准sRGB与gamma2.2组合的色域
     */
    @JvmStatic
    val HDR_SRGB_GAMMA_2_2 by lazy {
        val srgb = SRGB as Rgb
        Rgb("HDR sRGB Gamma2.2", srgb.primaries, srgb.whitePoint, 2.2)
    }

    /**
     * @see [Named.BT709]
     */
    @JvmStatic
    val BT709 by lazy { ColorSpace.get(Named.BT709) }

    /**
     * @see [Named.BT2020]
     */
    @JvmStatic
    val BT2020 by lazy { ColorSpace.get(Named.BT2020) }

    /**
     * @see [Named.DISPLAY_P3]
     */
    @JvmStatic
    val DISPLAY_P3 by lazy { ColorSpace.get(Named.DISPLAY_P3) }

    /**
     * @see [Named.DISPLAY_P3]
     */
    @JvmStatic
    val HDR_DISPLAY_P3 by lazy {
        val p3 = DISPLAY_P3 as Rgb
        Rgb("HDR Display-P3", p3.primaries, p3.whitePoint, p3.transferParameters!!)
    }

    /**
     * 线性Display-P3的色域
     */
    @JvmStatic
    val LINEAR_DISPLAY_P3 by lazy {
        val p3 = DISPLAY_P3 as Rgb
        Rgb("Linear Display-P3", p3.primaries, p3.whitePoint, 1.0)
    }

    /**
     * 颜色标准display-p3与gamma2.2组合的色域
     */
    @JvmStatic
    val DISPLAY_P3_GAMMA_2_2 by lazy {
        val p3 = DISPLAY_P3 as Rgb
        Rgb("Display-P3 Gamma2.2", p3.primaries, p3.whitePoint, 2.2)
    }

    /**
     * 定义为HDR的颜色标准display-p3与gamma2.2组合的色域
     */
    @JvmStatic
    val HDR_DISPLAY_P3_GAMMA_2_2 by lazy {
        val p3 = DISPLAY_P3 as Rgb
        Rgb("HDR Display-P3 Gamma2.2", p3.primaries, p3.whitePoint, 2.2)
    }

    /**
     * @see [Named.BT2020_HLG]
     */
    @JvmStatic
    val BT2020_HLG by lazy { if (ApiLevelUtil.isAtLeastAndroidU()) ColorSpace.get(Named.BT2020_HLG) else null }

    /**
     * 自定义的Display-P3 HLG的色域
     */
    @JvmStatic
    val DISPLAY_P3_HLG by lazy {
        if (ApiLevelUtil.isAtLeastAndroidU()) {
            val p3 = DISPLAY_P3 as Rgb
            // 从ColorSpase中copy过来的hlg的gamma参数
            val hlgParams = Rgb.TransferParameters(2.0, 2.0, 1 / 0.17883277, 0.28466892, 0.55991073, -0.685490157, -3.0)
            Rgb("Display-P3 HLG", p3.primaries, p3.whitePoint, hlgParams)
        } else null
    }

    /**
     * @see [Named.BT2020_PQ]
     */
    @JvmStatic
    val BT2020_PQ by lazy { if (ApiLevelUtil.isAtLeastAndroidU()) ColorSpace.get(Named.BT2020_PQ) else null }

    /**
     * 自定义的BT2020-Linear的色域
     */
    @JvmStatic
    val BT2020_LINEAR by lazy {
        if (ApiLevelUtil.isAtLeastAndroidU()) {
            val bt2020 = BT2020 as Rgb
            // 线性，所以gamma为1.0
            Rgb("BT2020 Linear", bt2020.primaries, bt2020.whitePoint, 1.0)
        } else null
    }

    /**
     * 自定义Display-P3 PQ的色域
     */
    @JvmStatic
    val DISPLAY_P3_PQ by lazy {
        if (ApiLevelUtil.isAtLeastAndroidU()) {
            val p3 = DISPLAY_P3 as Rgb
            // 从ColorSpase中copy过来的pq的gamma参数
            val pqParams = Rgb.TransferParameters(-1.555223, 1.860454, 32 / 2523.0, 2413 / 128.0, -2392 / 128.0, 8192 / 1305.0, -2.0)
            Rgb("DISPLAY-P3 PQ", p3.primaries, p3.whitePoint, pqParams)
        } else null
    }

    /**
     * 定义为HDR的色域
     */
    @JvmStatic
    private val HDR_GAMMAS by lazy {
        arrayOf(
            BT2020_HLG,
            DISPLAY_P3_HLG,
            BT2020_PQ,
            DISPLAY_P3_PQ,
            HDR_SRGB,
            HDR_DISPLAY_P3,
            HDR_SRGB_GAMMA_2_2,
            HDR_DISPLAY_P3_GAMMA_2_2
        )
    }

    /**
     * 定义为HLG的色域
     */
    private val HLG_GAMMAS by lazy {
        arrayOf(BT2020_HLG, DISPLAY_P3_HLG)
    }

    /**
     * 定义为SDR的色域
     */
    @JvmStatic
    private val SDR_GAMMAS by lazy {
        arrayOf(
            SRGB
        )
    }

    /**
     * 定义为srgb的色域
     */
    private val SRGB_COLOR_STANDARD by lazy {
        arrayOf(
            SRGB,
            SRGB_GAMMA_2_2,
            HDR_SRGB,
            HDR_SRGB_GAMMA_2_2,
            LINEAR_SRGB
        )
    }

    /**
     * 定义为P3的色域
     */
    private val DISPLAY_P3_COLOR_STANDARD by lazy {
        arrayOf(
            DISPLAY_P3,
            DISPLAY_P3_GAMMA_2_2,
            HDR_DISPLAY_P3,
            HDR_DISPLAY_P3_GAMMA_2_2,
            DISPLAY_P3_HLG,
            DISPLAY_P3_PQ,
            LINEAR_DISPLAY_P3
        )
    }

    /**
     * 判断是否是HDR gamma的色域
     */
    fun ColorSpace.isHdrGamma(): Boolean {
        return HDR_GAMMAS.find { it === this } != null
    }

    /**
     * 判断是否是HLG gamma的色域
     */
    fun ColorSpace.isHlgGamma(): Boolean {
        return HLG_GAMMAS.find { it === this } != null
    }

    /**
     * 将此色彩空间转换为线性
     *
     * @return 返回此色彩空间的线性色彩空间，如果没有定义值，则返回自身
     */
    fun ColorSpace.toLinear(): ColorSpace {
        return if (isDisplayP3ColorStandard()) {
            LINEAR_DISPLAY_P3
        } else if (isSrgbColorStandard()) {
            LINEAR_SRGB
        } else {
            this
        }
    }

    /**
     * 将此[ColorSpace]转换成HDR的[ColorSpace]，此目的仅方便用于判断是否要显示HDR。
     *
     * @return 返回转换为HDR的[ColorSpace]。如果没有对应的值则返回自身
     */
    fun ColorSpace.toHdr(): ColorSpace {
        return when (this) {
            DISPLAY_P3 -> HDR_DISPLAY_P3
            SRGB -> HDR_SRGB
            DISPLAY_P3_GAMMA_2_2 -> HDR_DISPLAY_P3_GAMMA_2_2
            SRGB_GAMMA_2_2 -> HDR_SRGB_GAMMA_2_2
            else -> this
        }
    }

    /**
     * 将此[ColorSpace]转换为曲线为Gamma2.2对应的色域
     *
     * **注意**：此方法仅适用与SDR对应的色域
     *
     * @return 返回转换为曲线为Gamma2.2对应的色域。
     */
    fun ColorSpace.toGamma2_2(): ColorSpace {
        return when (this) {
            DISPLAY_P3 -> DISPLAY_P3_GAMMA_2_2
            SRGB -> SRGB_GAMMA_2_2
            else -> this
        }
    }

    /**
     * 判断当前色域是否是srgb的
     */
    fun ColorSpace.isSrgbColorStandard(): Boolean {
        return SRGB_COLOR_STANDARD.find { it === this } != null
    }

    /**
     * 将当前色域转化为美摄色域名称
     */
    @RequiresApi(Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
    fun ColorSpace.getMeicamColorSpaceName(): String {
        return when (this) {
            SRGB -> Named.SRGB.toString()
            DISPLAY_P3 -> Named.DISPLAY_P3.toString()
            BT2020_HLG -> Named.BT2020_HLG.toString()
            BT2020_PQ -> Named.BT2020_PQ.toString()
            else -> Named.SRGB.toString()
        }
    }

    /**
     * 判断当前色域是否是Display-P3的
     */
    fun ColorSpace.isDisplayP3ColorStandard(): Boolean {
        return DISPLAY_P3_COLOR_STANDARD.find { it === this } != null
    }
}

/**
 * 获取gamma转换的参数，顺序按照：
 *
 * > [g] , [a] , [b] , [c] , [d] , [e] , [f]
 */
fun Rgb.TransferParameters.getTFParams(): FloatArray {
    return floatArrayOf(g.toFloat(), a.toFloat(), b.toFloat(), c.toFloat(), d.toFloat(), e.toFloat(), f.toFloat())
}