package com.hu.demo.utils

import android.util.Log
import okio.utf8Size
import java.io.ByteArrayInputStream
import java.io.DataInput
import java.io.DataInputStream
import java.io.EOFException
import java.io.IOException
import java.io.InputStream
import java.nio.ByteOrder

class ByteOrderedDataInputStream @JvmOverloads constructor(
    iStream: InputStream?,
    var byteOrder: ByteOrder = ByteOrder.BIG_ENDIAN,
) : InputStream(), DataInput {
    private val dataInputStream: DataInputStream = DataInputStream(iStream)
    private var position: Int = 0
    private var markPos = -1
    private var skipBuffer: ByteArray? = null

    constructor(
        bytes: ByteArray,
        offset: Int = 0,
        length: Int = bytes.size - offset,
        byteOrder: ByteOrder = ByteOrder.BIG_ENDIAN,
    ) : this(ByteArrayInputStream(bytes, offset, length), byteOrder)

    init {
        dataInputStream.mark(0)
    }

    fun position(): Int {
        return position
    }

    @Throws(IOException::class)
    override fun available(): Int {
        return dataInputStream.available()
    }

    @Throws(IOException::class)
    override fun read(): Int {
        ++position
        return dataInputStream.read()
    }

    @Throws(IOException::class)
    override fun read(b: ByteArray, off: Int, len: Int): Int {
        val bytesRead = dataInputStream.read(b, off, len)
        position += bytesRead
        return bytesRead
    }

    fun readBytes(size: Int): ByteArray {
        val array = ByteArray(size).apply { dataInputStream.read(this) }
        position += size
        return array
    }

    @Throws(IOException::class)
    override fun readUnsignedByte(): Int {
        ++position
        return dataInputStream.readUnsignedByte()
    }

    @Throws(IOException::class)
    override fun readLine(): String? {
        Log.d(TAG, "Currently unsupported")
        return null
    }

    @Throws(IOException::class)
    override fun readBoolean(): Boolean {
        ++position
        return dataInputStream.readBoolean()
    }

    @Throws(IOException::class)
    override fun readChar(): Char {
        position += 2
        return dataInputStream.readChar()
    }

    @Throws(IOException::class)
    override fun readUTF(): String {
        position += 2
        val utf = dataInputStream.readUTF()
        position += utf.utf8Size().toInt()
        return utf
    }

    @Throws(IOException::class)
    override fun readFully(buffer: ByteArray, offset: Int, length: Int) {
        position += length
        dataInputStream.readFully(buffer, offset, length)
    }

    @Throws(IOException::class)
    override fun readFully(buffer: ByteArray) {
        position += buffer.size
        dataInputStream.readFully(buffer)
    }

    @Throws(IOException::class)
    override fun readByte(): Byte {
        ++position
        val ch = dataInputStream.read()
        if (ch < 0) {
            throw EOFException()
        }
        return ch.toByte()
    }

    @Throws(IOException::class)
    override fun readShort(): Short {
        position += 2
        val ch1 = dataInputStream.read()
        val ch2 = dataInputStream.read()
        if (ch1 or ch2 < 0) {
            throw EOFException()
        }
        if (byteOrder == LITTLE_ENDIAN) {
            return ((ch2 shl 8) + ch1).toShort()
        } else if (byteOrder == BIG_ENDIAN) {
            return ((ch1 shl 8) + ch2).toShort()
        }
        throw IOException("Invalid byte order: $byteOrder")
    }

    @Throws(IOException::class)
    override fun readInt(): Int {
        position += 4
        val ch1 = dataInputStream.read()
        val ch2 = dataInputStream.read()
        val ch3 = dataInputStream.read()
        val ch4 = dataInputStream.read()
        if (ch1 or ch2 or ch3 or ch4 < 0) {
            throw EOFException()
        }
        if (byteOrder == LITTLE_ENDIAN) {
            return (ch4 shl 24) + (ch3 shl 16) + (ch2 shl 8) + ch1
        } else if (byteOrder == BIG_ENDIAN) {
            return (ch1 shl 24) + (ch2 shl 16) + (ch3 shl 8) + ch4
        }
        throw IOException("Invalid byte order: $byteOrder")
    }

    @Throws(IOException::class)
    override fun skipBytes(n: Int): Int {
        throw UnsupportedOperationException("skipBytes is currently unsupported")
    }


    fun skipFully(n: Long) {
        var size = n
        while (size > Int.MAX_VALUE) {
            skipFully(Int.MAX_VALUE)
            size -= Int.MAX_VALUE
        }
        skipFully(size.toInt())
    }

    /**
     * Discards n bytes of data from the input stream. This method will block until either
     * the full amount has been skipped or the end of the stream is reached, whichever happens
     * first.
     */
    @Throws(IOException::class)
    fun skipFully(n: Int) {
        var totalSkipped = 0
        while (totalSkipped < n) {
            var skipped = dataInputStream.skip((n - totalSkipped).toLong()).toInt()
            if (skipped <= 0) {
                if (skipBuffer == null) {
                    skipBuffer = ByteArray(SKIP_BUFFER_SIZE)
                }
                val bytesToSkip = Math.min(SKIP_BUFFER_SIZE, n - totalSkipped)
                if (dataInputStream.read(skipBuffer, 0, bytesToSkip).also { skipped = it } == -1) {
                    throw EOFException("Reached EOF while skipping $n bytes.")
                }
            }
            totalSkipped += skipped
        }
        position += totalSkipped
    }

    @Throws(IOException::class)
    override fun readUnsignedShort(): Int {
        position += 2
        val ch1 = dataInputStream.read()
        val ch2 = dataInputStream.read()
        if (ch1 or ch2 < 0) {
            throw EOFException()
        }
        if (byteOrder == LITTLE_ENDIAN) {
            return (ch2 shl 8) + ch1
        } else if (byteOrder == BIG_ENDIAN) {
            return (ch1 shl 8) + ch2
        }
        throw IOException("Invalid byte order: $byteOrder")
    }

    @Throws(IOException::class)
    fun readUnsignedInt(): Long {
        return readInt().toLong() and 0xffffffffL
    }

    @Throws(IOException::class)
    override fun readLong(): Long {
        position += 8
        val ch1 = dataInputStream.read()
        val ch2 = dataInputStream.read()
        val ch3 = dataInputStream.read()
        val ch4 = dataInputStream.read()
        val ch5 = dataInputStream.read()
        val ch6 = dataInputStream.read()
        val ch7 = dataInputStream.read()
        val ch8 = dataInputStream.read()
        if (ch1 or ch2 or ch3 or ch4 or ch5 or ch6 or ch7 or ch8 < 0) {
            throw EOFException()
        }
        if (byteOrder == LITTLE_ENDIAN) {
            return ((ch8.toLong() shl 56) +
                (ch7.toLong() shl 48) +
                (ch6.toLong() shl 40) +
                (ch5.toLong() shl 32) +
                (ch4.toLong() shl 24) +
                (ch3.toLong() shl 16) +
                (ch2.toLong() shl 8) +
                ch1.toLong())
        } else if (byteOrder == BIG_ENDIAN) {
            return ((ch1.toLong() shl 56) +
                (ch2.toLong() shl 48) +
                (ch3.toLong() shl 40) +
                (ch4.toLong() shl 32) +
                (ch5.toLong() shl 24) +
                (ch6.toLong() shl 16) +
                (ch7.toLong() shl 8) +
                ch8.toLong())
        }
        throw IOException("Invalid byte order: $byteOrder")
    }

    @Throws(IOException::class)
    override fun readFloat(): Float {
        return readInt().toFloat()
    }

    @Throws(IOException::class)
    override fun readDouble(): Double {
        return readLong().toDouble()
    }

    override fun mark(readlimit: Int) {
        markPos = position
        dataInputStream.mark(readlimit)
    }

    override fun reset() {
        if (position > markPos) {
            position = markPos
        }
        dataInputStream.reset()
    }

    companion object {
        private const val TAG = "ByteOrderedDataInputStr"
        private val LITTLE_ENDIAN = ByteOrder.LITTLE_ENDIAN
        private val BIG_ENDIAN = ByteOrder.BIG_ENDIAN
        private const val SKIP_BUFFER_SIZE = 8192
    }
}