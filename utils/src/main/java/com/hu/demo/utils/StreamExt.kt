package com.hu.demo.utils

import android.annotation.SuppressLint
import com.google.common.base.Ascii
import java.io.FileDescriptor
import java.io.IOException
import java.io.InputStream
import java.lang.reflect.Field
import java.nio.ByteOrder


private const val BYTE_ALIGN_II: Int = 0x49492A00 // II: Intel order

private const val BYTE_ALIGN_MM: Int = 0x4d4d002A // MM: Motorola order

fun <T : InputStream, O : Any> T.mark(readLimit: Int, func: T.() -> O): O {
    mark(readLimit)
    val rtn = func(this)
    reset()
    return rtn
}

fun InputStream.readUtf8(): String {
    val list = mutableListOf<Byte>()
    val byte = ByteArray(1)
    while (read(byte) != -1 && byte[0] != Ascii.NUL) {
        list.add(byte[0])
    }
    return list.toByteArray().decodeToString()
}

fun ByteOrderedDataInputStream.readByteOrder(): ByteOrder {
    return when (val endian = readInt()) {
        BYTE_ALIGN_II -> ByteOrder.LITTLE_ENDIAN
        BYTE_ALIGN_MM -> ByteOrder.BIG_ENDIAN
        else -> throw IOException("Invalid byte order: ${endian.toHexString()}")
    }
}

@SuppressLint("DiscouragedPrivateApi")
fun FileDescriptor.getFd(): Int {
    val field: Field = this.javaClass.getDeclaredField("descriptor")
    field.isAccessible = true
    return field.get(this) as Int
}