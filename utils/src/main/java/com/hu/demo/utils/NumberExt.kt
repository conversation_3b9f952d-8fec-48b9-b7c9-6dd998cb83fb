package com.hu.demo.utils


fun Byte.toHexString(): String {
    return Integer.toHexString(toInt() and 0xFF).uppercase()
}

fun Short.toHexString(): String {
    return Integer.toHexString(toInt() and 0xFFFF).uppercase()
}

fun Int.toHexString(): String {
    return Integer.toHexString(this).uppercase()
}

fun Long.toHexString(): String {
    return java.lang.Long.toHexString(this).uppercase()
}

fun UByte.toHexString(): String {
    return Integer.toHexString(toInt() and 0xFF).uppercase()
}

fun UShort.toHexString(): String {
    return Integer.toHexString(toInt() and 0xFFFF).uppercase()
}

fun UInt.toHexString(): String {
    return Integer.toHexString(toInt()).uppercase()
}

fun Byte.getSenior(): Byte {
    return ((toUByte().toInt() shr 4) and 0xF).toByte()
}

fun Byte.getLow(): Byte {
    return (toUByte().toInt() and 0xF).toByte()
}

fun Float.toNonZeroInt(): Int {
    return maxOf(this.toInt(), 1)
}