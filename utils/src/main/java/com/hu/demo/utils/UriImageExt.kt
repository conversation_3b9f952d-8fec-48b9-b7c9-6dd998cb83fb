package com.hu.demo.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.ImageDecoder
import android.media.MediaMetadataRetriever
import android.net.Uri
import android.util.Log
import android.widget.ImageView
import kotlinx.coroutines.*
import kotlin.math.max


private const val TAG = "UriImageExt"

@OptIn(DelicateCoroutinesApi::class)
fun ImageView.setImageUriAny(uri: Uri?, maxWh: Int = 600) {
    uri ?: return
    GlobalScope.launch(Dispatchers.Default) {
        val type = context.contentResolver.getType(uri)
        if (type?.startsWith("image/") == true) {
            withContext(Dispatchers.Main) {
                val bitmap = uri.decodeImage(context, maxWh)
                setImageBitmap(bitmap)
                Log.d(TAG, "setImageUriAny: ${bitmap.colorSpace}, <${bitmap.width},${bitmap.height}>")
            }
        } else if (type?.startsWith("video/") == true) {
            MediaMetadataRetriever().use {
                it.setDataSource(context, uri)
                it.getFrameAtTime(0).also { bitmap ->
                    withContext(Dispatchers.Main) {
                        setImageBitmap(bitmap)
                    }
                }
            }
        } else {
            Log.e(TAG, "setImageUri: type is null")
        }
    }
}

fun Uri.decodeImage(context: Context, maxWh: Int): Bitmap {
    val bitmap = ImageDecoder.decodeBitmap(ImageDecoder.createSource(context.contentResolver, this)) { decoder, info, source ->
        decoder.allocator = ImageDecoder.ALLOCATOR_SOFTWARE
        val realMaxWh = maxWh.coerceAtMost(max(info.size.width,info.size.height))
        val scale = max(info.size.width.toFloat() / realMaxWh, info.size.height.toFloat() / realMaxWh)
        decoder.setTargetSampleSize((scale.toInt() shr 1 shl 1).coerceAtLeast(1))
        decoder.setTargetSize((info.size.width / scale).toInt().coerceAtLeast(1), (info.size.height / scale).toInt().coerceAtLeast(1))
    }
    return bitmap
}