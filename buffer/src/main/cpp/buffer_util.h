#ifndef HUDEMO_BUFFER_UTIL_H
#define HUDEMO_BUFFER_UTIL_H

#include <android/bitmap.h>
#include <android/hardware_buffer.h>

const char *get_bitmap_config_name(uint32_t hardware_format);

uint32_t get_hw_pixel_byte_size(uint32_t hardware_format);

uint32_t get_bitmap_pixel_byte_size(uint32_t bitmap_format);

jobject create_bitmap(JNIEnv *env, uint32_t width, uint32_t height, const char *config_name, jobject color_space);

#endif //HUDEMO_BUFFER_UTIL_H
