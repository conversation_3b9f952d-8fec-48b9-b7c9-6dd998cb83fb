#include <string.h>
#include "buffer_util.h"
#include "DataDesc.h"
#include "log.h"

const char *get_bitmap_config_name(uint32_t hardware_format) {
    if (hardware_format == AHARDWAREBUFFER_FORMAT_R8G8B8A8_UNORM) {
        return "ARGB_8888";
    } else if (hardware_format == AHARDWAREBUFFER_FORMAT_R16G16B16A16_FLOAT) {
        return "RGBA_F16";
    } else if (hardware_format == AHARDWAREBUFFER_FORMAT_R10G10B10A2_UNORM) {
        return "RGBA_1010102";
    } else if (hardware_format == AHARDWAREBUFFER_FORMAT_R8_UNORM) {
        return "ALPHA_8";
    } else {
        return nullptr;
    }
}

uint32_t get_hw_pixel_byte_size(uint32_t hardware_format) {
    if (hardware_format == AHARDWAREBUFFER_FORMAT_R8G8B8A8_UNORM) {
        return 4;
    } else if (hardware_format == AHARDWAREBUFFER_FORMAT_R16G16B16A16_FLOAT) {
        return 8;
    } else if (hardware_format == AHARD<PERSON>REBUFFER_FORMAT_R10G10B10A2_UNORM) {
        return 4;
    } else if (hardware_format == AHARDWAREBUFFER_FORMAT_R8_UNORM) {
        return 1;
    } else {
        return 0;
    }
}

uint32_t get_bitmap_pixel_byte_size(uint32_t bitmap_format) {
    if (bitmap_format == ANDROID_BITMAP_FORMAT_RGBA_8888) {
        return 4;
    } else if (bitmap_format == ANDROID_BITMAP_FORMAT_RGBA_1010102) {
        return 4;
    } else if (bitmap_format == ANDROID_BITMAP_FORMAT_A_8) {
        return 1;
    } else if (bitmap_format == ANDROID_BITMAP_FORMAT_RGBA_F16) {
        return 8;
    } else {
        return 0;
    }
}

jobject create_bitmap(JNIEnv *env, uint32_t width, uint32_t height, const char *config_name, jobject color_space) {
    jclass bitmapClass = env->FindClass("android/graphics/Bitmap");
    jmethodID createBitmap = env->GetStaticMethodID(
        bitmapClass,
        "createBitmap",
        "(IILandroid/graphics/Bitmap$Config;ZLandroid/graphics/ColorSpace;)Landroid/graphics/Bitmap;");

    jclass configClass = env->FindClass("android/graphics/Bitmap$Config");
    jfieldID configField = env->GetStaticFieldID(configClass, config_name, "Landroid/graphics/Bitmap$Config;");
    jobject config = env->GetStaticObjectField(configClass, configField);

    jobject bitmap = env->CallStaticObjectMethod(
        bitmapClass,
        createBitmap,
        static_cast<jint>(width),
        static_cast<jint>(height),
        config,
        JNI_TRUE,
        color_space
    );

    return bitmap;
}

bool copy_to_bitmap(JNIEnv *env, void *data, DataDesc desc, jobject bitmap) {
    uint8_t *pixels;
    int lockResult = AndroidBitmap_lockPixels(env, bitmap, reinterpret_cast<void **>(&pixels));
    if (lockResult != ANDROID_BITMAP_RESULT_SUCCESS) {
        return false;
    }

    // 逐行拷贝，处理stride差异
    auto srcData = static_cast<uint8_t *>(data);
    auto dstData = pixels;
    auto size_per_pixel = desc.pixel_size;
    uint32_t row_byte_count = desc.width * size_per_pixel;
    uint32_t stride_byte_count = desc.stride * size_per_pixel;

    LOGD("row_byte_count: %i, stride_byte_count: %i, size_per_pixel: %i, width: %i,height: %i",
         row_byte_count, stride_byte_count, size_per_pixel, desc.width, desc.height);
    for (uint32_t y = 0; y < desc.height; y++) {
        memcpy(dstData + y * row_byte_count, srcData + y * stride_byte_count, row_byte_count);
    }
    AndroidBitmap_unlockPixels(env, bitmap);
    return true;
}