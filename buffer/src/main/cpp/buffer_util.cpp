#include "buffer_util.h"

const char *get_bitmap_config_name(uint32_t hardware_format) {
    if (hardware_format == AHARDWAREBUFFER_FORMAT_R8G8B8A8_UNORM) {
        return "ARGB_8888";
    } else if (hardware_format == AHARDWAREBUFFER_FORMAT_R16G16B16A16_FLOAT) {
        return "RGBA_F16";
    } else if (hardware_format == AHARDWAREBUFFER_FORMAT_R10G10B10A2_UNORM) {
        return "RGBA_1010102";
    } else if (hardware_format == AHARDWAREBUFFER_FORMAT_R8_UNORM) {
        return "ALPHA_8";
    } else {
        return nullptr;
    }
}

int get_hw_pixel_byte_size(uint32_t hardware_format) {
    if (hardware_format == AHARDWAREBUFFER_FORMAT_R8G8B8A8_UNORM) {
        return 4;
    } else if (hardware_format == AHARDWAREBUFFER_FORMAT_R16G16B16A16_FLOAT) {
        return 8;
    } else if (hardware_format == AHARDWAREBUFFER_FORMAT_R10G10B10A2_UNORM) {
        return 4;
    } else if (hardware_format == AHARDWAREBUFFER_FORMAT_R8_UNORM) {
        return 1;
    } else {
        return 0;
    }
}

int get_bitmap_pixel_byte_size(uint32_t bitmap_format) {
    if (bitmap_format == ANDROID_BITMAP_FORMAT_RGBA_8888) {
        return 4;
    } else if (bitmap_format == ANDROID_BITMAP_FORMAT_RGBA_1010102) {
        return 4;
    } else if (bitmap_format == ANDROID_BITMAP_FORMAT_A_8) {
        return 1;
    } else if (bitmap_format == ANDROID_BITMAP_FORMAT_RGBA_F16) {
        return 8;
    } else {
        return 0;
    }
}