#include <jni.h>
#include <unistd.h>
#include <string>
#include <android/hardware_buffer.h>
#include <android/hardware_buffer_jni.h>
#include <android/native_window.h>
#include <android/native_window_jni.h>
#include <android/bitmap.h>
#include "buffer_util.h"
#include "log.h"

jobject create_bitmap(JNIEnv *env, int width, int height, const char *config_name, jobject color_space) {
    jclass bitmapClass = env->FindClass("android/graphics/Bitmap");
    jmethodID createBitmap = env->GetStaticMethodID(
        bitmapClass,
        "createBitmap",
        "(IILandroid/graphics/Bitmap$Config;ZLandroid/graphics/ColorSpace;)Landroid/graphics/Bitmap;");

    jclass configClass = env->FindClass("android/graphics/Bitmap$Config");
    jfieldID configField = env->GetStaticFieldID(configClass, config_name, "Landroid/graphics/Bitmap$Config;");
    jobject config = env->GetStaticObjectField(configClass, configField);

    jobject bitmap = env->CallStaticObjectMethod(bitmapClass, createBitmap, width, height, config, JNI_TRUE, color_space);

    return bitmap;
}

void print_bytes(const uint8_t *data, uint32_t rowByteSize, uint32_t line) {
    // 打印前16个字节的数据（16进制）
    std::string hexStr = "Row " + std::to_string(line) + ": ";
    for (uint32_t i = 0; i < std::min(32u, rowByteSize); i++) {
        char hex[4];
        sprintf(hex, "%02X ", (data + line * rowByteSize)[i]);
        hexStr += hex;
    }
    LOGD("%s", hexStr.c_str());
}

extern "C"
JNIEXPORT jobject JNICALL
Java_com_hu_demo_buffer_Buffer_toBitmap(JNIEnv *env, jobject thiz, jobject buffer, jobject colorSpace) {
    auto *aBuffer = AHardwareBuffer_fromHardwareBuffer(env, buffer);
    if (!aBuffer) {
        return nullptr;
    }

    AHardwareBuffer_Desc desc = {0};
    AHardwareBuffer_describe(aBuffer, &desc);

    void *data;
    int result = AHardwareBuffer_lock(aBuffer, AHARDWAREBUFFER_USAGE_CPU_READ_OFTEN, -1, nullptr, &data);
    if (result != 0) {
        return nullptr;
    }

    const char *configName = get_bitmap_config_name(desc.format);
    if (!configName) {
        AHardwareBuffer_unlock(aBuffer, nullptr);
        return nullptr;
    }

    jobject bitmap = create_bitmap(env, static_cast<int32_t>(desc.width), static_cast<int32_t>(desc.height), configName, colorSpace);
    if (!bitmap || env->ExceptionCheck()) {
        AHardwareBuffer_unlock(aBuffer, nullptr);
        return nullptr;
    }

    uint8_t *pixels;
    int lockResult = AndroidBitmap_lockPixels(env, bitmap, reinterpret_cast<void **>(&pixels));
    if (lockResult != ANDROID_BITMAP_RESULT_SUCCESS) {
        AHardwareBuffer_unlock(aBuffer, nullptr);
        return nullptr;
    }

    // 逐行拷贝，处理stride差异
    auto srcData = static_cast<uint8_t *>(data);
    auto dstData = pixels;
    auto size_per_pixel = get_hw_pixel_byte_size(desc.format);
    uint32_t row_byte_count = desc.width * size_per_pixel;
    uint32_t stride_byte_count = desc.stride * size_per_pixel;
    LOGD("row_byte_count: %i, stride_byte_count: %i, size_per_pixel: %i, configName: %s,width: %i,height: %i",
         row_byte_count, stride_byte_count, size_per_pixel, configName, desc.width, desc.height);
    for (uint32_t y = 0; y < desc.height; y++) {
        memcpy(dstData + y * row_byte_count, srcData + y * stride_byte_count, row_byte_count);
    }

    AndroidBitmap_unlockPixels(env, bitmap);
    AHardwareBuffer_unlock(aBuffer, nullptr);

    return bitmap;
}

extern "C"
JNIEXPORT void JNICALL
Java_com_hu_demo_buffer_Buffer_toPixels(JNIEnv *env, jobject thiz, jobject bitmap, jint fd) {
    AndroidBitmapInfo info;
    AndroidBitmap_getInfo(env, bitmap, &info);

    auto bytesPerPixel = get_bitmap_pixel_byte_size(info.format);

    uint8_t *pixels;
    AndroidBitmap_lockPixels(env, bitmap, reinterpret_cast<void **>(&pixels));

    FILE *outputFile = fdopen(fd, "wb");
    fwrite(pixels, 1, info.width * info.height * bytesPerPixel, outputFile);
    fflush(outputFile);
    fclose(outputFile);

    AndroidBitmap_unlockPixels(env, bitmap);
}