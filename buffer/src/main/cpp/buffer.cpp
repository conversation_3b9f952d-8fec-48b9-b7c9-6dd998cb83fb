#include <jni.h>
#include <android/hardware_buffer_jni.h>
#include <stdio.h>
#include "BufferLocker.h"
#include "buffer_util.h"

extern "C"
JNIEXPORT jobject JNICALL
Java_com_hu_demo_buffer_Buffer_toBitmap(JNIEnv *env, jobject thiz, jobject buffer, jobject colorSpace) {
    auto *aBuffer = AHardwareBuffer_fromHardwareBuffer(env, buffer);
    if (!aBuffer) {
        return nullptr;
    }

    BufferLocker locker(aBuffer);

    AHardwareBuffer_Desc desc = {0};
    AHardwareBuffer_describe(aBuffer, &desc);

    void *data;
    int result = AHardwareBuffer_lock(aBuffer, AHARDWAREBUFFER_USAGE_CPU_READ_OFTEN, -1, nullptr, &data);
    if (result != 0) {
        return nullptr;
    }

    const char *configName = get_bitmap_config_name(desc.format);
    if (!configName) {
        return nullptr;
    }

    jobject bitmap = create_bitmap(env, static_cast<int32_t>(desc.width), static_cast<int32_t>(desc.height), configName, colorSpace);
    if (!bitmap || env->ExceptionCheck()) {
        return nullptr;
    }

    DataDesc dataDesc = {desc.width, desc.height, get_hw_pixel_byte_size(desc.format), desc.stride};

    copy_to_bitmap(env, data, dataDesc, bitmap);
    return bitmap;
}

extern "C"
JNIEXPORT jboolean JNICALL
Java_com_hu_demo_buffer_Buffer_writeToBitmap(JNIEnv *env, jobject thiz, jobject buffer, jobject bitmap) {
    auto *aBuffer = AHardwareBuffer_fromHardwareBuffer(env, buffer);
    if (!aBuffer) {
        return false;
    }

    BufferLocker locker(aBuffer);

    AHardwareBuffer_Desc desc = {0};
    AHardwareBuffer_describe(aBuffer, &desc);

    void *data;
    int result = AHardwareBuffer_lock(aBuffer, AHARDWAREBUFFER_USAGE_CPU_READ_OFTEN, -1, nullptr, &data);
    if (result != 0) {
        return false;
    }

    if (env->ExceptionCheck()) {
        return false;
    }

    DataDesc dataDesc = {desc.width, desc.height, get_hw_pixel_byte_size(desc.format), desc.stride};

    copy_to_bitmap(env, data, dataDesc, bitmap);
    return true;
}

extern "C"
JNIEXPORT void JNICALL
Java_com_hu_demo_buffer_Buffer_toPixels(JNIEnv *env, jobject thiz, jobject bitmap, jint fd) {
    AndroidBitmapInfo info;
    AndroidBitmap_getInfo(env, bitmap, &info);

    auto pixelSize = get_bitmap_pixel_byte_size(info.format);

    uint8_t *pixels;
    AndroidBitmap_lockPixels(env, bitmap, reinterpret_cast<void **>(&pixels));

    FILE *outputFile = fdopen(fd, "wb");
    fwrite(pixels, 1, info.width * info.height * pixelSize, outputFile);
    fflush(outputFile);
    fclose(outputFile);

    AndroidBitmap_unlockPixels(env, bitmap);
}