package com.hu.demo.buffer

import android.graphics.Bitmap
import android.graphics.ColorSpace
import android.hardware.HardwareBuffer

object Buffer {
    init {
        System.loadLibrary("buffer")
    }

    external fun toBitmap(buffer: HardwareBuffer, colorSpace: ColorSpace): Bitmap

    external fun toBitmap2(buffer: HardwareBuffer, bitmap: Bitmap): Boolean

    external fun toPixels(bitmap: Bitmap, fd: Int)
}