package com.hu.demo.base.ui

import android.content.res.Configuration
import android.os.Bundle
import android.util.Log
import android.view.MenuItem
import androidx.annotation.LayoutRes
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import com.hu.demo.base.R

abstract class BaseActivity : AppCompatActivity() {
    protected var toolbar: Toolbar? = null
    protected val tag = javaClass.simpleName
    final override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(getLayoutId())
        Log.i(tag, "onCreate: ")
        toolbar = findViewById(R.id.toolbar)
        toolbar?.apply {
            setSupportActionBar(this)
        }
        initToolbar()
        ViewCompat.setOnApplyWindowInsetsListener(window.decorView) { v, insets ->
            WindowCompat.getInsetsController(window,v).isAppearanceLightStatusBars = true
            v.setPadding(0, insets.getInsets(WindowInsetsCompat.Type.statusBars()).top, 0, insets.getInsets(WindowInsetsCompat.Type.navigationBars()).bottom)
            ViewCompat.onApplyWindowInsets(v, insets)
        }
        initData(savedInstanceState)
        initView(savedInstanceState)
        initEvent(savedInstanceState)
    }

    @LayoutRes
    abstract fun getLayoutId(): Int

    open fun initToolbar() = Unit

    open fun initView(savedInstanceState: Bundle?) = Unit

    open fun initData(savedInstanceState: Bundle?) = Unit

    open fun initEvent(savedInstanceState: Bundle?) = Unit
    override fun onRestart() {
        super.onRestart()
        Log.d(tag, "onRestart: ")
    }

    override fun onStart() {
        super.onStart()
        Log.i(tag, "onStart: ")
    }

    override fun onResume() {
        super.onResume()
        Log.i(tag, "onResume: ")
    }

    override fun onPause() {
        super.onPause()
        Log.i(tag, "onPause: ")
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        Log.i(tag, "onSaveInstanceState: ")
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        Log.i(tag, "onRestoreInstanceState: ")
    }

    override fun onStop() {
        super.onStop()
        Log.i(tag, "onStop: ")
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        Log.i(tag, "onConfigurationChanged: $newConfig")
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.i(tag, "onDestroy: ")
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                true
            }

            else -> {
                super.onOptionsItemSelected(item)
            }
        }
    }
}