package com.hu.demo.base.provider

import android.content.Context
import android.net.Uri
import android.os.CancellationSignal
import android.os.ParcelFileDescriptor
import android.util.Log
import androidx.core.content.FileProvider
import com.hu.demo.base.app.App
import java.io.File
import java.util.*
import java.util.concurrent.locks.ReentrantReadWriteLock

class DFileProvider : FileProvider() {
    override fun openFile(uri: Uri, mode: String, signal: CancellationSignal?): ParcelFileDescriptor? {
        val lock = fileLocks[uri]?.readLock()
        try {
            lock?.lock()
            return super.openFile(uri, mode, signal)
        } catch (e: Exception) {
            Log.e(TAG, "openFile: ", e)
        } finally {
            lock?.unlock()
        }
        return null
    }

    companion object {
        const val AUTHORITY = "com.hu.demo.fileprovider"
        private const val TAG = "DFileProvider"

        val cacheFile by lazy { File(App.app.externalCacheDir, "cache") }

        val fileLocks = Collections.synchronizedMap(WeakHashMap<Uri, ReentrantReadWriteLock>())

        fun getUriForFile(context: Context, file: File): Uri {
            return getUriForFile(context, AUTHORITY, file)
        }
    }
}