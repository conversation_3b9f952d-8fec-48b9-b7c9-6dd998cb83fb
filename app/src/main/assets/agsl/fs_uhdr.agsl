struct TfParams {
    float a, b, c, d, e, f, g;
};

const TfParams srgb = TfParams(1.0 / 1.055, 0.055 / 1.055, 1.0 / 12.92, 0.04045, 0.0, 0.0, 2.4);

uniform shader uTex;
uniform vec2 uTexSize;

uniform shader uGainmap;
uniform vec2 uGainmapSize;

uniform vec2 uResolution;

uniform vec3 uLogRatioMin;
uniform vec3 uLogRatioMax;
uniform vec3 uGainmapGamma;
uniform vec3 uEpsilonSdr;
uniform vec3 uEpsilonHdr;
uniform float uLogDeviceHdrSdrRatio;
uniform float uLogDisplaySdrRatio;
uniform float uLogDisplayHdrRatio;
uniform int uSingleChannel;
uniform int uUseCombineUhdr;

vec3 srgbEotf(vec3 color) {
    vec3 s = sign(color);
    color = abs(color);
    return s * mix(srgb.c * color + srgb.f, pow(srgb.a * color + srgb.b, vec3(srgb.g)) + srgb.e, step(srgb.d, color));
}

vec3 srgbOetf(vec3 color) {
    vec3 s = sign(color);
    color = abs(color);
    return s * mix((color - srgb.f) / srgb.c, (pow(color - srgb.e, vec3(1.0 / srgb.g)) - srgb.b) / srgb.a, step(srgb.d * srgb.c, color));
}

vec4 main(vec2 coords) {
    vec2 uv = coords / uResolution;

    vec4 sdrColor = uTex.eval(uv * uTexSize);

    sdrColor.rgb = srgbEotf(sdrColor.rgb);

    vec4 gainColor = uGainmap.eval(uv * uGainmapSize);

    vec3 H;

    if (uSingleChannel == 1) {
        gainColor = vec4(gainColor.rrr, 1.0);
    }

    vec3 L = mix(uLogRatioMin, uLogRatioMax, pow(gainColor.rgb, uGainmapGamma));

    float targetRatio = uLogDeviceHdrSdrRatio - uLogDisplaySdrRatio;
    float maxRatio = uLogDisplayHdrRatio - uLogDisplaySdrRatio;
    float W = clamp(targetRatio / maxRatio, 0.0, 1.0);
    H = (sdrColor.rgb + uEpsilonSdr) * exp(L * W) - uEpsilonHdr;

    // 归一化
    H *= 1.0 / exp(W * maxRatio);

    vec4 result = vec4(H, sdrColor.a);

    // 转为非线性
    result.rgb = srgbOetf(result.rgb);
    return result;
}