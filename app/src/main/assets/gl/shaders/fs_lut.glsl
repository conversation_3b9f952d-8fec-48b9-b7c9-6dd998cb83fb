#version 300 es
#extension GL_EXT_YUV_target: require
#extension GL_OES_texture_3D: enable
precision mediump float;
precision mediump sampler3D;

uniform sampler2D uTex;

uniform sampler3D uLut3dTex;
uniform float uLutSize;

in vec2 vTexCoord;
out vec4 FragColor;

vec3 applyLut(vec3 color, sampler3D lut, float lutSize) {
    vec3 scale = vec3((lutSize - 1.0f) / lutSize);
    vec3 offset = vec3(1.0f / (2.0f * lutSize));
    color.rgb = texture(lut, scale * color.rgb + offset).rgb;
    return color;
}

void main() {
    vec4 color = texture(uTex, vTexCoord);

    color.rgb = applyLut(color.rgb, uLut3dTex, uLutSize);
}