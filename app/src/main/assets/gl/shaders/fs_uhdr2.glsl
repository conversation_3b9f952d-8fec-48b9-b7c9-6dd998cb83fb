#version 300 es
precision mediump float;
in vec2 vTexCoord;

uniform highp float uSrcTF[7];
uniform highp mat3 uGamutTransform;
uniform highp float uDestTF[7];
uniform sampler2D uTex;
uniform sampler2D uGainmap;
uniform mediump vec3 uLogRatioMin;
uniform mediump vec3 uLogRatioMax;
uniform mediump vec3 uGainmapGamma;
uniform mediump vec3 uEpsilonSdr;
uniform mediump vec3 uEpsilonHdr;
uniform mediump float uW;
uniform highp int uGainmapIsAlpha;
uniform highp int uSingleChannel;
uniform highp int uNoGamma;

out vec4 FragColor;

highp float fromSrc(highp float x) {
    highp float G = uSrcTF[0];
    highp float A = uSrcTF[1];
    highp float B = uSrcTF[2];
    highp float C = uSrcTF[3];
    highp float D = uSrcTF[4];
    highp float E = uSrcTF[5];
    highp float F = uSrcTF[6];
    highp float s = sign(x);
    x = abs(x);
    x = x < D ? C * x + F : pow(A * x + B, G) + E;
    return s * x;
}

highp float toDest(highp float x) {
    highp float G = uDestTF[0];
    highp float A = uDestTF[1];
    highp float B = uDestTF[2];
    highp float C = uDestTF[3];
    highp float D = uDestTF[4];
    highp float E = uDestTF[5];
    highp float F = uDestTF[6];
    highp float s = sign(x);
    x = abs(x);
    x = x < D ? C * x + F : pow(A * x + B, G) + E;
    return s * x;
}

highp vec4 sampleBase(vec2 coord) {
    vec4 color = texture(uTex, vTexCoord);
    color = vec4(color.xyz / max(color.w, 0.0001), color.w);
    color.x = fromSrc(color.x);
    color.y = fromSrc(color.y);
    color.z = fromSrc(color.z);
    color.xyz *= color.w;
    return color;
}

void main() {
    vec4 S = sampleBase(vTexCoord);
    vec4 G = texture(uGainmap, vTexCoord);
    vec3 H;

    if (uGainmapIsAlpha == 1) {
        G = vec4(G.w, G.w, G.w, 1.0);
        mediump float L;

        if (uNoGamma == 1) {
            L = mix(uLogRatioMin.x, uLogRatioMax.x, G.x);
        } else {
            L = mix(uLogRatioMin.x, uLogRatioMax.x, pow(G.x, uGainmapGamma.x));
        }

        H = (S.xyz + uEpsilonSdr) * exp(L * uW) - uEpsilonHdr;
    } else {
        mediump vec3 L;
        if (uNoGamma == 1) {
            L = mix(uLogRatioMin, uLogRatioMax, G.xyz);
        } else {
            L = mix(uLogRatioMin, uLogRatioMax, pow(G.xyz, uGainmapGamma));
        }

        H = (S.xyz + uEpsilonSdr) * exp(L * uW) - uEpsilonHdr;
    }

    vec4 result = vec4(H.xyz / max(S.w, 0.0001), S.w);
    result.rgb = (uGamutTransform * result.rgb);
    result.x = toDest(result.x);
    result.y = toDest(result.y);
    result.z = toDest(result.z);
    result.xyz *= result.w;
    FragColor = result;
}