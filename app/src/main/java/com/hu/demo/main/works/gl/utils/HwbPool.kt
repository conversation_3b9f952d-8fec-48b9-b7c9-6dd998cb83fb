/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - HwbPool.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2025/04/16
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery		2025/04/16		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.utils

import android.hardware.HardwareBuffer
import android.hardware.HardwareBuffer.USAGE_GPU_COLOR_OUTPUT
import android.hardware.HardwareBuffer.USAGE_GPU_SAMPLED_IMAGE
import androidx.annotation.IntRange
import androidx.hardware.HardwareBufferFormat
import androidx.hardware.HardwareBufferUsage
import com.hu.demo.main.works.gl.IRecyclable

/**
 * 创建后期[HardwareBuffer]的key
 *
 * @param width 宽度
 * @param height 高度
 * @param format 每个像素的格式
 * @param layers - 缓冲区中的层数
 * @param usage - 标志描述如何使用缓冲区
 */
data class HwbKey(
    val width: Int,
    val height: Int,
    @HardwareBufferFormat val format: Int,
    @IntRange(from = 1) val layers: Int = 1,
    @HardwareBufferUsage val usage: Long = USAGE_GPU_SAMPLED_IMAGE or USAGE_GPU_COLOR_OUTPUT,
) {
    /**
     * 判断当前key 是否支持对应HardwareBuffer的创建
     * 发现在某些机型上，不支持对灰度图 0x38 这个format的 HardwareBuffer 创建
     */
    fun isSupported(): Boolean {
        return HardwareBuffer.isSupported(width, height, format, layers, usage)
    }
}

/**
 * 缓存池中存储的封装对象
 *
 * @param buffer [HardwareBuffer]对象
 */
internal data class HwbValue(val buffer: HardwareBuffer) : IRecyclable {
    override fun recycle() {
        buffer.close()
    }
}

/**
 * [HardwareBuffer]的缓存池
 *
 * @param pool 实际的缓存池
 */
enum class HwbPool(private val pool: KeyCachePool<HwbKey, HwbValue>) {
    /**
     * 默认的缓存池
     */
    DEFAULT(KeyCachePool(0, { HwbValue(HardwareBuffer.create(it.width, it.height, it.format, it.layers, it.usage)) }));

    /**
     * 获取复用的[HardwareBuffer]
     *
     * @param key 申请Buffer对应的[HwbKey]
     *
     * @return 返回可以复用的[HardwareBuffer]对象
     */
    fun obtain(key: HwbKey): HardwareBuffer? {
        return if (key.isSupported()) pool.obtain(key).buffer else null
    }

    /**
     * 回收[HardwareBuffer]
     *
     * @param buffer 需要回收的[HardwareBuffer]
     */
    fun reuse(buffer: HardwareBuffer) {
        val key = HwbKey(buffer.width, buffer.height, buffer.format, buffer.layers, buffer.usage)
        val value = HwbValue(buffer)
        pool.reuse(key, value)
    }

    /**
     * 清空缓存
     */
    fun clean() {
        pool.clean()
    }
}