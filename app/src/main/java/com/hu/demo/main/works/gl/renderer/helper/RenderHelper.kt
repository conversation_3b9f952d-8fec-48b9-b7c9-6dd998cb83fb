/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - RenderHelper.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2025/01/09
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery		2025/01/09		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.renderer.helper

import android.graphics.ColorSpace
import android.os.Build
import androidx.annotation.RequiresApi
import com.hu.demo.main.works.gl.canvas.GlCanvas
import com.hu.demo.main.works.gl.renderer.image.UhdrRenderer
import com.hu.demo.main.works.gl.renderer.normal.GamutRenderer
import com.hu.demo.main.works.gl.renderer.normal.Renderer
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_COLOR_SPACE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_DEVICE_HDR_SDR_RATIO
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_GAINMAP_TEXTURE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_IMAGE_TEXTURE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_OUTPUT_TEXTURE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_TEXTURE_POOL
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.getKey
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.NEXT_NP
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.STABLE_NP
import com.hu.demo.main.works.gl.renderer.param.RenderArgs
import com.hu.demo.main.works.gl.texture.IGainTexture
import com.hu.demo.main.works.gl.texture.ITexture
import com.hu.demo.main.works.gl.texture.RealTexture
import com.hu.demo.main.works.gl.utils.BufferDataBinder
import com.hu.demo.main.works.gl.utils.GLThread
import com.hu.demo.main.works.gl.utils.GlProgram

object RenderHelper {
    private const val TAG = "RenderHelper"

    /**
     * 获取渲染器参数，并填充必要的数据
     *
     * @return 返回渲染器参数集
     */
    @GLThread
    fun obtainArgs(): RenderArgs {
        val renderArgs = RenderArgs.obtain()
        renderArgs[STABLE_NP.getKey(KEY_TEXTURE_POOL)] = mutableSetOf<ITexture>()

        return renderArgs
    }

    /**
     * 执行色域转换。算法要求的色域是P3+HLG，而美摄的输出为Bt709+sRGB
     *
     * @param inputTexture 输入的纹理
     * @param outputTexture 输出的纹理，如果没有设定，则在Renderer中创建新的纹理
     *
     * @return 色域转换后的纹理
     */
    @GLThread
    fun transformColorSpace(renderArgs: RenderArgs, inputTexture: ITexture, colorSpace: ColorSpace, outputTexture: ITexture? = null): ITexture {
        renderArgs.remove(NEXT_NP)
        val renderer = Renderer.get(GamutRenderer::class.java)
        renderArgs[NEXT_NP.getKey(KEY_IMAGE_TEXTURE)] = inputTexture
        renderArgs[NEXT_NP.getKey(KEY_COLOR_SPACE)] = colorSpace
        renderArgs[NEXT_NP.getKey(KEY_OUTPUT_TEXTURE)] = outputTexture
        renderer.performRender(renderArgs)
        val gamutTexture = renderArgs.require<ITexture>(NEXT_NP.getKey(KEY_IMAGE_TEXTURE))
        return gamutTexture
    }

    /**
     * 执行合成UHDR
     *
     * @param inputTexture 输入的主图
     * @param inputGainTexture 输入的增益图
     * @param hdrSdrRatio 提亮倍数
     *
     * @return 返回合成后的hdr线性纹理
     */
    @GLThread
    @RequiresApi(Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
    fun combineHdr(renderArgs: RenderArgs, inputTexture: ITexture, inputGainTexture: IGainTexture, hdrSdrRatio: Float): ITexture {
        renderArgs.remove(NEXT_NP)
        val renderer = Renderer.get(UhdrRenderer::class.java)
        renderArgs[STABLE_NP.getKey(KEY_DEVICE_HDR_SDR_RATIO)] = hdrSdrRatio

        renderArgs[NEXT_NP.getKey(KEY_IMAGE_TEXTURE)] = inputTexture
        renderArgs[NEXT_NP.getKey(KEY_GAINMAP_TEXTURE)] = inputGainTexture
        renderer.performRender(renderArgs)

        return renderArgs.require(NEXT_NP.getKey(KEY_IMAGE_TEXTURE))
    }

    /**
     * 回收当前GL线程相关内存
     *
     * @param renderArgs 渲染器参数
     */
    @GLThread
    fun release(renderArgs: RenderArgs) {
        recycleTexture(renderArgs)
        renderArgs.recycle()
        Renderer.clean()
        GlCanvas.clean()
        GlProgram.clean()
        BufferDataBinder.clean()
        RealTexture.clean()
    }

    /**
     * 对上次渲染请求过程中的纹理执行复用
     *
     * @param renderArgs 渲染参数集
     */
    @GLThread
    fun recycleTexture(renderArgs: RenderArgs) {
        renderArgs.require<MutableSet<ITexture>>(STABLE_NP.getKey(KEY_TEXTURE_POOL)).removeAll {
            it.recycle()
            true
        }
    }
}