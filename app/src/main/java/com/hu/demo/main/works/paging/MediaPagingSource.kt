package com.hu.demo.main.works.paging

import android.content.ContentResolver
import android.provider.MediaStore
import androidx.core.database.getStringOrNull
import androidx.core.os.bundleOf
import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.hu.demo.main.works.paging.data.MediaData
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.Date

class MediaPagingSource(private val provider: ContentResolver) : PagingSource<Int, MediaData>() {
    override fun getRefreshKey(state: PagingState<Int, MediaData>): Int? {
        return state.anchorPosition?.let { anchorPosition ->
            val anchorPage = state.closestPageToPosition(anchorPosition)
            anchorPage?.prevKey?.plus(1) ?: anchorPage?.nextKey?.minus(1)
        }
    }

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, MediaData> {
        val start = params.key ?: 0
        val count = params.loadSize

        val items = reloadItems(start * count, count)
        val prevKey = if (start > 0) start.minus(1) else null
        val nextKey = if (items.size == count) start.plus(1) else null
        return LoadResult.Page(
            data = items,
            prevKey = prevKey,
            nextKey = nextKey
        )
    }


    private fun reloadItems(start: Int, count: Int): List<MediaData> {
        return provider.query(
            MediaStore.Files.getContentUri(MediaStore.VOLUME_EXTERNAL_PRIMARY),
            null,
            bundleOf(
                ContentResolver.QUERY_ARG_SQL_SORT_ORDER to "${MediaStore.Files.FileColumns.DATE_ADDED} DESC",
                ContentResolver.QUERY_ARG_SQL_LIMIT to "$start,$count"
            ),
            null
        )?.use {
            val pattern = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss")
            buildList {
                while (it.moveToNext()) {
                    add(
                        MediaData(
                            it.getLong(it.getColumnIndexOrThrow(MediaStore.Files.FileColumns._ID)),
                            it.getStringOrNull(it.getColumnIndexOrThrow(MediaStore.Files.FileColumns.VOLUME_NAME)),
                            it.getStringOrNull(it.getColumnIndexOrThrow(MediaStore.Files.FileColumns.DISPLAY_NAME)),
                            it.getStringOrNull(it.getColumnIndexOrThrow(MediaStore.Files.FileColumns.MEDIA_TYPE)),
                            pattern.format(
                                LocalDateTime.ofInstant(
                                    Date(
                                        it.getStringOrNull(it.getColumnIndexOrThrow(MediaStore.Files.FileColumns.DATE_TAKEN))?.toLongOrNull() ?: 0
                                    ).toInstant(),
                                    ZoneId.systemDefault()
                                )
                            )
                        )
                    )
                }
            }
        } ?: emptyList()
    }

    fun getCount(): Int {
        return provider.query(
            MediaStore.Files.getContentUri(MediaStore.VOLUME_EXTERNAL),
            arrayOf("count(*)"),
            null,
            null
        )?.use {
            if (!it.moveToNext()) {
                it.getInt(0)
            } else null
        } ?: 0
    }

    companion object {
        private const val TAG = "MediaPagingSource"
    }
}