/*
 * *************************************************************
 * Copyright (C), 2010-2024, Oplus. All rights reserved.
 * VENDOR_EDIT
 * File: PDFCreate.kt
 * Description: create pdf .
 * Version: 1.0
 * Date: 2024/1/17
 * Author: 80242265
 * ---------------------Revision History: ---------------------
 * <author>     <data>      <version >      <desc>
 * yangbin     2024/1/17      1.0          build this file
 * *************************************************************
 */
package com.hu.demo.main.works.pdf

import android.content.Context
import android.graphics.Bitmap
import android.graphics.ImageDecoder
import android.graphics.Paint
import android.graphics.pdf.PdfDocument
import android.graphics.pdf.PdfDocument.Page
import android.net.Uri
import android.os.Build
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import kotlin.math.min


object PdfCreator {
    private const val TAG = "PdfCreator"
    const val DEFAULT_A4_WIDTH = 2000
    const val DEFAULT_A4_HEIGHT = 2000
    private const val DEFAULT_SPACING = 20
    private const val PDF_SUFFIX = ".pdf"

    suspend fun savePdfToExternal(context: Context, uriList: List<Uri>, saveDir: File, callback: (String) -> Unit): File? {
        return withContext(Dispatchers.IO) {
            saveDir.mkdirs()
            val pdfFile = File(saveDir, "${System.currentTimeMillis()}$PDF_SUFFIX")
            if (uriList.isEmpty()) {
                Log.e(TAG, "savePdfToExternal: bitmap list is empty.")
                return@withContext null
            }
            val document = PdfDocument()
            var page: Page? = null
            runCatching {
                var pageNumber = 1
                val paint = Paint(Paint.ANTI_ALIAS_FLAG)

                var index = 0
                var cacheBitmap: Bitmap? = null
                Log.d(TAG, "savePdfToExternal: start write. total ${uriList.size}.")
                val fos = FileOutputStream(pdfFile)
                while (index < uriList.size || cacheBitmap != null) {
                    Log.d(TAG, "savePdfToExternal: write page $pageNumber .")
                    val pageInfo = PdfDocument.PageInfo.Builder(DEFAULT_A4_WIDTH, DEFAULT_A4_HEIGHT, pageNumber++).create()
                    page = document.startPage(pageInfo)
                    val canvas = page!!.canvas
                    var remainHeight = canvas.height
                    while (index < uriList.size || cacheBitmap != null) {
                        if (cacheBitmap == null) {
                            Log.d(TAG, "savePdfToExternal: write bitmap $index .")
                            cacheBitmap = createToBitmap(context, uriList[index++])
                        }
                        if (remainHeight >= cacheBitmap.height) {
                            callback("$index/${uriList.size}")
                            canvas.drawBitmap(cacheBitmap, (canvas.width - cacheBitmap.width) / 2.0f, 0f, paint)
                            remainHeight = remainHeight - cacheBitmap.height - DEFAULT_SPACING
                            cacheBitmap = null
                        } else break
                    }
                    document.finishPage(page)
                }
                callback("写入文件")
                document.writeTo(fos)
                Log.d(TAG, "savePdfToExternal: end write. total.")
            }.onFailure {
                Log.e(TAG, "savePdfToExternal save error", it)
                document.finishPage(page)
            }
            document.close()
            pdfFile
        }
    }

    private fun createToBitmap(context: Context, uri: Uri): Bitmap {
        return ImageDecoder.decodeBitmap(ImageDecoder.createSource(context.contentResolver, uri)) { decoder, info, _ ->
            decoder.allocator = ImageDecoder.ALLOCATOR_SOFTWARE
            val scale = min(DEFAULT_A4_WIDTH.toFloat() / info.size.width, DEFAULT_A4_HEIGHT.toFloat() / info.size.height).coerceAtMost(1f)
            decoder.setTargetSampleSize((scale.toInt() shr 1 shl 1).coerceAtLeast(1))
            decoder.setTargetSize((info.size.width * scale).toInt(), (info.size.height * scale).toInt())
        }.also {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
//                it.gainmap = null
            }
        }
    }
}