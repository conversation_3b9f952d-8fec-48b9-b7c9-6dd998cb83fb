package com.hu.demo.main.works.fileparse.box.jpg

import com.hu.demo.main.works.fileparse.box.Tree
import com.hu.demo.utils.ByteOrderedDataInputStream
import com.hu.demo.utils.contentHexString
import com.hu.demo.utils.toHexString

internal class Sof0Section(parent: Tree) : SizeSection(parent) {
    private var sampleAccuracy: Byte? = null
    private var imageHeight: Short? = null
    private var imageWidth: Short? = null
    private var componentSize: Byte? = null
    private var components: ByteArray? = null
    private val sb = StringBuilder()

    override fun read(bis: ByteOrderedDataInputStream) {
        super.read(bis)
        sampleAccuracy = bis.nReadByte()
        imageHeight = bis.nReadShort()
        imageWidth = bis.nReadShort()
        componentSize = bis.nReadByte()
        components = ByteArray(componentSize!!.toInt()) {
            bis.nReadByte()
        }
    }

    override fun toString(): String {
        return "${super.toString()} SOF0\nsampleAccuracy:$sampleAccuracy, imageHeight:${imageHeight?.toHexString()}, " +
                "imageWidth:${imageWidth?.toHexString()}, componentSize:${componentSize?.toHexString()}, " +
                "components:${components?.contentHexString()}"
    }
}
