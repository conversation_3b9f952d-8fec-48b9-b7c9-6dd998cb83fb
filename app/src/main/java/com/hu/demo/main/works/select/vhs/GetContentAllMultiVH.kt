package com.hu.demo.main.works.select.vhs

import android.net.Uri
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.hu.demo.main.R
import com.hu.demo.main.adapter.BaseVH
import com.hu.demo.utils.setImageUriAny
import com.hu.demo.main.works.select.datas.GetContentAllMultiData

class GetContentAllMultiVH(parent: ViewGroup) : BaseVH<GetContentAllMultiData>(parent, R.layout.item_pick_multi) {
    private val tvTitle: TextView = findViewById(R.id.tv_item_title)
    private val ivImages = arrayListOf<ImageView>(
        findViewById(R.id.iv_item_image1),
        findViewById(R.id.iv_item_image2),
        findViewById(R.id.iv_item_image3),
        findViewById(R.id.iv_item_image4),
        findViewById(R.id.iv_item_image5),
        findViewById(R.id.iv_item_image6)
    )

    @Suppress("UNCHECKED_CAST")
    override fun bind(extraData: Map<String, Any>, data: GetContentAllMultiData, position: Int) {
        tvTitle.text = data.title
        (data.obj as? List<Uri>)?.zip(ivImages) { uri: Uri, ivImage: ImageView ->
            ivImage.setImageUriAny(uri)
        }
        itemView.setOnClickListener {
            data.launcher.launch("*/*")
        }
    }
}