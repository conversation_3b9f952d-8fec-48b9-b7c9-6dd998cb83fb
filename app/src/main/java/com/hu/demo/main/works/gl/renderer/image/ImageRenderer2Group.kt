/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - ImageRenderer.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/08/29
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * huca<PERSON><PERSON>@Apps.Gallery		2024/08/29		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.renderer.image

import android.graphics.ColorSpace
import com.hu.demo.utils.ApiLevelUtil
import com.hu.demo.main.works.gl.renderer.normal.FrameRenderer
import com.hu.demo.main.works.gl.renderer.normal.GamutRenderer
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.VALUE_NEED_RENDER_THRESHOLD
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.NEXT_NP
import com.hu.demo.main.works.gl.renderer.param.RenderArgs
import com.hu.demo.main.works.gl.shader.ProgramShader
import com.hu.demo.main.works.gl.utils.GlProgram
import com.hu.demo.main.works.gl.renderer.normal.RendererGroup
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_COLOR_SPACE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_DEVICE_HDR_SDR_RATIO
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_GAINMAP_TEXTURE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_GAINMAP_TEXTURE_2
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_IMAGE_TEXTURE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_IMAGE_TEXTURE_2
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_MIX_RATIO
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.getKey
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.PROCEDURAL_NP
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.STABLE_NP
import com.hu.demo.utils.ColorSpaceExt.toGamma2_2

/**
 * 图片的渲染器，其作为父级，整合参数
 *
 * 输入：
 *
 * | key                                  | 类型        | 是否必须  | 解释        |
 * | -                                    | -           | -        | -           |
 * | [KEY_STABLE_COMPARE_IMAGE_TEXTURE]   | [ITexture]  | 否       | 对比图片纹理 |
 * | [KEY_STABLE_IMAGE_TEXTURE]           | [ITexture]  | 否       | 正常图片纹理 |
 * | [KEY_STABLE_IMAGE_THUMBNAIL]         | [ITexture]  | 否       | 缩略图       |
 * | [KEY_NEXT_GAINMAP_TEXTURE]           | [ITexture]  | 否       | 增益纹理     |
 * | [KEY_STABLE_MIX_RATIO]               | [Float]     | 否       | 混合比例     |
 *
 * 输出：
 *
 * | key                  | 类型        | 是否必须    | 解释      |
 * | -                    | -           | -          | -         |
 * | [KEY_NEXT_TEXTURE]   | [ITexture]  | 否         | 输出的纹理 |
 * | [KEY_NEXT_MIX_RATIO] | [Float]     | 否         | 混合比例   |
 */
class ImageRenderer2Group private constructor() : RendererGroup(TAG) {
    init {
        if (ApiLevelUtil.isAtLeastAndroidU()) {
            addChild(new(UhdrRenderer::class.java))
        }
        addChild(new(GamutRenderer::class.java))
        addChild(new(FrameRenderer::class.java))
    }

    override fun install(shader: ProgramShader?): GlProgram? {
        return super.install(null)
    }

    override fun render(renderArgs: RenderArgs, glProgram: GlProgram?) {
        renderArgs.remove(NEXT_NP)

        val mixRatio = 1.0f - renderArgs.require<Float>(PROCEDURAL_NP.getKey(KEY_MIX_RATIO))
        if (mixRatio > VALUE_NEED_RENDER_THRESHOLD) {
            renderArgs[NEXT_NP.getKey(KEY_MIX_RATIO)] = mixRatio

            // 目前发现gamma2.2是oppo显示用的gamma，并不是srgb
            val colorSpace = renderArgs.get<ColorSpace>(STABLE_NP.getKey(KEY_COLOR_SPACE))?.run {
                if ((renderArgs.get<Float>(STABLE_NP.getKey(KEY_DEVICE_HDR_SDR_RATIO)) ?: 1.0f) > 1.0f) {
                    toGamma2_2()
                } else this
            }

            // 如果当前是图片渲染，则设置当前要给SF的色域为屏幕的色域，如果图片是UHDR的，则会在UhdrRenderer中再次修改
            renderArgs[PROCEDURAL_NP.getKey(KEY_COLOR_SPACE)] = colorSpace

            renderArgs[NEXT_NP.getKey(KEY_COLOR_SPACE)] = colorSpace

            renderArgs[PROCEDURAL_NP.getKey(KEY_GAINMAP_TEXTURE_2)] = renderArgs.get<Any>(STABLE_NP.getKey(KEY_GAINMAP_TEXTURE_2))
            renderArgs[PROCEDURAL_NP.getKey(KEY_IMAGE_TEXTURE_2)] = renderArgs.get<Any>(STABLE_NP.getKey(KEY_IMAGE_TEXTURE_2))

            renderArgs[NEXT_NP.getKey(KEY_GAINMAP_TEXTURE)] = renderArgs.get<Any>(PROCEDURAL_NP.getKey(KEY_GAINMAP_TEXTURE_2))
            renderArgs[NEXT_NP.getKey(KEY_IMAGE_TEXTURE)] = renderArgs.get<Any>(PROCEDURAL_NP.getKey(KEY_IMAGE_TEXTURE_2))

            super.render(renderArgs, glProgram)
        }
    }

    override fun toString(): String {
        return name
    }

    companion object {
        const val TAG = "ImageRenderer"
    }
}