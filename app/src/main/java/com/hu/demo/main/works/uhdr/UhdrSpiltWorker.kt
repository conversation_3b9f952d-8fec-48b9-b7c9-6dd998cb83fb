package com.hu.demo.main.works.uhdr

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Bitmap.CompressFormat
import android.graphics.ImageDecoder
import android.util.Log
import androidx.core.graphics.decodeBitmap
import androidx.work.Data
import androidx.work.Worker
import androidx.work.WorkerParameters
import com.hu.demo.main.works.uhdr.BaseWorker.Companion.INPUT_DIR
import com.hu.demo.main.works.uhdr.BaseWorker.Companion.OUTPUT_DIR
import com.hu.demo.utils.ApiLevelUtil
import com.oplus.camera.jni.IccProfile
import java.io.File
import java.io.FileFilter
import java.io.FileOutputStream
import java.io.OutputStream

class UhdrSpiltWorker(context: Context, workerParams: WorkerParameters) : Worker(context, workerParams) {
    override fun doWork(): Result {
        return splitUhdr()
    }

    private fun splitUhdr(): Result {
        Log.d(TAG, "splitUhdr: start")
        val inputImagesDir = inputData.getString(INPUT_DIR)?.let { File(it) } ?: run {
            Log.d(TAG, "splitUhdr: end 1")
            return Result.failure()
        }
        val outputImagesDir = inputData.getString(OUTPUT_DIR)?.let { File(it) } ?: inputImagesDir
        outputImagesDir.mkdirs()
        val imageFiles = inputImagesDir.listFiles(FileFilter {
            it.path.endsWith(".jpg") && !it.isHidden
        }) ?: return Result.failure()

        var count: Int = 0
        for ((index, file) in imageFiles.withIndex()) {
            val fileName = file.name.substringBefore(".jpg")
            val bitmap = ImageDecoder.createSource(file).decodeBitmap { _, _ ->
                allocator = ImageDecoder.ALLOCATOR_SOFTWARE
            }
            if (ApiLevelUtil.isAtLeastAndroidU() && bitmap.gainmap != null) {
                val gainmap = bitmap.gainmap
                bitmap.gainmap = null
                gainmap?.gainmapContents?.apply {
                    File(outputImagesDir, "${fileName}_gain.jpg").outputStream().use { fos ->
                        if (config == Bitmap.Config.ALPHA_8) {
                            compressAlpha(CompressFormat.JPEG, 95, fos)
                        } else {
                            compress(CompressFormat.JPEG, 95, fos)
                        }
                    }
                }
                File(outputImagesDir, "${fileName}_gainInfo.txt").writer().buffered().use {
                    it.appendLine("gamma=${gainmap?.gamma.contentToString()}")
                    it.appendLine("ratioMin=${gainmap?.ratioMin.contentToString()}")
                    it.appendLine("ratioMax=${gainmap?.ratioMax.contentToString()}")
                    it.appendLine("epsilonSdr=${gainmap?.epsilonSdr.contentToString()}")
                    it.appendLine("epsilonHdr=${gainmap?.epsilonHdr.contentToString()}")
                    it.appendLine("minDisplayRatioForHdrTransition=${gainmap?.minDisplayRatioForHdrTransition}")
                    it.appendLine("displayRatioForFullHdr=${gainmap?.displayRatioForFullHdr}")
                }
                IccProfile.compressBitmapToFile(bitmap, 95, File(outputImagesDir, "${fileName}_sdr.jpg").absolutePath)
                Log.d(TAG, "splitUhdr: $file, $index")
                count++
            }
        }
        Log.d(TAG, "splitUhdr: end 2")
        return Result.success(Data.Builder().putAll(mapOf(BaseWorker.OUTPUT_COUNT to count)).build())
    }

    private fun Bitmap.compressAlpha(format: CompressFormat, quality: Int, fos: FileOutputStream) {
        val oplusImageHdrImplClazz = Class.forName("com.oplus.media.OplusImageHdrImpl")
        val method = oplusImageHdrImplClazz.getMethod(
            "compressAlpha8",
            Bitmap::class.java,
            CompressFormat::class.java,
            Int::class.java,
            OutputStream::class.java
        )
        method.invoke(null, this, format, quality, fos)
    }

    companion object {
        private const val TAG = "UhdrSpiltWorker"
    }
}