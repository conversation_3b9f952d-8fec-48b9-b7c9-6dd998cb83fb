package com.hu.demo.main.works.fileparse.box

import com.hu.demo.main.works.fileparse.IRead
import com.hu.demo.utils.ByteOrderedDataInputStream
import com.hu.demo.utils.toHexString

internal open class Box(boxName: String, level: Int = 0, parent: Tree? = null) : Tree(boxName, level, parent), IRead {
    protected open val boxReadChild: Boolean = false

    var size: Int? = null
        private set
    var type: String? = null
        private set
    var largeSize: Long? = null
        private set
    var extendedType: String? = null
        private set
    override val realSize: Long
        get() = size?.toULong()?.toLong().takeIf { it != 1L } ?: largeSize ?: 0

    val isLargeSize: Boolean
        get() = size == 1

    /**
     * 剩余的字节数
     */
    protected val remainSize: Long
        get() = realSize - alreadyReadSize

    /**
     * 当前box读取后，剩余的字节数
     */
    private var boxRemain: Long = -1
        get() = field.takeIf { it >= 0 } ?: remainSize

    val boxNeedReadChild: Boolean
        get() {
            return boxReadChild && remainSize > 0
        }

    /**
     * 判断当前box是否读取完成
     */
    val boxReadOver: Boolean
        get() {
            return boxRemain == remainSize || remainSize <= 0L
        }

    final override fun read(bis: ByteOrderedDataInputStream) {
        size = bis.nReadInt()
        type = bis.nReadBytes(4).decodeToString()
        if (isLargeSize) {
            largeSize = bis.nReadLong()
        }
        if (type == "uuid") {
            extendedType
        }
        readInner(bis)
        boxRemain = remainSize
    }

    open fun readInner(bis: ByteOrderedDataInputStream) = Unit

    open fun skipRemain(bis: ByteOrderedDataInputStream) {
        bis.skipFully(remainSize).apply { alreadyReadSize += remainSize }
    }

    override fun toString(): String {
        return "offset: ${(rootTree.alreadyReadSize - alreadyReadSize).toHexString()}, type:'$type', " +
                "size:${realSize.toHexString()}, isLarge:$isLargeSize"
    }

    open fun fork(parent: Tree): Box {
        return Box(nodeName, level, parent)
    }
}

