/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - BitmapTexture.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/08/24
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * huca<PERSON><PERSON>@Apps.Gallery		2024/08/24		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.texture

import android.graphics.Bitmap
import android.graphics.ColorSpace
import android.opengl.GLES30
import com.hu.demo.main.works.gl.texture.ITexture.Companion.getFormat
import com.hu.demo.main.works.gl.texture.ITexture.Companion.getInternalFormat
import com.hu.demo.main.works.gl.texture.ITexture.Companion.getType
import com.hu.demo.main.works.gl.utils.GlUtil
import com.hu.demo.utils.ColorSpaceExt.SRGB
import java.nio.ByteBuffer

/**
 * [Bitmap]类型的[Texture]，通过传入的[bitmap]实现与GPU纹理的绑定。
 *
 * @param bitmap bitmap
 * @param canRecycle 是否可以在[load]后回收[bitmap]，默认是false
 */
class BufferTexture(
    private var buffer: ByteBuffer?,
    width: Int,
    height: Int,
    depth: Int = 0,
    texConfig: TexConfig,
    colorSpace: ColorSpace = SRGB,
    texTarget: Int = GLES30.GL_TEXTURE_2D,
) : Texture(TexKey(width, height, depth, texConfig, texTarget), colorSpace) {
    override val tag: String = TAG

    override fun load() {
        if (hasUpload) return
        val buffer = buffer ?: return

        realTexture.value.load()
        GlUtil.checkGlError()
        buffer.position(0)
        if (texTarget == GLES30.GL_TEXTURE_3D) {
            GLES30.glTexSubImage3D(texTarget, 0, 0, 0, 0, width, height, depth, getFormat(texConfig.getInternalFormat()), texConfig.getType(), buffer)
        } else {
            GLES30.glTexSubImage2D(texTarget, 0, 0, 0, width, height, getFormat(texConfig.getInternalFormat()), texConfig.getType(), buffer)
        }
        GlUtil.checkGlError()
        GLES30.glFlush()

        hasUpload = true

        this.buffer = null
    }


    private companion object {
        private const val TAG = "BufferTexture"
    }
}