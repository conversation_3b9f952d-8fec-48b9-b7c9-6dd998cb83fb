package com.hu.demo.main.works.videoplayer

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Rect
import android.media.MediaMetadataRetriever
import android.net.Uri
import android.util.AttributeSet
import android.view.View
import androidx.core.graphics.withTranslation
import com.hu.demo.utils.resizeToSquare
import java.util.concurrent.Executors
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch

class TrimView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {
    private var isAdsorption: Boolean = false
    private var range: Int = 0
    private var percent: Float = 0f
    private var adapter: Adapter = Adapter(context, 150, 150, 6) {
        invalidate()
    }
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val linePaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.MAGENTA
        style = Paint.Style.STROKE
        strokeWidth = 8.0f
    }
    private val rangePaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = 0x80FFFFFF.toInt()
    }


    fun setWhiteLine(percent: Float) {
        this.percent = percent
        invalidate()
    }

    fun getRange(): IntRange {
        val center = (width * 0.5f).toInt()
        return center - range..center + range
    }

    fun setRange(range: Int) {
        this.range = range
        invalidate()
    }

    fun setAdsorption(isAdsorption: Boolean) {
        this.isAdsorption = isAdsorption
        invalidate()
    }

    @SuppressLint("DrawAllocation")
    override fun onDraw(canvas: Canvas) {
        synchronized(adapter.bitmap) {
            canvas.drawBitmap(
                adapter.bitmap,
                Rect(0, 0, adapter.bitmap.width, adapter.bitmap.height),
                Rect().also(canvas::getClipBounds),
                paint
            )
            canvas.withTranslation(width * 0.5f) {
                canvas.drawRect(Rect(-range, 0, range, height), rangePaint)
            }
            if (isAdsorption) {
                canvas.withTranslation(width * 0.5f) {
                    canvas.drawRect(Rect(-height / 2, 0, height / 2, height), linePaint)
                }
            } else {
                canvas.withTranslation(width * percent) {
                    canvas.drawRect(Rect(-height / 2, 0, height / 2, height), linePaint)
                }
            }
        }
    }

    fun setVideo(uri: Uri) {
        adapter.uri = uri
        adapter.resetBitmap()
        adapter.restart()
    }
}

class Adapter(val context: Context, val imageWidth: Int, val imageHeight: Int, val size: Int, val callback: Bitmap.() -> Unit) {
    var uri: Uri? = null
        set(value) {
            field = value
            retriever.setDataSource(context, uri)
        }
    private val retriever = MediaMetadataRetriever()
    private val dispatcher = Executors.newSingleThreadExecutor().asCoroutineDispatcher()
    var bitmap = Bitmap.createBitmap(imageWidth * size, imageHeight, Bitmap.Config.ARGB_8888)
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private var canvas = Canvas(bitmap)

    fun resetBitmap() {
        synchronized(bitmap) {
            bitmap = Bitmap.createBitmap(imageWidth * size, imageHeight, Bitmap.Config.ARGB_8888)
        }
        canvas = Canvas(bitmap)
    }

    @OptIn(DelicateCoroutinesApi::class)
    fun restart() {
        GlobalScope.launch(dispatcher) {
            val frameCount = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_FRAME_COUNT)?.toIntOrNull() ?: return@launch
            for (index in 0 until size) {
                val frame = retriever.getFrameAtIndex(index * frameCount / size)?.run {
                    resizeToSquare(imageWidth)
                } ?: continue
                synchronized(bitmap) {
                    canvas.drawBitmap(
                        frame,
                        Rect(0, 0, frame.width, frame.height),
                        Rect(imageWidth * index, 0, imageWidth * (index + 1), imageHeight),
                        paint
                    )
                }
                callback(bitmap)
            }
        }
    }
}