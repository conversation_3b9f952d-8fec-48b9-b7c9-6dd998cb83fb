/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - RendererAdapter.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/09/12
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery		2024/09/12		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.renderer

import android.opengl.GLES30
import android.util.Log
import androidx.graphics.lowlatency.BufferInfo
import androidx.graphics.opengl.FrameBuffer
import androidx.graphics.opengl.GLFrameBufferRenderer
import androidx.graphics.opengl.GLRenderer
import androidx.graphics.opengl.egl.EGLManager
import androidx.graphics.surface.SurfaceControlCompat
import androidx.hardware.SyncFenceCompat
import com.hu.demo.main.works.gl.canvas.GlCanvas
import com.hu.demo.main.works.gl.renderer.brighten.HdrModeManager
import com.hu.demo.main.works.gl.renderer.normal.DefaultRenderer
import com.hu.demo.main.works.gl.renderer.normal.Renderer
import com.hu.demo.main.works.gl.renderer.normal.RendererGroup
import com.hu.demo.main.works.gl.renderer.param.KeyName
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_BUFFER_INFO
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_EGL_MANAGER
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_GAINMAP_TEXTURE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_GAINMAP_TEXTURE_2
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_HEIGHT
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_IMAGE_TEXTURE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_IMAGE_TEXTURE_2
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_MIX_RATIO
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_TEXTURE_POOL
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_THUMBNAIL
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_TRANSFORM
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_VIDEO_TEXTURE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_WIDTH
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.getKey
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.NEXT_NP
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.PROCEDURAL_NP
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.STABLE_NP
import com.hu.demo.main.works.gl.renderer.param.RenderArgs
import com.hu.demo.main.works.gl.texture.ITexture
import com.hu.demo.main.works.gl.texture.RealTexture
import com.hu.demo.main.works.gl.utils.BufferDataBinder
import com.hu.demo.main.works.gl.utils.GLThread
import com.hu.demo.main.works.gl.utils.GlProgram
import com.hu.demo.main.works.gl.utils.GlUtil
import com.hu.demo.utils.runIfEqual
import java.util.Collections
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 * 渲染器的适配器，此类是管理渲染器的对象，并连接EGL的回调、外部业务调用
 */
class RendererAdapter(
    private val glRenderer: GLRenderer,
    private val hdrModeManager: HdrModeManager,
) : GLRenderer.EGLContextCallback, GLFrameBufferRenderer.Callback {
    private var renderNode = Renderer.new(DefaultRenderer::class.java)
    private val pendingArgs = RenderArgs.obtain()
    private val renderCallbacks = Collections.synchronizedSet(HashSet<IRenderCallback>())

    @GLThread
    override fun onEGLContextCreated(eglManager: EGLManager) {
        Log.d(TAG, "onEGLContextCreated: is called.")
        pendingArgs[STABLE_NP.getKey(KEY_MIX_RATIO)] = 1.0f
        pendingArgs[STABLE_NP.getKey(KEY_TEXTURE_POOL)] = mutableSetOf<ITexture>()

        renderNode.install()

        hdrModeManager.startShow()
    }

    @GLThread
    override fun onEGLContextDestroyed(eglManager: EGLManager) {
        Log.d(TAG, "onEGLContextDestroyed: is called.")
        clearCache()

        hdrModeManager.stopShow()
    }

    @GLThread
    override fun onDrawFrame(
        eglManager: EGLManager,
        width: Int,
        height: Int,
        bufferInfo: BufferInfo,
        transform: FloatArray,
    ) {
        GLES30.glEnable(GLES30.GL_BLEND)
        GLES30.glBlendFunc(GLES30.GL_SRC_ALPHA, GLES30.GL_ONE_MINUS_SRC_ALPHA)
        GLES30.glClearColor(0.2f, 0.3f, 0.3f, 1.0f)
        GLES30.glClear(GLES30.GL_COLOR_BUFFER_BIT)
        GlUtil.checkGlError()

        // 回收纹理
        reuseTexture()

        // 移除上次渲染中的参数
        pendingArgs.remove(PROCEDURAL_NP)
        pendingArgs.remove(NEXT_NP)

        pendingArgs[PROCEDURAL_NP.getKey(KEY_EGL_MANAGER)] = eglManager
        pendingArgs[PROCEDURAL_NP.getKey(KEY_WIDTH)] = width
        pendingArgs[PROCEDURAL_NP.getKey(KEY_HEIGHT)] = height
        pendingArgs[PROCEDURAL_NP.getKey(KEY_BUFFER_INFO)] = bufferInfo
        pendingArgs[PROCEDURAL_NP.getKey(KEY_TRANSFORM)] = transform

        pendingArgs.set(
            pendingArgs.getInfoByType(STABLE_NP).mapKeys {
                PROCEDURAL_NP.getKey(it.key.keyName)
            }
        )

        renderNode.performRender(pendingArgs)
    }

    @GLThread
    override fun onDrawComplete(
        targetSurfaceControl: SurfaceControlCompat,
        transaction: SurfaceControlCompat.Transaction,
        frameBuffer: FrameBuffer,
        syncFence: SyncFenceCompat?,
    ) {
        hdrModeManager.render(pendingArgs, targetSurfaceControl, transaction)
    }

    /**
     * 对上次渲染请求过程中的纹理执行复用
     */
    private fun reuseTexture() {
        pendingArgs.require<MutableSet<ITexture>>(STABLE_NP.getKey(KEY_TEXTURE_POOL)).removeAll {
            canReuse(it).runIfEqual(true) {
                it.reuse()
            }
        }
    }

    /**
     * 判断纹理是否在不能放进缓存池的列表中
     *
     * @param texture 需要判断纹理
     *
     * @return 返回是否可以重用
     */
    private fun canReuse(texture: ITexture): Boolean {
        return CANNOT_REUSE_KEY_LIST.any {
            texture == pendingArgs.get<ITexture>(it)
        }.not()
    }

    /**
     * 设置参数，一般情况下，设置的参数需是[NodePath.STABLE_NODE_PATH]下的
     *
     * @param keyName key名称
     * @param value 值
     */
    fun setData(keyName: KeyName, value: Any?) {
        glRenderer.execute {
            pendingArgs[keyName] = value
            (value as? ITexture)?.addToPool(pendingArgs)
        }
    }

    /**
     * 设置参数，一般情况下，设置的参数需是[NodePath.STABLE_NODE_PATH]下的
     *
     * @param values 需要设施的参数集合
     */
    fun setData(values: Map<KeyName, Any?>) {
        glRenderer.execute {
            values.forEach { (key, value) ->
                pendingArgs[key] = value
                (value as? ITexture)?.addToPool(pendingArgs)
            }
        }
    }

    /**
     * 通过[keyName]获取数据
     *
     * @param keyName 数据的key，数据一般是[NodePath.STABLE_NODE_PATH]下的内容
     *
     * @return 返回获取到到的指定的数据
     */
    suspend fun <T> getData(keyName: KeyName): T? {
        return suspendCoroutine {
            glRenderer.execute {
                it.resume(pendingArgs.get<T>(keyName))
            }
        }
    }

    /**
     * 修改节点树
     */
    fun changeNode(func: (RendererGroup) -> Unit) {
        glRenderer.execute {
            func(renderNode)
            renderNode.install()
        }
    }

    /**
     * 清楚缓存
     */
    @GLThread
    fun clearCache() {
        Log.d(TAG, "clearCache: is called.")
        Renderer.clean()
        GlCanvas.clean()
        GlProgram.clean()
        BufferDataBinder.clean()
        RealTexture.clean()
        RenderArgs.clean()
    }

    /**
     * 注册渲染的回调
     *
     * @param callback 渲染的回调
     */
    fun registerRenderCallback(callback: IRenderCallback) {
        renderCallbacks.add(callback)
    }

    /**
     * 反注册渲染的回调
     *
     * @param callback 渲染的回调
     */
    fun unregisterRenderCallback(callback: IRenderCallback) {
        renderCallbacks.remove(callback)
    }

    companion object {
        const val TAG = "RendererAdapter"

        /**
         * 不能执行重用和回收的纹理key，纹理如果是实体纹理（非虚拟纹理），需要让渲染链路管理的列表
         */
        private val CANNOT_REUSE_KEY_LIST = setOf(
            STABLE_NP.getKey(KEY_IMAGE_TEXTURE),
            STABLE_NP.getKey(KEY_IMAGE_TEXTURE_2),
            STABLE_NP.getKey(KEY_THUMBNAIL),
            STABLE_NP.getKey(KEY_VIDEO_TEXTURE),
            STABLE_NP.getKey(KEY_GAINMAP_TEXTURE),
            STABLE_NP.getKey(KEY_GAINMAP_TEXTURE_2)
        )
    }
}

/**
 * 渲染的回调
 */
fun interface IRenderCallback {
    /**
     * 执行渲染时触发回调
     */
    fun onRender()
}