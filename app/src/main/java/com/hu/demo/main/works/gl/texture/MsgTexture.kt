/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - VirtualTexture.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/10/24
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * hucanh<PERSON>@Apps.Gallery		2024/10/24		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.texture

import android.graphics.Bitmap
import android.graphics.ColorSpace
import android.opengl.GLES30
import com.hu.demo.utils.ColorSpaceExt

/**
 * 只提供信息的Texture类，并不是真正的纹理
 *
 * @param texture 输入真正的纹理
 */
class MsgTexture(
    override val width: Int,
    override val height: Int,
    override val depth: Int = 0,
    override val colorSpace: ColorSpace = ColorSpaceExt.SRGB,
    override val texConfig: TexConfig = TexConfig.ARGB_8888,
    override val clamp: Float = 1.0f,
    override val texTarget: Int = GLES30.GL_TEXTURE_2D,
) : ITexture {
    override var canUse: Boolean = false
    override var displayColorSpace: ColorSpace = colorSpace
    override val textureId: Int = GLES30.GL_NONE
    override val byteSize: Int = texConfig.byteSize
    override fun reuse() = Unit
    override fun recycle() = Unit

    override fun load() = Unit

    override fun force() = Unit
    override fun copy(outTexture: ITexture?): ITexture {
        throw IllegalArgumentException("MsgTexture cannot call is method!")
    }

    override fun toBitmap(scale: Float): Bitmap {
        throw IllegalArgumentException("MsgTexture cannot call is method!")
    }

    override fun active(texUnitIndex: Int) = Unit

    override fun getTexture(): Texture {
        throw IllegalArgumentException("MsgTexture cannot call is method!")
    }

    override fun toVirtual(): ITexture {
        throw IllegalArgumentException("MsgTexture cannot call is method!")
    }

    override fun increaseMark() {
        throw IllegalArgumentException("MsgTexture cannot call is method!")
    }

    override fun reduceMark() {
        throw IllegalArgumentException("MsgTexture cannot call is method!")
    }
}