/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - UhdrRenderer.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/09/19
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery		2024/09/19		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.renderer.image

import android.graphics.ColorSpace
import android.graphics.RectF
import android.opengl.GLES30
import android.opengl.Matrix
import android.os.Build
import androidx.annotation.RequiresApi
import com.hu.demo.main.works.gl.canvas.FboGlCanvas
import com.hu.demo.main.works.gl.canvas.FboKey
import com.hu.demo.main.works.gl.canvas.GlCanvas
import com.hu.demo.main.works.gl.renderer.addToPool
import com.hu.demo.main.works.gl.renderer.getGamutMode
import com.hu.demo.main.works.gl.renderer.normal.Renderer
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_COLOR_SPACE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_DEVICE_HDR_SDR_RATIO
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_GAINMAP_TEXTURE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_IMAGE_TEXTURE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.getKey
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.NEXT_NP
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.PROCEDURAL_NP
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.STABLE_NP
import com.hu.demo.main.works.gl.renderer.param.RenderArgs
import com.hu.demo.main.works.gl.shader.GlShader
import com.hu.demo.main.works.gl.shader.ProgramShader
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.CONST_TF_PARAMS_BT709
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.CONST_TF_PARAMS_GAMMA_2_2
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.CONST_TF_PARAMS_HLG
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.CONST_TF_PARAMS_PQ
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.CONST_TF_PARAMS_SRGB
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.EXEC_COMBINE_UHDR
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.EXEC_SET_FRAGCOLOR
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.EXEC_SET_GL_POSITION
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.EXEC_SET_TEXCOORD
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.EXEC_TEXTURE_COLOR
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_IN_VEC2_TEX_COORD
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_LAYOUT_IN_VEC3_POSITION
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_LAYOUT_IN_VEC3_TEX_COORD
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_OUT_VEC2_TEX_COORD
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_OUT_VEC4_FRAGCOLOR
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_UNIFORM_FLOAT_LOG_DEVICE_HDRSDR_RATIO
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_UNIFORM_FLOAT_LOG_DISPLAY_HDR_RATIO
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_UNIFORM_FLOAT_LOG_DISPLAY_SDR_RATIO
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_UNIFORM_INT_SINGLE_CHANNEL
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_UNIFORM_INT_UHDR_SRC_MODE
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_UNIFORM_INT_USE_COMBINE_UHDR
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_UNIFORM_MAT4_MODEL_M
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_UNIFORM_MAT4_PROJECTION_M
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_UNIFORM_MAT4_VIEW_M
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_UNIFORM_SAMPLER2D_GAINMAP
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_UNIFORM_SAMPLER2D_TEX
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_UNIFORM_VEC3_EPSILON_HDR
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_UNIFORM_VEC3_EPSILON_SDR
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_UNIFORM_VEC3_GAINMAP_GAMMA
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_UNIFORM_VEC3_LOG_RATIO_MAX
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_UNIFORM_VEC3_LOG_RATIO_MIN
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.METHOD_BT709_EOTF
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.METHOD_BT709_OETF
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.METHOD_COMPOSE_UHDR
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.METHOD_EOTF
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.METHOD_GAMMA_2_2_EOTF
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.METHOD_GAMMA_2_2_OETF
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.METHOD_HLG_EOTF
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.METHOD_HLG_OETF
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.METHOD_OETF
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.METHOD_PQ_EOTF
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.METHOD_PQ_OETF
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.METHOD_SRGB_EOTF
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.METHOD_SRGB_OETF
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.STRUCT_TF_HLG_PARAMS
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.STRUCT_TF_PARAMS
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.STRUCT_TF_PQ_PARAMS
import com.hu.demo.main.works.gl.texture.GainmapTexture
import com.hu.demo.main.works.gl.texture.IGainTexture
import com.hu.demo.main.works.gl.texture.ITexture
import com.hu.demo.main.works.gl.texture.MsgTexture
import com.hu.demo.main.works.gl.texture.RawTexture
import com.hu.demo.main.works.gl.texture.TexConfig
import com.hu.demo.main.works.gl.texture.Texture
import com.hu.demo.main.works.gl.utils.AttributeShaderParam
import com.hu.demo.main.works.gl.utils.BufferDataBinder
import com.hu.demo.main.works.gl.utils.GlProgram
import com.hu.demo.main.works.gl.utils.ShaderParam
import com.hu.demo.main.works.gl.utils.Uniform1fShaderParam
import com.hu.demo.main.works.gl.utils.Uniform1iShaderParam
import com.hu.demo.main.works.gl.utils.Uniform3fShaderParam
import com.hu.demo.main.works.gl.utils.UniformMat4fvShaderParam
import com.hu.demo.main.works.gl.utils.toBuffer
import com.hu.demo.utils.BYTE_SIZE_1
import com.hu.demo.utils.ColorSpaceExt
import com.hu.demo.utils.ColorSpaceExt.toHdr
import com.hu.demo.utils.getOrLog
import kotlin.math.ln
import kotlin.math.min

/**
 * UHDR渲染器，将uhdr上传成纹理
 *
 * 输入：
 *
 * | key                                | 类型              | 是否必须    | 解释                |
 * | -                                  | -                 | -          | -                   |
 * | [KEY_NEXT_TEXTURE]                 | [Texture]         | 是         | 主纹理              |
 * | [KEY_NEXT_GAINMAP_TEXTURE]       | [GainmapTexture]  | 是         | 增益纹理            |
 * | [KEY_STABLE_COLOR_SPACE]           | [ColorSpace]      | 是         | 当前屏幕色域        |
 * | [KEY_STABLE_DEVICE_HDR_SDR_RATIO]  | [Float]           | 是         | 当前设备的hdr sdr比 |
 *
 * 输出：
 *
 * | key                                | 类型              | 是否必须    | 解释               |
 * | -                                  | -                 | -         | -                  |
 * | [KEY_NEXT_TEXTURE]                 | [Texture]         | 是         | 输出纹理           |
 */
@Suppress("ThrowingExceptionsWithoutMessageOrCause", "MagicNumber", "FuncSingleCommentRule")
@RequiresApi(Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
class UhdrRenderer private constructor() : Renderer(TAG) {
    // 顶点着色器中attribute、uniform
    private val aPosition = AttributeShaderParam("aPosition", POSITION_SIZE)
    private val aTexCoord = AttributeShaderParam("aTexCoord", COORD_SIZE)
    private val uViewM = UniformMat4fvShaderParam("uViewM")
    private val uModelM = UniformMat4fvShaderParam("uModelM")
    private val uProjectionM = UniformMat4fvShaderParam("uProjectionM")

    // 片段着色器中attribute、uniform
    private val uTex = Uniform1iShaderParam("uTex")
    private val uGainmap = Uniform1iShaderParam("uGainmap")
    private val uLogRatioMin = Uniform3fShaderParam("uLogRatioMin")
    private val uLogRatioMax = Uniform3fShaderParam("uLogRatioMax")
    private val uGainmapGamma = Uniform3fShaderParam("uGainmapGamma")
    private val uEpsilonSdr = Uniform3fShaderParam("uEpsilonSdr")
    private val uEpsilonHdr = Uniform3fShaderParam("uEpsilonHdr")
    private val uLogDeviceHdrSdrRatio = Uniform1fShaderParam("uLogDeviceHdrSdrRatio")
    private val uLogDisplaySdrRatio = Uniform1fShaderParam("uLogDisplaySdrRatio")
    private val uLogDisplayHdrRatio = Uniform1fShaderParam("uLogDisplayHdrRatio")
    private val uSingleChannel = Uniform1iShaderParam("uSingleChannel")
    private val uUseCombineUhdr = Uniform1iShaderParam("uUseCombineUhdr")
    private val uUhdrSrcMode = Uniform1iShaderParam("uUhdrSrcMode")

    override val handleParams: Array<ShaderParam> = arrayOf(
        aPosition,
        aTexCoord,
        uViewM,
        uModelM,
        uProjectionM,
        uTex,
        uGainmap,
        uLogRatioMin,
        uLogRatioMax,
        uGainmapGamma,
        uEpsilonSdr,
        uEpsilonHdr,
        uLogDeviceHdrSdrRatio,
        uLogDisplaySdrRatio,
        uLogDisplayHdrRatio,
        uSingleChannel,
        uUseCombineUhdr,
        uUhdrSrcMode
    )

    private val viewM = FloatArray(MATRIX_SIZE).apply {
        Matrix.setIdentityM(this, 0)
    }
    private val modelM = FloatArray(MATRIX_SIZE).apply {
        Matrix.setIdentityM(this, 0)
    }
    private val projectionM = FloatArray(MATRIX_SIZE).apply {
        Matrix.setIdentityM(this, 0)
    }
    override val programShader: ProgramShader = ProgramShader(VS_SHADER_BUILDER, FS_SHADER_BUILDER)

    override fun install(shader: ProgramShader?): GlProgram {
        val glProgram = super.install(shader)!!
        if (shader == null) {
            glProgram.use()
            val bufferDataBinder = BufferDataBinder.obtain(vertices)
            bufferDataBinder.install()

            bufferDataBinder.use()
            aPosition.setValue(STRIDE, POSITION_OFFSET)
            aTexCoord.setValue(STRIDE, COORD_OFFSET)
            bufferDataBinder.unUse()

            Matrix.setIdentityM(viewM, 0)
            uViewM.setValue(viewM)
            glProgram.unUse()
        }

        return glProgram
    }

    @Suppress("LongMethod")
    override fun render(renderArgs: RenderArgs, glProgram: GlProgram?) {
        glProgram!!

        glProgram.use()
        uUseCombineUhdr.setValue(0)
        glProgram.unUse()

        val baseTexture = renderArgs.get<Texture>(NEXT_NP.getKey(KEY_IMAGE_TEXTURE))
            ?.addToPool(renderArgs).getOrLog(TAG, "baseTexture") ?: return
        baseTexture.load()
        val gainmapTexture = renderArgs.get<IGainTexture>(NEXT_NP.getKey(KEY_GAINMAP_TEXTURE))
            ?.addToPool(renderArgs).getOrLog(TAG, "gainmapTexture") ?: return
        gainmapTexture.load()

        glProgram.use()

        uUseCombineUhdr.setValue(1)

        val portWidth = baseTexture.width
        val portHeight = baseTexture.height
        calcModelM(baseTexture, portWidth, portHeight)
        uModelM.setValue(modelM)

        calcProjectionM(portWidth, portHeight)
        uProjectionM.setValue(projectionM)

        val isAlpha = gainmapTexture.byteSize == BYTE_SIZE_1
        uSingleChannel.setValue(if (isAlpha) 1 else 0)

        val mode = baseTexture.displayColorSpace.getGamutMode()
        uUhdrSrcMode.setValue(mode)

        val gainmap = gainmapTexture.gainmap
        val gainmapGamma = gainmap.gamma
        uGainmapGamma.setValue(gainmapGamma[0], gainmapGamma[1], gainmapGamma[2])

        uLogRatioMin.setValue(ln(gainmap.ratioMin[0]), ln(gainmap.ratioMin[1]), ln(gainmap.ratioMin[2]))

        uLogRatioMax.setValue(ln(gainmap.ratioMax[0]), ln(gainmap.ratioMax[1]), ln(gainmap.ratioMax[2]))

        uEpsilonSdr.setValue(gainmap.epsilonSdr[0], gainmap.epsilonSdr[1], gainmap.epsilonSdr[2])

        uEpsilonHdr.setValue(gainmap.epsilonHdr[0], gainmap.epsilonHdr[1], gainmap.epsilonHdr[2])

        val deviceHdrSdrRatio = renderArgs.get<Float>(STABLE_NP.getKey(KEY_DEVICE_HDR_SDR_RATIO)) ?: 1.0f
        val logDeviceHdrSdrRatio = ln(deviceHdrSdrRatio)
        uLogDeviceHdrSdrRatio.setValue(logDeviceHdrSdrRatio)
        val logDisplayRatioSdr = ln(gainmap.minDisplayRatioForHdrTransition)
        uLogDisplaySdrRatio.setValue(logDisplayRatioSdr)
        val logDisplayHdrRatio = ln(gainmap.displayRatioForFullHdr)
        uLogDisplayHdrRatio.setValue(logDisplayHdrRatio)

        val targetColorSpace = if (baseTexture.colorSpace == ColorSpaceExt.DISPLAY_P3) {
            ColorSpaceExt.LINEAR_DISPLAY_P3
        } else ColorSpaceExt.LINEAR_SRGB

        // 设置合成uhdr的色域
        val hdrColorSpace = renderArgs.get<ColorSpace>(NEXT_NP.getKey(KEY_COLOR_SPACE))?.toHdr()
        renderArgs[PROCEDURAL_NP.getKey(KEY_COLOR_SPACE)] = hdrColorSpace
        renderArgs[NEXT_NP.getKey(KEY_COLOR_SPACE)] = hdrColorSpace

        val texUnitIndex0 = 0
        baseTexture.active(texUnitIndex0)
        uTex.setValue(texUnitIndex0)
        val texUnitIndex1 = 1
        gainmapTexture.active(texUnitIndex1)
        uGainmap.setValue(texUnitIndex1)

        val outTexture: ITexture
        if (glProgram.programShader == programShader) {
            val bufferDataBinder = BufferDataBinder.obtain(vertices)
            bufferDataBinder.bind()

            // 由于 OpenGL的状态机特性，以下的纹理active后的bind操作都会绑定到刚刚的纹理单元上，故需要提前创建输出纹理
            outTexture = RawTexture(portWidth, portHeight, targetColorSpace, TexConfig.RGBA_F16)
            GLES30.glActiveTexture(GLES30.GL_TEXTURE31)
            outTexture.load()

            val fboKey = FboKey(vertices.capacity() / STRIDE, portWidth, portHeight)
            val glCanvas = GlCanvas.obtain(fboKey) as FboGlCanvas
            glCanvas.draw(outTexture)
            glCanvas.reuse()
            bufferDataBinder.unbind()
        } else {
            outTexture = MsgTexture(portWidth, portHeight, 0,targetColorSpace, TexConfig.RGBA_F16)
        }
        glProgram.unUse()
        renderArgs[NEXT_NP.getKey(KEY_IMAGE_TEXTURE)] = outTexture
    }

    private fun calcProjectionM(portWidth: Int, portHeight: Int) {
        Matrix.setIdentityM(projectionM, 0)
        Matrix.orthoM(projectionM, 0, 0f, portWidth.toFloat(), portHeight.toFloat(), 0f, -1f, 1f)
    }

    private fun calcModelM(texture: ITexture, portWidth: Int, portHeight: Int) {
        Matrix.setIdentityM(modelM, 0)
        val displayRect = calcDisplayRect(texture.width.toFloat(), texture.height.toFloat(), portWidth.toFloat(), portHeight.toFloat())
        Matrix.translateM(modelM, 0, displayRect.left, displayRect.top, 0f)
        Matrix.scaleM(modelM, 0, displayRect.width(), displayRect.height(), 1f)
    }

    private fun calcDisplayRect(imageWidth: Float, imageHeight: Float, portWidth: Float, portHeight: Float): RectF {
        val ratio = min(portWidth / imageWidth, portHeight / imageHeight)
        val displayWidth = imageWidth * ratio
        val displayHeight = imageHeight * ratio
        val left = (portWidth - displayWidth) / 2
        val top = (portHeight - displayHeight) / 2
        return RectF(left, top, left + displayWidth, top + displayHeight)
    }

    companion object {
        private const val TAG = "UhdrRenderer"
        private val VS_SHADER_BUILDER by lazy {
            GlShader().apply {
                addField(FIELD_LAYOUT_IN_VEC3_POSITION)
                addField(FIELD_LAYOUT_IN_VEC3_TEX_COORD)
                addField(FIELD_UNIFORM_MAT4_VIEW_M)
                addField(FIELD_UNIFORM_MAT4_MODEL_M)
                addField(FIELD_UNIFORM_MAT4_PROJECTION_M)
                addField(FIELD_OUT_VEC2_TEX_COORD)

                addExecEnd(EXEC_SET_GL_POSITION)
                addExecEnd(EXEC_SET_TEXCOORD)
            }
        }

        private val FS_SHADER_BUILDER by lazy {
            GlShader().apply {
                addStruct(STRUCT_TF_PARAMS)
                addStruct(STRUCT_TF_HLG_PARAMS)
                addStruct(STRUCT_TF_PQ_PARAMS)

                addConst(CONST_TF_PARAMS_SRGB)
                addConst(CONST_TF_PARAMS_GAMMA_2_2)
                addConst(CONST_TF_PARAMS_BT709)
                addConst(CONST_TF_PARAMS_BT709)
                addConst(CONST_TF_PARAMS_HLG)
                addConst(CONST_TF_PARAMS_PQ)

                addField(FIELD_IN_VEC2_TEX_COORD)
                addField(FIELD_OUT_VEC4_FRAGCOLOR)
                addField(FIELD_UNIFORM_SAMPLER2D_TEX)
                addField(FIELD_UNIFORM_SAMPLER2D_GAINMAP)
                addField(FIELD_UNIFORM_VEC3_LOG_RATIO_MIN)
                addField(FIELD_UNIFORM_VEC3_LOG_RATIO_MAX)
                addField(FIELD_UNIFORM_VEC3_GAINMAP_GAMMA)
                addField(FIELD_UNIFORM_VEC3_EPSILON_SDR)
                addField(FIELD_UNIFORM_VEC3_EPSILON_HDR)
                addField(FIELD_UNIFORM_FLOAT_LOG_DEVICE_HDRSDR_RATIO)
                addField(FIELD_UNIFORM_FLOAT_LOG_DISPLAY_SDR_RATIO)
                addField(FIELD_UNIFORM_FLOAT_LOG_DISPLAY_HDR_RATIO)
                addField(FIELD_UNIFORM_INT_SINGLE_CHANNEL)
                addField(FIELD_UNIFORM_INT_USE_COMBINE_UHDR)
                addField(FIELD_UNIFORM_INT_UHDR_SRC_MODE)

                addMethod(METHOD_SRGB_EOTF)
                addMethod(METHOD_SRGB_OETF)
                addMethod(METHOD_HLG_EOTF)
                addMethod(METHOD_HLG_OETF)
                addMethod(METHOD_PQ_EOTF)
                addMethod(METHOD_PQ_OETF)
                addMethod(METHOD_BT709_EOTF)
                addMethod(METHOD_BT709_OETF)
                addMethod(METHOD_GAMMA_2_2_EOTF)
                addMethod(METHOD_GAMMA_2_2_OETF)
                addMethod(METHOD_EOTF)
                addMethod(METHOD_OETF)
                addMethod(METHOD_COMPOSE_UHDR)

                addExecStart(EXEC_TEXTURE_COLOR)
                addExec(EXEC_COMBINE_UHDR)
                addExecEnd(EXEC_SET_FRAGCOLOR)
            }
        }

        /**
         * 步长
         */
        private const val STRIDE = 5

        /**
         * 矩阵大小
         */
        private const val MATRIX_SIZE = 16

        /**
         * 顶点位置在buffer中的偏移
         */
        private const val POSITION_OFFSET = 0

        /**
         * 坐标在buffer中的偏移
         */
        private const val COORD_OFFSET = 3

        /**
         * 顶点位置的大小
         */
        private const val POSITION_SIZE = 3

        /**
         * 坐标的大小
         */
        private const val COORD_SIZE = 2
        private val vertices = floatArrayOf(
            // X, Y, Z, U, V
            0f, 0f, 0.0f, 0.0f, 1.0f,
            1f, 0f, 0.0f, 1.0f, 1.0f,
            0f, 1f, 0.0f, 0.0f, 0.0f,
            1f, 1f, 0.0f, 1.0f, 0.0f
        ).toBuffer()
    }
}
