/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - VideoMode.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/11/12
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery		2024/11/12		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.renderer.brighten

import android.annotation.SuppressLint
import android.graphics.ColorSpace
import android.hardware.DataSpace.DATASPACE_BT2020_PQ
import androidx.graphics.surface.SurfaceControlCompat
import com.hu.demo.main.works.gl.renderer.brighten.ISubHdrMode.Companion.DATASPACE_BT2020_HLG
import com.hu.demo.main.works.gl.renderer.brighten.ISubHdrMode.Companion.DATASPACE_CDISPLAY_P3
import com.hu.demo.main.works.gl.renderer.brighten.ISubHdrMode.Companion.DATASPACE_DCI_P3
import com.hu.demo.main.works.gl.renderer.brighten.ISubHdrMode.Companion.DATASPACE_P3_HLG
import com.hu.demo.main.works.gl.renderer.brighten.ISubHdrMode.Companion.DATASPACE_SCRGB
import com.hu.demo.main.works.gl.renderer.brighten.ISubHdrMode.Companion.DATASPACE_SRGB
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_COLOR_SPACE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_DEVICE_HDR_SDR_RATIO
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_MIX_RATIO
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_VIDEO_TEXTURE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.VALUE_IMAGE_VIDEO_THRESHOLD
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.getKey
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.PROCEDURAL_NP
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.STABLE_NP
import com.hu.demo.main.works.gl.renderer.param.RenderArgs
import com.hu.demo.main.works.gl.texture.ITexture
import com.hu.demo.utils.ColorSpaceExt
import com.hu.demo.utils.ColorSpaceExt.BT2020_HLG
import com.hu.demo.utils.ColorSpaceExt.BT2020_PQ
import com.hu.demo.utils.ColorSpaceExt.DISPLAY_P3_HLG
import com.hu.demo.utils.ColorSpaceExt.HDR_DISPLAY_P3
import com.hu.demo.utils.ColorSpaceExt.HDR_DISPLAY_P3_GAMMA_2_2
import com.hu.demo.utils.ColorSpaceExt.HDR_SRGB
import com.hu.demo.utils.ColorSpaceExt.HDR_SRGB_GAMMA_2_2
import com.oplus.gallery.foundation.opengl2.texture.OesRawTexture

class VideoHdrMode : ISubHdrMode {
    override var inWarn: Boolean = false

    override fun startShow() = Unit

    /**
     * 检查是否要处理当前的显示模式，满足视频提亮：
     * 1. 设备支持臻彩视界；
     * 2. 视频资源是HDR；
     * 3. 图片与视频混合，图片的混合参数小于等于0.5
     *
     * @param renderArgs 渲染参数
     *
     * @return 返回检查结果，true表示需要当前模式处理，false不能使用当前模式处理
     */
    override fun checkHandle(renderArgs: RenderArgs): Boolean {
        // 2. 视频资源是HDR；
        val isHdrVideo = (renderArgs.require<ITexture>(STABLE_NP.getKey(KEY_VIDEO_TEXTURE)) as? OesRawTexture)?.colorSpace in HDR_GAMMA
        if (!isHdrVideo) {
            return false
        }
        // 图片与视频混合，图片的混合参数小于等于0.5
        return renderArgs.require<Float>(PROCEDURAL_NP.getKey(KEY_MIX_RATIO)) <= VALUE_IMAGE_VIDEO_THRESHOLD
    }

    @SuppressLint("NewApi")
    override fun render(renderArgs: RenderArgs, surfaceControl: SurfaceControlCompat, transaction: SurfaceControlCompat.Transaction) {
        val deviceHdrSdrRatio = renderArgs.get<Float>(STABLE_NP.getKey(KEY_DEVICE_HDR_SDR_RATIO)) ?: 1.0f
        when (renderArgs.get<ColorSpace>(PROCEDURAL_NP.getKey(KEY_COLOR_SPACE))) {
            BT2020_HLG -> transaction.setDataSpace(surfaceControl, DATASPACE_BT2020_HLG)
            BT2020_PQ -> transaction.setDataSpace(surfaceControl, DATASPACE_BT2020_PQ)
            DISPLAY_P3_HLG -> transaction.setDataSpace(surfaceControl, DATASPACE_P3_HLG)
            HDR_DISPLAY_P3, HDR_DISPLAY_P3_GAMMA_2_2 -> transaction.setDataSpace(surfaceControl, DATASPACE_SCRGB)
            HDR_SRGB, HDR_SRGB_GAMMA_2_2 -> transaction.setDataSpace(surfaceControl, DATASPACE_CDISPLAY_P3)
            else -> Unit
        }
        transaction.setExtendedRangeBrightness(surfaceControl, deviceHdrSdrRatio, HLG_HDR_RATIO)
    }

    @SuppressLint("NewApi")
    override fun unrender(renderArgs: RenderArgs, surfaceControl: SurfaceControlCompat, transaction: SurfaceControlCompat.Transaction) {
        when (renderArgs.get<ColorSpace>(STABLE_NP.getKey(KEY_COLOR_SPACE))) {
            ColorSpaceExt.DISPLAY_P3 -> transaction.setDataSpace(surfaceControl, DATASPACE_DCI_P3)
            else -> transaction.setDataSpace(surfaceControl, DATASPACE_SRGB)
        }
        transaction.setExtendedRangeBrightness(surfaceControl, 1.0f, 1.0f)
    }

    override fun stopShow() = Unit

    override fun toString(): String {
        return TAG
    }

    companion object {
        private const val TAG = "VideoMode"

        private const val HLG_HDR_RATIO = 5.0f

        /**
         * 是HDR的gamma曲线，就可以认为是HDR的视频帧
         */
        private val HDR_GAMMA by lazy {
            arrayOf(
                DISPLAY_P3_HLG,
                BT2020_HLG,
                BT2020_PQ
            )
        }
    }
}