package com.hu.demo.main.works.recyclerview.longclick

import android.graphics.Canvas
import android.view.MotionEvent
import android.view.ViewGroup
import com.hu.demo.main.works.recyclerview.view.IAttachView
import com.hu.demo.main.works.recyclerview.view.IItemTouchProcessor
import com.hu.demo.main.works.recyclerview.view.ViewTouchAdapter

class ItemLongClickProcessor<V : ViewGroup>(private val longClick: ItemLongClick? = null) : IItemTouchProcessor<V>, IAttachView<V>,
    LongClickCallback {
    private var touchAdapter: ViewTouchAdapter<V>? = null
    private var isLongClick = false

    override fun onLongClick(e: MotionEvent, pos: Int): Boolean {
        return (longClick?.onLongClick(pos) ?: false).apply {
            isLongClick = this
        }
    }

    override fun onInterceptTouchEvent(e: MotionEvent): Boolean {
        return isLongClick.apply {
            if ((e.actionMasked == MotionEvent.ACTION_UP) || (e.actionMasked == MotionEvent.ACTION_CANCEL)) {
                isLongClick = false
            }
        }
    }

    override fun onTouchEvent(e: MotionEvent) {
        if ((e.actionMasked == MotionEvent.ACTION_UP) || (e.actionMasked == MotionEvent.ACTION_CANCEL)) {
            isLongClick = false
        }
    }

    override fun onRequestDisallowInterceptTouchEvent(disallowIntercept: Boolean) {
        if (disallowIntercept) {
            isLongClick = false
        }
    }

    override fun onDraw(c: Canvas, view: V) = Unit

    override fun attachToView(viewTouchAdapter: ViewTouchAdapter<V>?) {
        touchAdapter?.removeCallback(this)
        touchAdapter = viewTouchAdapter?.apply {
            addCallback(this@ItemLongClickProcessor)
        }
    }
}

fun interface ItemLongClick {
    fun onLongClick(pos: Int): Boolean
}
