package com.hu.demo.main.works.pdf.sdk

import android.graphics.Rect
import com.google.common.base.Ascii
import java.io.ByteArrayOutputStream
import java.io.Closeable
import java.io.DataOutputStream
import java.io.InputStream
import java.io.OutputStream
import java.util.Locale
import kotlin.random.Random

class PdfDocument(outS: OutputStream) : Closeable {
    private val outS = DataOutputStream(outS)
    private val pageInfos = mutableListOf<PageInfo>()
    private val objInfos = mutableListOf<ObjInfo>()
    private var currentPage: Page? = null
    private val indexQ = IndexNum()

    fun start() {
        Headerd().write(outS)
    }

    fun startPage(pageInfo: PageInfo): Page {
        pageInfo.offset = outS.size().toLong()
        val page = Page(pageInfo, objInfos, outS, indexQ)
        currentPage = page

        return page
    }

    fun finishPage(page: Page?) {
        page ?: return
        if (currentPage == page) {
            pageInfos.add(page.pageInfo)
            currentPage = null
        }
    }

    fun finish() {
        val pagesId = indexQ.getAndInc()

        // 写所有的page
        val pageList = pageInfos.map {
            Objd(
                objInfos,
                Dictionaryd(
                    mapOf(
                        Named("Type") to Named("Page"),
                        Named("Parent") to IndirectRefd(pagesId),
//                        Named("Contents") to Arrayd(listOf()),
                        Named("MediaBox") to Arrayd(with(it.contentRect) { listOf(Numd(left), Numd(top), Numd(right), Numd(bottom)) }),
                        Named("Resources") to Dictionaryd(
                            mapOf(
                                Named("ProcSet") to Arrayd(listOf(Named("PDF"), Named("Text"), Named("ImageB"), Named("ImageC"), Named("ImageI"))),
                                Named("XObject") to Dictionaryd(mapOf(Named("X${it.index}") to IndirectRefd(it.index)))
                            )
                        )
                    )
                ),
                indexQ.getAndInc()
            )
        }

        // 写pages
        val list = pageList.map {
            IndirectRefd(it.index)
        }
        Objd(
            objInfos,
            Dictionaryd(
                mapOf(
                    Named("Type") to Named("Pages"),
                    Named("Count") to Numd(list.size),
                    Named("Kids") to Arrayd(list)
                )
            ),
            pagesId
        ).apply { write(outS) }

        pageList.forEach {
            it.write(outS)
        }

        // 写catelog
        val catalogObjd = Objd(
            objInfos,
            Dictionaryd(
                mapOf(
                    Named("Type") to Named("Catalog"),
                    Named("Pages") to IndirectRefd(pagesId)
                )
            ),
            indexQ.getAndInc()
        ).apply { write(outS) }

        Trailerd(catalogObjd, objInfos).write(outS)
        outS.flush()
    }

    override fun close() {
        outS.close()
    }

    class PageInfo private constructor(
        val pageWidth: Int,
        val pageHeight: Int,
        val contentRect: Rect,
    ) {
        internal var index: Int = 0
        internal var offset: Long = 0

        class Builder(private val pageWidth: Int, private val pageHeight: Int) {
            fun build(): PageInfo {
                return PageInfo(pageWidth, pageHeight, Rect(0, 0, pageWidth, pageHeight))
            }
        }
    }

    class Page internal constructor(
        val pageInfo: PageInfo,
        private val objInfos: MutableList<ObjInfo>,
        private val outS: OutputStream,
        private val indexQ: IndexNum,
    ) {
        fun write(size: Long, inS: InputStream) {
            val index = indexQ.getAndInc()
            // 先写对象，讲page的内容保留
            Objd(
                objInfos,
                Streamd(
                    Dictionaryd(
                        mapOf(
                            Named("Length") to Numd(size),
                            Named("Width") to Numd(pageInfo.pageWidth),
                            Named("Height") to Numd(pageInfo.pageHeight),
//                            Named("Filter") to Named("JPXDecode"),
                            Named("Subtype") to Named("Image"),
                            Named("Type") to Named("XObject"),
                            Named("BitsPerComponent") to Numd(8),
                            Named("ColorSpace") to Named("DeviceRGB"),
                        )
                    )
                ) { inS.copyTo(it) },
                index
            ).apply {
                write(outS)
            }
            pageInfo.index = index
        }
    }

    fun interface IWriter {
        fun write(stream: OutputStream)

        open fun toBytes(): ByteArray {
            return ByteArrayOutputStream().apply {
                write(this)
            }.toByteArray()
        }
    }

    class Headerd() : IWriter {
        override fun write(stream: OutputStream) {
            stream.write("%PDF-1.7".toByteArray())
            stream.write(Ascii.LF.toInt())
            stream.write("%".toByteArray())
            stream.write(Random.nextInt(128, 255))
            stream.write(Random.nextInt(128, 255))
            stream.write(Random.nextInt(128, 255))
            stream.write(Random.nextInt(128, 255))
            stream.write(Ascii.LF.toInt())
        }
    }

    class Trailerd(private val catalogObjd: Objd, private val objInfos: MutableList<ObjInfo>) : IWriter {
        override fun write(stream: OutputStream) {
            val offset = (stream as DataOutputStream).size()
            stream.write("xref ".toByteArray())
            stream.write(Ascii.LF.toInt())
            stream.write("0 ${objInfos.size + 1}".toByteArray())
            stream.write(Ascii.LF.toInt())
            stream.write(String.format(Locale.ROOT, "%010d %05d f", 0, 65535).toByteArray())
            stream.write(Ascii.LF.toInt())
            objInfos.forEach {
                stream.write(String.format(Locale.ROOT, "%010d %05d n", it.offset, 0).toByteArray())
                stream.write(Ascii.LF.toInt())
            }
            stream.write("trailer".toByteArray())
            stream.write(Ascii.LF.toInt())
            Dictionaryd(
                mapOf(
                    Named("Size") to Numd(objInfos.size + 1),
                    Named("Root") to IndirectRefd(catalogObjd.index)
                )
            ).write(stream)
            stream.write("startxref".toByteArray())
            stream.write(Ascii.LF.toInt())
            stream.write(offset.toString().toByteArray())
            stream.write(Ascii.LF.toInt())
            stream.write("%%EOF".toByteArray())
        }
    }


    class Numd(private val value: Number) : IWriter {
        override fun write(stream: OutputStream) {
            stream.write(value.toString().toByteArray())
        }
    }

    class Strd(private val value: String) : IWriter {
        override fun write(stream: OutputStream) {
            stream.write("($value)".toByteArray())
        }
    }

    class Named(private val value: String) : IWriter {
        override fun write(stream: OutputStream) {
            stream.write("/$value".toByteArray())
        }
    }

    class Boold(private val value: Boolean) : IWriter {
        override fun write(stream: OutputStream) {
            stream.write(value.toString().toByteArray())
        }
    }

    class Arrayd(private val array: List<IWriter>) : IWriter {
        override fun write(stream: OutputStream) {
            stream.write("[".toByteArray())
            for ((index, value) in array.withIndex()) {
                stream.write(value.toBytes())
                if (index != array.size - 1) {
                    stream.write(Ascii.SP.toInt())
                }
            }
            stream.write("]".toByteArray())
        }
    }

    class Dictionaryd(private val map: Map<Named, IWriter>) : IWriter {
        override fun write(stream: OutputStream) {
            stream.write(Ascii.LF.toInt())
            stream.write("<<".toByteArray())
            map.onEachIndexed { index, entry ->
                stream.write(entry.key.toBytes())
                stream.write(Ascii.SP.toInt())
                stream.write(entry.value.toBytes())
                if (index != map.size - 1) {
                    stream.write(Ascii.SP.toInt())
                }
            }
            stream.write(">>".toByteArray())
            stream.write(Ascii.LF.toInt())
        }
    }

    class IndirectRefd(private val objNo: Int, private val genNo: Int = 0, private val type: String = "R") : IWriter {
        override fun write(stream: OutputStream) {
            stream.write("$objNo $genNo $type".toByteArray())
        }
    }

    class Objd(private val objInfos: MutableList<ObjInfo>, private val child: IWriter, val index: Int) : IWriter {
        override fun write(stream: OutputStream) {
            objInfos.add(ObjInfo(index, (stream as DataOutputStream).size().toLong()))

            IndirectRefd(index, 0, "obj").write(stream)
            child.write(stream)
            // 结束对象
            stream.write("endobj".toByteArray())
            stream.write(Ascii.LF.toInt())
        }
    }

    class ObjInfo(
        val index: Int,
        val offset: Long,
    )

    class Streamd(private val size: Dictionaryd, private val child: IWriter) : IWriter {
        override fun write(stream: OutputStream) {
            size.write(stream)
            // 写入打开stream
            stream.write("stream".toByteArray())
            stream.write(Ascii.LF.toInt())
            child.write(stream)
            // 写入结束stream
            stream.write(Ascii.LF.toInt())
            stream.write("endstream".toByteArray())
            stream.write(Ascii.LF.toInt())
        }
    }

    class IndexNum {
        private var index: Int = 1

        fun getAndInc(): Int {
            return index++
        }

        fun getSize(): Int {
            return index
        }
    }

}