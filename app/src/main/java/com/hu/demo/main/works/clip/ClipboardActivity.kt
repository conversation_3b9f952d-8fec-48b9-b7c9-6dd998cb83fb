package com.hu.demo.main.works.clip

import android.annotation.SuppressLint
import android.content.ClipData
import android.content.ClipboardManager
import android.content.ContentValues
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.ImageDecoder
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.provider.MediaStore.Files.FileColumns.*
import android.provider.MediaStore.MediaColumns.DISPLAY_NAME
import android.util.Log
import android.view.View
import android.view.View.OnClickListener
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.FileProvider
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.hu.demo.base.provider.DFileProvider
import com.hu.demo.main.R
import com.hu.demo.main.adapter.BaseData
import com.hu.demo.main.adapter.BaseVH
import com.hu.demo.main.adapter.DefaultAdapter
import com.hu.demo.utils.ensureFile
import com.hu.demo.utils.getName
import com.hu.demo.utils.setImageUriAny
import com.hu.demo.base.ui.BaseActivity
import java.io.File

class ClipboardActivity : BaseActivity(), OnClickListener {
    private var parseImageAdapter: ImageAdapter? = null
    private var selectImageAdapter: ImageAdapter? = null
    private var rvImages: RecyclerView? = null
    private var btnSelect: Button? = null
    private var btnCopyToClipboard: Button? = null
    private var rvParseImages: RecyclerView? = null
    private var btnPaste: Button? = null
    private var btnSaveToFile: Button? = null
    private val mediaRegex = Regex("^(image|video)/.*$")

    private val getContentAllMultiLauncher = registerForActivityResult(
        ActivityResultContracts.GetMultipleContents(),
        PickMultiCallback { changeData(it) }
    )

    override fun getLayoutId(): Int {
        return R.layout.activity_clipboard
    }

    override fun initToolbar() {
        super.initToolbar()
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowTitleEnabled(true)
            setDisplayShowHomeEnabled(false)
        }
    }

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        rvImages = findViewById(R.id.rvImages)
        btnSelect = findViewById(R.id.btnSelect)
        btnCopyToClipboard = findViewById(R.id.btnCopyToClipboard)
        rvParseImages = findViewById(R.id.rvParseImages)
        btnPaste = findViewById(R.id.btnPaste)
        btnSaveToFile = findViewById(R.id.btnSaveToFile)
    }

    override fun initEvent(savedInstanceState: Bundle?) {
        super.initEvent(savedInstanceState)
        rvImages?.apply {
            addItemDecoration(DividerItemDecoration(this@ClipboardActivity, RecyclerView.HORIZONTAL))
            adapter = ImageAdapter(this).apply {
                resetData(emptyList())
                selectImageAdapter = this
            }
            layoutManager = LinearLayoutManager(this@ClipboardActivity, RecyclerView.HORIZONTAL, false)
        }
        btnSelect?.setOnClickListener(this)
        btnCopyToClipboard?.setOnClickListener(this)
        rvParseImages?.apply {
            addItemDecoration(DividerItemDecoration(this@ClipboardActivity, RecyclerView.HORIZONTAL))
            adapter = ImageAdapter(this).apply {
                resetData(emptyList())
                parseImageAdapter = this
            }
            layoutManager = LinearLayoutManager(this@ClipboardActivity, RecyclerView.HORIZONTAL, false)
        }
        btnPaste?.setOnClickListener(this)
        btnSaveToFile?.setOnClickListener(this)
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.btnSelect -> {
                getContentAllMultiLauncher.launch(MIME_TYPE_MATCH_ALL)
            }

            R.id.btnCopyToClipboard -> {
                val dataList = selectImageAdapter?.dataList ?: return
                var clipData: ClipData? = null
                dataList.forEach {
                    if (it is UriData) {
                        val uri = it.uri ?: return@forEach
                        val file = File(DFileProvider.cacheFile, contentResolver.getName(uri)!!).ensureFile()
                        val shareUri = FileProvider.getUriForFile(this, DFileProvider.AUTHORITY, file)
                        val currentTime = System.currentTimeMillis()
                        contentResolver.openInputStream(uri)?.use { iStream ->
                            file.outputStream().use { oStream ->
                                iStream.copyTo(oStream)
                            }
                        }
                        Log.e(TAG, "onClick: cost time: ${System.currentTimeMillis() - currentTime}")
                        if (clipData == null) {
                            clipData = ClipData.newUri(contentResolver, "Image", shareUri)
                        } else {
                            clipData!!.addItem(contentResolver, ClipData.Item(shareUri))
                        }
                    }
                }
                clipData?.run {
                    getSystemService(ClipboardManager::class.java).setPrimaryClip(this)
                }
                Toast.makeText(this, "复制成功${clipData?.itemCount}个", Toast.LENGTH_SHORT).show()
            }

            R.id.btnPaste -> {
                val clipData = getSystemService(ClipboardManager::class.java).primaryClip ?: return
                paste(clipData)
            }

            R.id.btnSaveToFile -> {
                val dataList = parseImageAdapter?.dataList ?: return
                val mediaUri = MediaStore.Images.Media.getContentUri(MediaStore.VOLUME_EXTERNAL_PRIMARY)

                var count = 0
                dataList.forEach {
                    if (it is UriData) {
                        val uri = it.uri ?: return@forEach
                        val willInsertCv = ContentValues()
                        willInsertCv.put(MIME_TYPE, contentResolver.getType(uri))
                        willInsertCv.put(DISPLAY_NAME, contentResolver.getName(uri))
                        willInsertCv.put(DATE_ADDED, System.currentTimeMillis() / 1000)
                        willInsertCv.put(RELATIVE_PATH, "DCIM/clip")
                        willInsertCv.put(BUCKET_DISPLAY_NAME, "clip")
                        if (willInsertCv.getAsString(MIME_TYPE)?.startsWith("image/") == true) {
                            willInsertCv.put(MEDIA_TYPE, MEDIA_TYPE_IMAGE)
                        } else if (willInsertCv.getAsString(MIME_TYPE)?.startsWith("video/") == true) {
                            willInsertCv.put(MEDIA_TYPE, MEDIA_TYPE_VIDEO)
                        }
                        willInsertCv.put(IS_PENDING, true)

                        val insertUri = contentResolver.insert(mediaUri, willInsertCv)!!
                        contentResolver.openOutputStream(insertUri)?.use { outputS ->
                            contentResolver.openInputStream(uri)?.use { inputS ->
                                val bitmap = BitmapFactory.decodeStream(inputS)
                                bitmap.compress(Bitmap.CompressFormat.JPEG, 100, outputS)
                                // inputS.copyTo(outputS)
                            }
                        }
                        contentResolver.update(insertUri, ContentValues().apply { put(IS_PENDING, false) }, null, null)
                        count++
                    }
                }
                Toast.makeText(this, "保存成功${count}个", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun decode(uri: Uri, maxWh: Int): Bitmap {
        Log.d(TAG, "decode: start maxWh: $maxWh")
        val lastTime = System.currentTimeMillis()
        val bitmap: Bitmap = ImageDecoder.decodeBitmap(ImageDecoder.createSource(contentResolver, uri)) { decoder, imageInfo, _ ->
            decoder.allocator = ImageDecoder.ALLOCATOR_SOFTWARE
            val scale = minOf(imageInfo.size.width.toFloat() / maxWh, imageInfo.size.height.toFloat() / maxWh)
            decoder.setTargetSampleSize((scale.toInt() shr 1 shl 1).coerceAtLeast(1))
            decoder.setTargetSize((imageInfo.size.width / scale).toInt(), (imageInfo.size.height / scale).toInt())
        }
        Log.d(TAG, "decode: time: ${System.currentTimeMillis() - lastTime}, ${bitmap.width to bitmap.height}")
        return bitmap
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun paste(clipData: ClipData) {
        val pasteImageAdapter = parseImageAdapter ?: return
        val list = mutableListOf<BaseData>()
        for (index in 0 until clipData.itemCount) {
            val item = clipData.getItemAt(index)
            if (item.uri != null && contentResolver.getType(item.uri)?.matches(mediaRegex) == true) {
                list.add(UriData(item.uri))
            } else if (item.text != null) {
                list.add(TextData(item.text))
            }
        }
        pasteImageAdapter.resetData(list)
        pasteImageAdapter.notifyDataSetChanged()

        Toast.makeText(this, "粘贴${list.size}个", Toast.LENGTH_SHORT).show()
        btnSaveToFile?.isEnabled = true
    }

    @Suppress("NotifyDataSetChanged", "UNCHECKED_CAST")
    private fun changeData(obj: Any?) {
        val list = (obj as List<Uri>).mapNotNull {
            if (contentResolver.getType(it)?.matches(mediaRegex) == true) {
                UriData(it)
            } else null
        }
        selectImageAdapter?.resetData(list)
        selectImageAdapter?.notifyDataSetChanged()
        btnCopyToClipboard?.isEnabled = true
        Toast.makeText(this, "选择${list.size}个", Toast.LENGTH_SHORT).show()
    }

    class UriVH(parent: ViewGroup) : BaseVH<UriData>(parent, R.layout.item_clipboard_uri) {
        private val ivImage: ImageView = findViewById(R.id.ivImage)

        override fun bind(extraData: Map<String, Any>, data: UriData, position: Int) {
            ivImage.setImageUriAny(data.uri)
        }
    }

    class TextVH(parent: ViewGroup) : BaseVH<TextData>(parent, R.layout.item_clipboard_text) {
        private val tvText: TextView = findViewById(R.id.tvText)

        override fun bind(extraData: Map<String, Any>, data: TextData, position: Int) {
            tvText.text = data.str
        }
    }

    data class UriData(val uri: Uri?) : BaseData(TYPE_URI)
    data class TextData(val str: CharSequence?) : BaseData(TYPE_TEXT)

    class ImageAdapter(recyclerView: RecyclerView) : DefaultAdapter<BaseData, BaseVH<BaseData>>(recyclerView) {
        @Suppress("UNCHECKED_CAST")
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH<BaseData> {
            return when (viewType) {
                TYPE_URI -> UriVH(parent) as BaseVH<BaseData>
                else -> TextVH(parent) as BaseVH<BaseData>
            }
        }

        override fun onBindViewHolder(holder: BaseVH<BaseData>, position: Int) {
            holder.bind(emptyMap(), dataList[position], position)
        }
    }

    private open class PickMultiCallback(private val resultFunc: (List<Uri>) -> Unit) : ActivityResultCallback<List<Uri>> {
        override fun onActivityResult(result: List<Uri>) {
            if (result.isNullOrEmpty()) {
                Log.d(TAG, "PickMultiCallback.onActivityResult: ")
                return
            }
            Log.d(TAG, "onActivityResult: $result")
            resultFunc(result)
        }
    }

    companion object {
        private const val TAG = "ClipboardActivity"
        private const val MIME_TYPE_MATCH_ALL = "*/*"
        private const val TYPE_URI = 0
        private const val TYPE_TEXT = 1
    }
}