package com.hu.demo.main.works.recyclerview.vhs

import android.view.ViewGroup
import android.widget.TextView
import com.hu.demo.main.R
import com.hu.demo.main.adapter.BaseVH
import com.hu.demo.main.works.recyclerview.datas.RecyclerTitleData

class RecyclerTitleVH(parent: ViewGroup) : BaseVH<RecyclerTitleData>(parent, R.layout.item_recycler_title) {
    private val tvName = findViewById<TextView>(R.id.tvName)
    private val tvSize = findViewById<TextView>(R.id.tvSize)
    override fun bind(extraData: Map<String, Any>, data: RecyclerTitleData, position: Int) {
        tvName.text = data.name
        tvSize.text = data.size.toString()
    }
}