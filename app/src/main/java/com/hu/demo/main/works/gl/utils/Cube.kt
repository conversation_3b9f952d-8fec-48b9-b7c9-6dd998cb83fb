package com.hu.demo.main.works.gl.utils

import java.io.BufferedReader
import java.io.InputStream
import java.io.InputStreamReader
import java.nio.FloatBuffer
import java.util.regex.Pattern


object Cube {
    private const val KEY_TITLE = "TITLE"

    private const val KEY_SIZE = "LUT_3D_SIZE"

    fun read(stream: InputStream): CubeLut? {
        BufferedReader(InputStreamReader(stream)).useLines {
            val iterator = it.iterator()
            var title: String? = null
            var size: Int? = null

            var next: String = iterator.next()
            var split: List<String> = next.split(Regex("\\s+"), 2)
            if (split.getOrNull(0) == KEY_TITLE) {
                title = split[1].replace(Regex("^\"|\"$"), "")
            }
            next = iterator.next()
            split = next.split(Pattern.compile(" "), 2)
            if (split.getOrNull(0) == KEY_SIZE) {
                size = split[1].toInt()
            }
            if (title == null || size == null) {
                return null
            }

            val cubeLut = CubeLut(title, size)
            while (iterator.hasNext()) {
                next = iterator.next()
                if (next.isBlank()) {
                    continue
                }
                val splitArr = next.split(Regex("\\s+")).map { it.toFloat() }
                cubeLut.lutTable.put(splitArr.toFloatArray())
            }
            return cubeLut
        }
    }
}

class CubeLut(val title: String, val size: Int) {
    val lutTable: FloatBuffer = FloatBuffer.allocate(size * size * size * 3)
}