package com.hu.demo.main.works.fileparse.box.jpg

import com.hu.demo.main.works.fileparse.box.Tree
import com.hu.demo.utils.ByteOrderedDataInputStream
import com.hu.demo.utils.contentHexString
import com.hu.demo.utils.getLow
import com.hu.demo.utils.getSenior

internal class DhtSection(parent: Tree) : SizeSection(parent) {
    var htInfo: Byte? = null
    val htNo: Byte?
        get() = htInfo?.getLow()
    val htType: Byte
        get() = (htInfo?.getSenior()!!.toInt() and 0b1).toByte()
    val htTypeStr: String
        get() {
            return when (htType.toInt()) {
                0 -> "(DC)"
                1 -> "(AC)"
                else -> ""
            }
        }
    var htKeyTable: ByteArray? = null
    var htValue: ByteArray? = null

    override fun read(bis: ByteOrderedDataInputStream) {
        super.read(bis)
        htInfo = bis.nReadByte()
        htKeyTable = bis.nReadBytes(16)
        htValue = bis.nReadBytes(htKeyTable!!.sum())
    }

    override fun toString(): String {
        return "${super.toString()} DHT(huffman) \nhtInfo:$htInfo(htNo:$htNo, htType:$htType$htTypeStr), \nhtKeyTable:${htKeyTable?.contentHexString()}, \nhtValue:${htValue?.contentHexString()}"
    }

    fun buildHuffTable(dhtInfo: DhtInfo) {
        val table = dhtInfo.huffTable
        var sum = 0
        for (index in 0 until 16) {
            val t = dhtInfo.bitTable[index]
            sum += t
        }
        dhtInfo.huffTable = HuffLookupTable(sum)

        var code = 0
        var idx = 1
        var weightIdx = 0
        for (index in 0..16) {
            val n = dhtInfo.bitTable[index]

            if (n > 0) idx++

            for (index2 in 0..n) {
                val weight = dhtInfo.valueTable[weightIdx]
                dhtInfo.huffTable!!.code[weightIdx] = code.toShort()
                dhtInfo.huffTable!!.len[weightIdx] = (index + 1).toByte()
                dhtInfo.huffTable!!.weight[weightIdx] = weight
                code++
                weightIdx++
            }
            if (idx > 1) code = code shl 1
        }
    }

    class DhtInfo(bitTable: ByteArray, valueTable: ByteArray) {
        var info: Byte? = null
        var bitTable: ByteArray = bitTable.copyOf()
        var valueTable: ByteArray = valueTable.copyOf()
        var huffTable: HuffLookupTable? = null
    }

    class HuffLookupTable(var size: Int) {
        val code = ShortArray(size)
        val len = ByteArray(size)
        val weight = ByteArray(size)
    }
}
