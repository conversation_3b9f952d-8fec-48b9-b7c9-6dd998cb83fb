package com.hu.demo.main.works.fileparse.box.heif

import com.hu.demo.main.works.fileparse.box.Box
import com.hu.demo.main.works.fileparse.box.Tree
import com.hu.demo.utils.ByteOrderedDataInputStream
import com.hu.demo.utils.toByteString
import com.hu.demo.utils.toIntStringArray

internal class FileTypeBox(parent: Tree) : Box("ftyp", 1, parent) {
    var majorBrand: Int? = null
        private set
    var minorBrand: Int? = null
        private set

    var majorBrands: IntArray? = null
        private set

    override fun readInner(bis: ByteOrderedDataInputStream) {
        super.readInner(bis)
        majorBrand = bis.nReadInt()
        minorBrand = bis.nReadInt()
        val brandsSize = ((realSize - 16) / 4).toInt()
        majorBrands = bis.nReadInts(brandsSize)
    }

    override fun fork(parent: Tree): Box {
        return FileTypeBox(parent)
    }

    override fun toString(): String {
        return "${super.toString()}, majorBrand:${majorBrand?.toByteString()}, " +
                "minorBrand:$minorBrand, majorBrands:${majorBrands?.toIntStringArray().contentToString()}"
    }
}