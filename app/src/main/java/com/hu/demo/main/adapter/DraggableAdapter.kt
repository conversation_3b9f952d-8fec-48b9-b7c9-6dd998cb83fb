package com.hu.demo.main.adapter

import android.animation.ValueAnimator
import android.view.View
import androidx.core.animation.doOnEnd
import androidx.core.animation.doOnStart
import androidx.recyclerview.widget.RecyclerView
import com.hu.demo.main.works.recyclerview.drag.IDragCallback
import com.hu.demo.main.works.recyclerview.vhs.IDraggable
import java.util.Collections

abstract class DraggableAdapter<DATA : BaseData, VH : BaseVH<DATA>>(
    recyclerView: RecyclerView,
) : DefaultAdapter<DATA, VH>(recyclerView), IDragCallback {
    override fun canMove(pos: Int): Boolean {
        return isEditMode && ((recyclerView.findViewHolderForLayoutPosition(pos) as? IDraggable)?.canDrag() ?: false)
    }

    override fun onDragStart(dragView: View, from: Int) {
        recyclerView.getChildViewHolder(dragView)?.also { vh ->
            vh.setIsRecyclable(false)
        }
    }

    override fun onDragMove(from: Int, to: Int) {
        if (from < to) {
            for (index in from until to) {
                Collections.swap(dataList, index, index + 1)
            }
        } else {
            for (index in from downTo to + 1) {
                Collections.swap(dataList, index, index - 1)
            }
        }
        notifyItemMoved(from, to)
    }

    override fun onDragEnd(dragView: View, to: Int) {
        recyclerView.getChildViewHolder(dragView)?.also { vh ->
            val translationX = vh.itemView.translationX
            val translationY = vh.itemView.translationY
            ValueAnimator.ofFloat(1f, 0f).apply {
                addUpdateListener {
                    vh.itemView.translationX = translationX * (it.animatedValue as Float)
                    vh.itemView.translationY = translationY * (it.animatedValue as Float)
                }
                doOnStart {
                    vh.setIsRecyclable(false)
                }
                doOnEnd {
                    vh.setIsRecyclable(true)
                }
            }.start()
        }
    }

    companion object {
        private const val TAG = "DraggableAdapter"
    }
}
