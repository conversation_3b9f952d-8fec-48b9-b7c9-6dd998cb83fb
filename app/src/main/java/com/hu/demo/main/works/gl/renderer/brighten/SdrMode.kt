/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - SdrMode.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/11/20
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * huca<PERSON><PERSON>@Apps.Gallery		2024/11/20		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.renderer.brighten

import android.graphics.ColorSpace
import android.os.Build
import androidx.graphics.surface.SurfaceControlCompat
import com.hu.demo.main.works.gl.renderer.brighten.ISubHdrMode.Companion.DATASPACE_BT2020_HLG
import com.hu.demo.main.works.gl.renderer.brighten.ISubHdrMode.Companion.DATASPACE_BT2020_PQ
import com.hu.demo.main.works.gl.renderer.brighten.ISubHdrMode.Companion.DATASPACE_DISPLAY_P3
import com.hu.demo.main.works.gl.renderer.brighten.ISubHdrMode.Companion.DATASPACE_LINEAR_DISPLAY_P3
import com.hu.demo.main.works.gl.renderer.brighten.ISubHdrMode.Companion.DATASPACE_LINEAR_SRGB
import com.hu.demo.main.works.gl.renderer.brighten.ISubHdrMode.Companion.DATASPACE_P3_HLG
import com.hu.demo.main.works.gl.renderer.brighten.ISubHdrMode.Companion.DATASPACE_SRGB
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_COLOR_SPACE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_DEVICE_HDR_SDR_RATIO
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.getKey
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.PROCEDURAL_NP
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.STABLE_NP
import com.hu.demo.main.works.gl.renderer.param.RenderArgs
import com.hu.demo.utils.ColorSpaceExt.BT2020_HLG
import com.hu.demo.utils.ColorSpaceExt.BT2020_PQ
import com.hu.demo.utils.ColorSpaceExt.DISPLAY_P3
import com.hu.demo.utils.ColorSpaceExt.DISPLAY_P3_GAMMA_2_2
import com.hu.demo.utils.ColorSpaceExt.DISPLAY_P3_HLG
import com.hu.demo.utils.ColorSpaceExt.HDR_DISPLAY_P3
import com.hu.demo.utils.ColorSpaceExt.HDR_DISPLAY_P3_GAMMA_2_2
import com.hu.demo.utils.ColorSpaceExt.HDR_SRGB
import com.hu.demo.utils.ColorSpaceExt.HDR_SRGB_GAMMA_2_2
import com.hu.demo.utils.ColorSpaceExt.LINEAR_DISPLAY_P3
import com.hu.demo.utils.ColorSpaceExt.LINEAR_SRGB
import com.hu.demo.utils.ColorSpaceExt.SRGB
import com.hu.demo.utils.ColorSpaceExt.SRGB_GAMMA_2_2

class SdrMode : ISubHdrMode {
    override var inWarn: Boolean = false

    override fun startShow() = Unit

    override fun checkHandle(renderArgs: RenderArgs): Boolean {
        return true
    }

    override fun render(
        renderArgs: RenderArgs,
        surfaceControl: SurfaceControlCompat,
        transaction: SurfaceControlCompat.Transaction,
    ) {
        when (renderArgs.get<ColorSpace>(PROCEDURAL_NP.getKey(KEY_COLOR_SPACE))) {
            BT2020_HLG -> transaction.setDataSpace(surfaceControl, DATASPACE_BT2020_HLG)
            BT2020_PQ -> transaction.setDataSpace(surfaceControl, DATASPACE_BT2020_PQ)
            DISPLAY_P3_HLG -> transaction.setDataSpace(surfaceControl, DATASPACE_P3_HLG)
            DISPLAY_P3, DISPLAY_P3_GAMMA_2_2, HDR_DISPLAY_P3, HDR_DISPLAY_P3_GAMMA_2_2 -> {
                transaction.setDataSpace(surfaceControl, DATASPACE_DISPLAY_P3)
            }

            SRGB, SRGB_GAMMA_2_2, HDR_SRGB, HDR_SRGB_GAMMA_2_2 -> transaction.setDataSpace(surfaceControl, DATASPACE_SRGB)
            LINEAR_DISPLAY_P3 -> transaction.setDataSpace(surfaceControl, DATASPACE_LINEAR_DISPLAY_P3)
            LINEAR_SRGB -> transaction.setDataSpace(surfaceControl, DATASPACE_LINEAR_SRGB)
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            val deviceHdrSdrRatio = renderArgs.get<Float>(STABLE_NP.getKey(KEY_DEVICE_HDR_SDR_RATIO)) ?: 1.0f
            transaction.setExtendedRangeBrightness(surfaceControl, deviceHdrSdrRatio, 1.0f)
        }
    }

    override fun unrender(
        renderArgs: RenderArgs,
        surfaceControl: SurfaceControlCompat,
        transaction: SurfaceControlCompat.Transaction,
    ) {
        when (renderArgs.get<ColorSpace>(PROCEDURAL_NP.getKey(KEY_COLOR_SPACE))) {
            BT2020_HLG -> transaction.setDataSpace(surfaceControl, DATASPACE_BT2020_HLG)
            BT2020_PQ -> transaction.setDataSpace(surfaceControl, DATASPACE_BT2020_PQ)
            DISPLAY_P3_HLG -> transaction.setDataSpace(surfaceControl, DATASPACE_P3_HLG)
            DISPLAY_P3, DISPLAY_P3_GAMMA_2_2, HDR_DISPLAY_P3, HDR_DISPLAY_P3_GAMMA_2_2 -> {
                transaction.setDataSpace(surfaceControl, DATASPACE_DISPLAY_P3)
            }

            SRGB, SRGB_GAMMA_2_2, HDR_SRGB, HDR_SRGB_GAMMA_2_2 -> transaction.setDataSpace(surfaceControl, DATASPACE_SRGB)
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            val deviceHdrSdrRatio = renderArgs.get<Float>(STABLE_NP.getKey(KEY_DEVICE_HDR_SDR_RATIO)) ?: 1.0f
            transaction.setExtendedRangeBrightness(surfaceControl, deviceHdrSdrRatio, 1.0f)
        }
    }

    override fun stopShow() = Unit

    companion object {
        private const val TAG = "SdrMode"
    }
}