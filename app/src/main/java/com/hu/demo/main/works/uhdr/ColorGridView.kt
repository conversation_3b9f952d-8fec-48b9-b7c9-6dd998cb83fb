package com.hu.demo.main.works.uhdr

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.*
import android.os.Build
import android.util.AttributeSet
import android.view.View

class ColorGridView(context: Context, attrs: AttributeSet?) : View(context, attrs) {

    val density: Float
    val dp: Int.() -> Float

    init {
        setWillNotDraw(false)
        isClickable = true
        density = resources.displayMetrics.density
        dp = { this * density }
        isClickable = true
        setOnClickListener {
            invalidate()
        }
    }

    private fun Int.toMaxColor(colorspace: ColorSpace): Long {
        val red = (Color.red(this) / 255f) * colorspace.getMaxValue(0)
        val green = (Color.green(this) / 255f) * colorspace.getMaxValue(1)
        val blue = (Color.blue(this) / 255f) * colorspace.getMaxValue(2)
        val alpha = Color.alpha(this) / 255f
        return Color.pack(red, green, blue, alpha, colorspace)
    }

    @SuppressLint("DrawAllocation")
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        val paint = Paint()
        paint.isDither = true
        paint.isAntiAlias = true
        paint.textSize = 10.dp()
        paint.textAlign = Paint.Align.LEFT

        val textHeight = 10.dp()
        val widthGap = 5.dp()
        val colWidth = width / colorSpaces.size.toFloat() - 5.dp()
        val rowHeight = minOf((height - textHeight) / 4f, colWidth)

        val dest = Rect(0, 0, colWidth.toInt(), rowHeight.toInt())

        for ((colIndex, colorspace) in colorSpaces.withIndex()) {
            canvas.save()
            canvas.translate(colIndex * (colWidth + widthGap), textHeight)

            paint.color = Color.LTGRAY
            canvas.drawText(colorspace.second, 0f, 1f, paint)

            arrayOf(Color.WHITE, Color.RED, Color.BLUE, Color.GREEN).forEach {
                paint.setColor(it.toMaxColor(colorspace.first))
                canvas.drawRect(dest, paint)
                canvas.translate(0f, rowHeight)
            }
            canvas.restore()
        }
    }

    companion object {
        val colorSpaces = buildList {
            add(ColorSpace.get(ColorSpace.Named.SRGB) to "sRGB")
            add(ColorSpace.get(ColorSpace.Named.DISPLAY_P3) to "DISPLAY_P3")
            add(ColorSpace.get(ColorSpace.Named.EXTENDED_SRGB) to "EXTEND_SRGB")
            add(ColorSpace.get(ColorSpace.Named.BT2020) to "BT2020")
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                add(ColorSpace.get(ColorSpace.Named.BT2020_PQ) to "BT2020_PQ")
                add(ColorSpace.get(ColorSpace.Named.BT2020_HLG) to "BT2020_HLG")
            }
        }
    }
}