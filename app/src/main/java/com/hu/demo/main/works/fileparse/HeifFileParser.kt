package com.hu.demo.main.works.fileparse

import android.util.Log
import com.hu.demo.main.works.fileparse.box.Box
import com.hu.demo.main.works.fileparse.box.Tree
import com.hu.demo.main.works.fileparse.box.heif.ExifBlock
import com.hu.demo.main.works.fileparse.box.heif.FileTypeBox
import com.hu.demo.main.works.fileparse.box.heif.HandlerBox
import com.hu.demo.main.works.fileparse.box.heif.HeifFile
import com.hu.demo.main.works.fileparse.box.heif.ItemInfoBox
import com.hu.demo.main.works.fileparse.box.heif.ItemInfoEntryBox
import com.hu.demo.main.works.fileparse.box.heif.ItemLocationBox
import com.hu.demo.main.works.fileparse.box.heif.MediaDataBox
import com.hu.demo.main.works.fileparse.box.heif.MetaBox
import com.hu.demo.main.works.fileparse.box.heif.XmpBlock
import com.hu.demo.utils.ByteOrderedDataInputStream
import com.hu.demo.utils.mark
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.FlowCollector
import kotlinx.coroutines.flow.buffer
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import java.io.DataInputStream

class HeifFileParser : IFileParser {
    override fun canParse(iStream: IStreamSource): Boolean {
        val fis = DataInputStream(iStream.newStream().buffered())
        val buffer = fis.mark(CHECK_SIZE) {
            ByteArray(CHECK_SIZE).apply(::readFully)
        }
        fis.close()
        return checkStartWith(buffer)
    }

    private fun buildTree(): List<Box> {
        return buildList {
            val heifFile = HeifFile(-1)
            FileTypeBox(heifFile).apply(::add)
            MetaBox(heifFile).apply(::add).apply {
                HandlerBox(this).also(::add)
                ItemLocationBox(this).apply(::add)
                ItemInfoBox(this).apply(::add).apply {
                    ItemInfoEntryBox(this).apply(::add)
                }
            }
            MediaDataBox(heifFile)
        }
    }

    override suspend fun parse(iStream: IStreamSource): Flow<CharSequence> {
        val flow = flow {
            runCatching {
                val mp4File = HeifFile(Long.MAX_VALUE)
                val tree = buildTree()

                // 第一次读取：解析MP4结构
                ByteOrderedDataInputStream(iStream.newStream().buffered()).use {
                    readMp4(it, tree, mp4File)
                }

                // 第二次读取：解析EXIF数据
                ByteOrderedDataInputStream(iStream.newStream().buffered()).use {
                    readExif(it, mp4File)
                }

                // 第三次读取：解析XMP数据
                ByteOrderedDataInputStream(iStream.newStream().buffered()).use {
                    readXmp(it, mp4File)
                }
            }.onFailure {
                Log.e(TAG, "parse: ", it)
            }
        }.flowOn(Dispatchers.IO).catch {
            Log.e(TAG, "parse: ", it)
        }.buffer()
        return flow
    }

    private suspend fun FlowCollector<CharSequence>.readMp4(bis: ByteOrderedDataInputStream, checkTree: List<Box>, parent: Tree) {
        while (bis.mark(1) { read() != -1 }) {
            readBox(bis, checkTree, parent)
        }
    }

    private suspend fun FlowCollector<CharSequence>.readBox(bis: ByteOrderedDataInputStream, checkTree: List<Box>, parent: Tree) {
        val checkBox = bis.mark(4 + 4 + 14) {
            Box("unknown", parent.level + 1).apply { read(this@mark) }
        }
        val box = checkTree.find {
            it.nodeName == checkBox.type && it.level == checkBox.level
        }?.fork(parent) ?: Box(checkBox.nodeName, checkBox.level, parent)
        box.read(bis)
        val levelStr = buildLevelStr(box.level)
        emit("$levelStr$box \n")

        while (box.boxNeedReadChild || !box.boxReadOver) {
            readBox(bis, checkTree, box)
        }
        box.skipRemain(bis)
    }

    private suspend fun FlowCollector<CharSequence>.readExif(bis: ByteOrderedDataInputStream, parent: Tree) {
        try {
            // 读取 exif
            val rootParent = parent.rootTree

            val metaBox = (rootParent as? HeifFile)?.children?.find {
                it.nodeName == "meta"
            } as? MetaBox

            val exifInfe = (metaBox?.children?.find {
                it.nodeName == "iinf"
            } as? ItemInfoBox)?.children?.find {
                (it as ItemInfoEntryBox).itemType == "Exif"
            } as? ItemInfoEntryBox

            val exifIlocItem = (metaBox?.children?.find {
                it.nodeName == "iloc"
            } as? ItemLocationBox)?.items?.find {
                it.itemId == exifInfe?.itemId
            }

            if (exifIlocItem != null && exifIlocItem.extentItem?.isNotEmpty() == true) {
                val extentOffset = exifIlocItem.extentItem!![0].extentOffset
                val extentLength = exifIlocItem.extentItem!![0].extentLength

                if (extentOffset != null && extentLength != null && extentLength > 0) {
                    // 跳转到EXIF数据位置（新的流已经从文件开始）
                    bis.skipFully(extentOffset)

                    val exifBlock = ExifBlock(exifIlocItem, metaBox.level + 1, metaBox)
                    exifBlock.read(bis)
                    val exifLevel = buildLevelStr(metaBox.level + 1)
                    emit("$exifLevel$exifBlock \n")
                } else {
                    emit("EXIF data location invalid: offset=$extentOffset, length=$extentLength\n")
                }
            } else {
                emit("EXIF data not found in HEIF file\n")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error reading EXIF data", e)
            emit("Error reading EXIF: ${e.message}\n")
        }
    }

    private suspend fun FlowCollector<CharSequence>.readXmp(bis: ByteOrderedDataInputStream, parent: Tree) {
        // 读取 exif
        val rootParent = parent.rootTree

        val metaBox = (rootParent as? HeifFile)?.children?.find { it.nodeName == "meta" } as? MetaBox

        val xmpInfe = (metaBox?.children?.find {
            it.nodeName == "iinf"
        } as? ItemInfoBox)?.children?.find {
            (it as ItemInfoEntryBox).itemType == "mime"
        } as? ItemInfoEntryBox

        val xmpIlocItem = (metaBox?.children?.find {
            it.nodeName == "iloc"
        } as? ItemLocationBox)?.items?.find {
            it.itemId == xmpInfe?.itemId
        }

        if (xmpIlocItem != null) {
            bis.skipFully(xmpIlocItem.extentItem!!.get(0).extentOffset!!)
            val xmpBlock = XmpBlock(xmpIlocItem, metaBox.level + 1, metaBox)
            xmpBlock.read(bis)
            val exifLevel = buildLevelStr(metaBox.level + 1)
            emit("$exifLevel$xmpBlock \n")
        }
    }

    private fun buildLevelStr(level: Int): String {
        return buildString {
            for (index in 1 until level) {
                append(" > ")
            }
        }
    }

    private fun checkStartWith(cur: ByteArray): Boolean {
        for (index in 0 until 4) {
            if (cur[index + 4] != HEIF_SIGNATURE[index]) {
                return false
            }
        }
        for (index in 0 until 4) {
            if (cur[index + 8] != HEIC_BRAND[index]) {
                return false
            }
        }
        return true
    }

    companion object {
        private const val TAG = "HeifFileParser"
        val HEIF_SIGNATURE = "ftyp".toByteArray(Charsets.US_ASCII)
        val HEIC_BRAND = "heic".toByteArray(Charsets.US_ASCII)
        private const val CHECK_SIZE = 20

        private const val BYTE_ALIGN_II: Int = 0x49492A00 // II: Intel order

        private const val BYTE_ALIGN_MM: Int = 0x4d4d002A // MM: Motorola order
    }
}