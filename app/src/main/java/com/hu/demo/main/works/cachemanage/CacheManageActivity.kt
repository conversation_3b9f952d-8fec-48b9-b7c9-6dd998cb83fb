package com.hu.demo.main.works.cachemanage

import android.os.Bundle
import com.hu.demo.base.ui.BaseActivity
import com.hu.demo.main.R

class CacheManageActivity : BaseActivity() {

    override fun getLayoutId(): Int {
        return R.layout.activity_cache_manage
    }

    override fun initToolbar() {
        super.initToolbar()
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowTitleEnabled(true)
            setDisplayShowHomeEnabled(false)
        }
    }

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
    }

    override fun initEvent(savedInstanceState: Bundle?) {
        super.initEvent(savedInstanceState)
    }

}