/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - RenderExt.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/10/24
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery		2024/10/24		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.renderer

import android.graphics.ColorSpace
import androidx.annotation.IntDef
import com.hu.demo.main.works.gl.renderer.GamutMode.Companion.MODE_BT709
import com.hu.demo.main.works.gl.renderer.GamutMode.Companion.MODE_GAMMA_2_2
import com.hu.demo.main.works.gl.renderer.GamutMode.Companion.MODE_HLG
import com.hu.demo.main.works.gl.renderer.GamutMode.Companion.MODE_LINEAR
import com.hu.demo.main.works.gl.renderer.GamutMode.Companion.MODE_PQ
import com.hu.demo.main.works.gl.renderer.GamutMode.Companion.MODE_SRGB
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_TEXTURE_POOL
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.getKey
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.STABLE_NP
import com.hu.demo.main.works.gl.renderer.param.RenderArgs
import com.hu.demo.main.works.gl.texture.ITexture
import com.hu.demo.utils.ColorSpaceExt.ADOBE_SRGB
import com.hu.demo.utils.ColorSpaceExt.BT2020_HLG
import com.hu.demo.utils.ColorSpaceExt.BT2020_PQ
import com.hu.demo.utils.ColorSpaceExt.BT709
import com.hu.demo.utils.ColorSpaceExt.DISPLAY_P3
import com.hu.demo.utils.ColorSpaceExt.DISPLAY_P3_GAMMA_2_2
import com.hu.demo.utils.ColorSpaceExt.DISPLAY_P3_HLG
import com.hu.demo.utils.ColorSpaceExt.DISPLAY_P3_PQ
import com.hu.demo.utils.ColorSpaceExt.HDR_DISPLAY_P3
import com.hu.demo.utils.ColorSpaceExt.HDR_DISPLAY_P3_GAMMA_2_2
import com.hu.demo.utils.ColorSpaceExt.HDR_SRGB
import com.hu.demo.utils.ColorSpaceExt.HDR_SRGB_GAMMA_2_2
import com.hu.demo.utils.ColorSpaceExt.SRGB
import com.hu.demo.utils.ColorSpaceExt.SRGB_GAMMA_2_2

/**
 * 将纹理添加到当前绘制阶段的缓存池中。在绘制阶段中的渲染器的纹理需要调用此方法
 *
 * @param renderArgs 存在TexturePool的渲染器参数
 *
 * @return 返回T的当前值
 */
fun <T : ITexture> T.addToPool(renderArgs: RenderArgs): T = apply {
    renderArgs.get<MutableSet<ITexture>>(STABLE_NP.getKey(KEY_TEXTURE_POOL))?.add(this)
}

@Retention(AnnotationRetention.SOURCE)
@IntDef(
    MODE_LINEAR,
    MODE_HLG,
    MODE_PQ,
    MODE_SRGB,
    MODE_BT709,
    MODE_GAMMA_2_2,
)
annotation class GamutMode {
    companion object {
        internal const val MODE_LINEAR = 0
        internal const val MODE_HLG = 1
        internal const val MODE_PQ = 2
        internal const val MODE_SRGB = 3
        internal const val MODE_BT709 = 4
        internal const val MODE_GAMMA_2_2 = 5
    }
}

/**
 * 获取shader对应的曲线模式
 *
 * @param this@getGamutMode 输入的纹理对应色域
 *
 * @return 返回模式
 */
@GamutMode
internal fun ColorSpace.getGamutMode(): Int {
    return when (this) {
        DISPLAY_P3_HLG, BT2020_HLG -> MODE_HLG
        DISPLAY_P3_PQ, BT2020_PQ -> MODE_PQ
        SRGB, HDR_SRGB, DISPLAY_P3, HDR_DISPLAY_P3 -> MODE_SRGB
        BT709 -> MODE_BT709
        ADOBE_SRGB, DISPLAY_P3_GAMMA_2_2, SRGB_GAMMA_2_2, HDR_DISPLAY_P3_GAMMA_2_2, HDR_SRGB_GAMMA_2_2 -> MODE_GAMMA_2_2
        else -> MODE_LINEAR
    }
}