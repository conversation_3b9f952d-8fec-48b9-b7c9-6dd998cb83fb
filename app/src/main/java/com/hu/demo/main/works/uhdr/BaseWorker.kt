package com.hu.demo.main.works.uhdr

import android.content.Context
import androidx.work.Worker
import androidx.work.WorkerParameters

abstract class BaseWorker(context: Context, workerParams: WorkerParameters) : Worker(context, workerParams) {
    companion object {
        val INPUT_FILE = "inputFile"
        val INPUT_DIR = "inputDir"
        val OUTPUT_DIR = "outputDir"
        val OUTPUT_FILE = "outputFile"
        val MAX_SLICE_COUNT = "maxSliceCount"
        val OUTPUT_COUNT = "outputCount"
        val COLOR_SPACE = "colorSpace"
    }
}