/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - ProgramShader.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2025/01/07
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * huca<PERSON><PERSON>@Apps.Gallery		2025/01/07		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.shader

data class ProgramShader(
    val vertexGlShader: GlShader = GlShader(),
    val fragmentGlShader: GlShader = GlShader(),
) {
    fun combine(programShader: ProgramShader) {
        vertexGlShader.combine(programShader.vertexGlShader)
        fragmentGlShader.combine(programShader.fragmentGlShader)
    }

    fun isEmpty(): Boolean {
        return vertexGlShader.isEmpty() && fragmentGlShader.isEmpty()
    }

    fun clean() {
        vertexGlShader.clean()
        fragmentGlShader.clean()
    }
}