package com.hu.demo.main.works.clip

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.RectF
import android.text.TextPaint
import android.util.AttributeSet
import android.view.View
import kotlin.properties.Delegates

class ClipboardView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    var text by Delegates.observable<CharSequence?>(null) { _, _, _ ->
        invalidate()
    }

    var bitmap :Bitmap? = null
        set(value) {
            field = value
            invalidate()
        }

    private val cacheSrcRect = Rect(0, 0, 0, 0)
    private val cacheDstRectF = RectF(50f, 200f, 1000f, 1500f)
    private val textPaint = TextPaint(Paint.ANTI_ALIAS_FLAG).apply {
        textSize = 100f
    }
    private val bitmapPaint = Paint(Paint.ANTI_ALIAS_FLAG)

    override fun onDraw(canvas: Canvas) {
        text?.also {
            canvas.drawText(it, 0, it.length, 0f, 100f, textPaint)
        }
        bitmap?.also {
            cacheSrcRect.set(0, 0, it.width, it.height)
            canvas.drawBitmap(it, cacheSrcRect, cacheDstRectF, bitmapPaint)
        }
    }
}