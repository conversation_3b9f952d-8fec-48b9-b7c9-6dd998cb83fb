/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - SlidingSelectHelper.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2022/01/13
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery		2022/01/13		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.recyclerview.slidingselect

import android.graphics.Canvas
import android.graphics.PointF
import android.util.Log
import android.view.MotionEvent
import android.view.ViewConfiguration
import android.view.ViewGroup
import com.hu.demo.main.R
import com.hu.demo.main.works.recyclerview.longclick.LongClickCallback
import com.hu.demo.main.works.recyclerview.view.AutoScroll
import com.hu.demo.main.works.recyclerview.view.IAttachView
import com.hu.demo.main.works.recyclerview.view.IItemTouchProcessor
import com.hu.demo.main.works.recyclerview.view.ViewTouchAdapter
import kotlin.math.abs
import kotlin.math.min
import kotlin.math.pow
import kotlin.math.sqrt

class SlidingSelectProcessor<V : ViewGroup>(
    private val slidingCallback: ISlidingCallback?
) : IItemTouchProcessor<V>, IAttachView<V>, LongClickCallback {
    /**
     * 按下时的当前位置
     */
    private val downP = PointF(0f, 0f)

    /**
     * 进入划选时的当前触摸位置
     */
    private val enterSlidingP = PointF(0f, 0f)

    /**
     * 上次的触摸位置
     */
    private val lastP = PointF(0f, 0f)

    private val autoScroll: AutoScroll = AutoScroll {
        dealSlidingSelect()
    }

    private val longPressRunnable = Runnable {

    }

    /**
     * 第一个选中的Adapter的position
     */
    private var firstItemPos = -1

    /**
     * 上一个选中的Adapter的position
     */
    private var lastItemPos = -1

    private var slidingMode = MODE_SLIDING_NONE

    /**
     * - 累计滑动的距离，用于判断是否要拦截事件。
     * - 当进入了划选时，会重置distance，并且事件会转移到onTouchEvent中；
     * - 当滑动的距离已经超过了[slidingTouchSlop]时，就说明不是划选，则不再拦截事件；
     */
    private var distance = 0f
    private var scaledTouchSlop = -1
    private var slidingTouchSlop = -1
    private var touchAdapter: ViewTouchAdapter<V>? = null
    private var defaultAutoScrollH = 0
    private var autoScrollHeight = 0
    private var slidingStartCalled = false
    private var selfDisallowIntercept = false

    override fun onLongClick(e: MotionEvent, pos: Int): Boolean {
        return (firstItemPos.takeIf {
            it >= 0
        }?.let {
            slidingCallback?.onSlidingStart(it)
            slidingStartCalled = true
            true
        } ?: false).apply {
            Log.d(TAG, "onLongClick: is called $this")
        }
    }

    override fun onInterceptTouchEvent(e: MotionEvent): Boolean {
        val touchAdapter = touchAdapter ?: return false
        // 当满足以下条件，则不拦截事件
        // 1. 触摸的手指不是第一个
        // 2. action事件不是down、up、cancel其中一个，并且在down事件时并没有找到firstItemPos，并且滑动的距离已经超过了划选拦截的最大距离touchSlopRadius
        if ((e.actionIndex != 0)
            || ((e.actionMasked == MotionEvent.ACTION_MOVE) && ((firstItemPos == -1) || (distance > slidingTouchSlop)))
        ) {
            return false
        }
        when (e.actionMasked) {
            MotionEvent.ACTION_DOWN -> {
                dealActionDown(e)
            }
            MotionEvent.ACTION_MOVE -> {
                if (dealCheckSliding(e)) {
                    selfDisallowIntercept = true
                    touchAdapter.view.requestDisallowInterceptTouchEvent(true)
                    selfDisallowIntercept = false
                    return true
                }
            }
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                reset(touchAdapter.view)
            }
        }
        return false
    }

    override fun onTouchEvent(e: MotionEvent) {
        val view = touchAdapter?.view ?: return
        if (e.actionIndex != 0) {
            return
        }
        when (e.actionMasked) {
            MotionEvent.ACTION_MOVE -> {
                lastP.set(e.x, e.y)
                dealSlidingSelect()

                dealAutoScroll()
                view.parent?.requestDisallowInterceptTouchEvent(true)
            }
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                reset(view)
            }
        }
    }

    override fun onDraw(c: Canvas, view: V) = Unit

    /**
     * 处理ACTION_DOWN事件，方法主要处理按下的位置记录和长按事件
     *
     * @param e MotionEvent
     */
    private fun dealActionDown(e: MotionEvent) {
        val touchAdapter = touchAdapter ?: return
        downP.set(e.x, e.y)
        lastP.set(e.x, e.y)
        touchAdapter.findChildView(e.x, e.y, false)?.let {
            touchAdapter.findChildPosition(it)
        }?.takeIf { index ->
            touchAdapter.isSelectable(index)
        }?.apply {
            firstItemPos = this
            Log.d(TAG, "onInterceptTouchEvent: detect sliding select. down is $firstItemPos")
        }

        // 处理长按事件
        touchAdapter.view.apply {
            removeCallbacks(longPressRunnable)
            postDelayed(longPressRunnable, ViewConfiguration.getLongPressTimeout().toLong())
        }
    }

    /**
     * 检查是否可以进入划选
     *
     * @param e [MotionEvent]
     *
     * @return 如果可以进入划选事件，则返回true，否则返回false
     */
    private fun dealCheckSliding(e: MotionEvent): Boolean {
        val view = touchAdapter?.view ?: return false
        val dx = abs(e.x - downP.x)
        val dy = abs(e.y - downP.y)
        if (dx > dy && (dx > scaledTouchSlop) && (slidingCallback?.canSliding() == true)) {
            // 进入了划选，移除长按
            view.removeCallbacks(longPressRunnable)

            distance = 0f
            lastItemPos = firstItemPos
            enterSlidingP.set(e.x, e.y)
            autoScrollHeight = defaultAutoScrollH.coerceAtMost(
                min(enterSlidingP.y, (view.height - enterSlidingP.y)).toInt()
            )
            Log.d(TAG, "onInterceptTouchEvent: can sliding select. first is $firstItemPos")
            if (!slidingStartCalled) {
                slidingCallback.onSlidingStart(firstItemPos)
            }
            view.parent.requestDisallowInterceptTouchEvent(true)
            lastP.set(e.x, e.y)
            return true
        } else {
            distance += sqrt((e.x - lastP.x).pow(2) + (e.y - lastP.y).pow(2))
            lastP.set(e.x, e.y)

            if (distance > scaledTouchSlop) {
                // 滑动距离超过了限制，取消长按
                view.removeCallbacks(longPressRunnable)
            }
            Log.d(TAG, "onInterceptTouchEvent: distance: $distance")
        }
        return false
    }

    /**
     * 处理划选
     *
     */
    private fun dealSlidingSelect() {
        val touchAdapter = touchAdapter ?: return
        touchAdapter.findChildView(lastP.x, lastP.y, true)?.let {
            touchAdapter.findChildPosition(it)
        }?.takeIf {
            it >= 0
        }?.apply {
            if (this > lastItemPos) {
                val selectRange = (lastItemPos + if (slidingMode == MODE_SLIDING_DOWN || slidingMode == MODE_SLIDING_NONE) 1 else 0)..this
                Log.d(TAG, "dealSlidingSelect: down range: $selectRange")
                // 处理向下划选
                when {
                    firstItemPos > this -> {
                        slidingCallback?.onSlidingChange(selectRange, false, autoScroll.isAutoScrolling)
                    }
                    firstItemPos in selectRange -> {
                        slidingCallback?.onSlidingChange(selectRange.first until firstItemPos, false, autoScroll.isAutoScrolling)
                        slidingCallback?.onSlidingChange(firstItemPos..selectRange.last, true, autoScroll.isAutoScrolling)
                    }
                    else -> {
                        slidingCallback?.onSlidingChange(selectRange, true, autoScroll.isAutoScrolling)
                    }
                }
                slidingMode = MODE_SLIDING_DOWN
            } else if (this < lastItemPos) {
                val selectRange =
                    (lastItemPos - if (slidingMode == MODE_SLIDING_UP || slidingMode == MODE_SLIDING_NONE) 1 else 0) downTo this
                Log.d(TAG, "dealSlidingSelect: up range: $selectRange")
                // 处理向上划选
                when {
                    firstItemPos < this -> {
                        slidingCallback?.onSlidingChange(selectRange, false, autoScroll.isAutoScrolling)
                    }
                    firstItemPos in selectRange -> {
                        slidingCallback?.onSlidingChange(selectRange.first downTo firstItemPos + 1, false, autoScroll.isAutoScrolling)
                        slidingCallback?.onSlidingChange(firstItemPos downTo selectRange.last, true, autoScroll.isAutoScrolling)
                    }
                    else -> {
                        slidingCallback?.onSlidingChange(selectRange, true, autoScroll.isAutoScrolling)
                    }
                }
                slidingMode = MODE_SLIDING_UP
            } else {
                // 处理当前item的选择
            }
            lastItemPos = this
        }
    }

    /**
     * 处理自动滚动
     */
    private fun dealAutoScroll() {
        val view = touchAdapter?.view ?: return
        if (((enterSlidingP.y - lastP.y) > scaledTouchSlop) && (lastP.y < autoScrollHeight)) {
            // 处理上滑自动滚动
            autoScroll.setVelocityScale((lastP.y - autoScrollHeight) / autoScrollHeight.toFloat())
            autoScroll.start(view)
        } else if (((lastP.y - enterSlidingP.y) > scaledTouchSlop) && (lastP.y > (view.height - autoScrollHeight))) {
            // 处理下滑自动滚动
            autoScroll.setVelocityScale((autoScrollHeight - (view.height - lastP.y)) / autoScrollHeight.toFloat())
            autoScroll.start(view)
        } else {
            // 处理停止
            autoScroll.cancel()
        }
    }

    private fun reset(view: ViewGroup?) {
        Log.d(TAG, "reset")
        if (firstItemPos != -1) {
            slidingCallback?.onSlidingEnd()
        }
        firstItemPos = -1
        lastItemPos = -1
        downP.set(0f, 0f)
        enterSlidingP.set(0f, 0f)
        lastP.set(0f, 0f)
        distance = 0f
        autoScroll.cancel()
        slidingMode = MODE_SLIDING_NONE
        slidingStartCalled = false
        view?.removeCallbacks(longPressRunnable)
        view?.parent?.requestDisallowInterceptTouchEvent(false)
    }

    override fun onRequestDisallowInterceptTouchEvent(disallowIntercept: Boolean) {
        if (!selfDisallowIntercept && disallowIntercept) {
            touchAdapter?.view?.apply {
                reset(this)
            }
        }
    }

    override fun attachToView(viewTouchAdapter: ViewTouchAdapter<V>?) {
        touchAdapter?.apply {
            reset(view)
            removeCallback(this@SlidingSelectProcessor)
        }
        touchAdapter = viewTouchAdapter?.apply {
            addCallback(this@SlidingSelectProcessor)
            scaledTouchSlop = ViewConfiguration.get(view.context).scaledTouchSlop
            slidingTouchSlop = scaledTouchSlop * 2
            defaultAutoScrollH = view.resources.getDimensionPixelSize(R.dimen.toolbar_min_height)
            autoScrollHeight = defaultAutoScrollH
        }
    }

    companion object {
        private const val TAG = "SlidingSelectHelper"
        private const val MODE_SLIDING_NONE = 0
        private const val MODE_SLIDING_DOWN = 1
        private const val MODE_SLIDING_UP = 2
    }
}


interface ISlidingCallback : ISlidingDataCallback {

    /**
     * 判定是否可以划选，此回调在划选开始时判断
     *
     * @return 是否可以划选
     */
    fun canSliding(): Boolean
}

interface ISlidingDataCallback {

    /**
     * 滑选操作开始时触发
     *
     * @param position 滑选操作第一个 Item 索引
     */
    fun onSlidingStart(position: Int)

    /**
     * 滑选操作滑过区域变化时触发
     *
     * @param range 滑选操作的 range
     * @param correct 是否正在滚动，快速滚动过程性能消耗较大，需做优化，如关闭checkbox动画
     */
    fun onSlidingChange(range: IntProgression, correct: Boolean, autoScrolling: Boolean)

    /**
     * 划选操作结束时触发
     */
    fun onSlidingEnd()
}