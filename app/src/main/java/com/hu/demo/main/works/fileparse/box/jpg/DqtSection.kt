package com.hu.demo.main.works.fileparse.box.jpg

import com.hu.demo.main.works.fileparse.box.Tree
import com.hu.demo.utils.ByteOrderedDataInputStream
import com.hu.demo.utils.getLow
import com.hu.demo.utils.getSenior

internal class DqtSection(parent: Tree) : SizeSection(parent) {
    var qtInfo: Byte? = null
    val qtNo: Byte?
        get() {
            val info = qtInfo ?: return null
            return info.getLow()
        }
    val qtNoStr: String
        get() {
            return when (qtNo?.toInt()) {
                0 -> "(light)"
                1 -> "(chroma)"
                else -> ""
            }
        }
    val qtAccuracy: Byte?
        get() {
            val info = qtInfo ?: return null
            return info.getSenior()
        }
    var qt: ByteArray? = null

    override fun read(bis: ByteOrderedDataInputStream) {
        super.read(bis)
        qtInfo = bis.nReadByte()
        val accuracy = if (qtAccuracy!!.toInt() == 0) 1 else 2
        qt = bis.nReadBytes(64 * accuracy)
    }

    override fun toString(): String {
        return "${super.toString()} DQT\nqtInfo:$qtInfo(qtNo:$qtNo$qtNoStr, qtAccuracy:$qtAccuracy), \nqt:${qt?.contentToString()}"
    }
}
