package com.hu.demo.main

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.work.Data
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import com.hu.demo.main.works.uhdr.BaseWorker.Companion.INPUT_DIR
import com.hu.demo.main.works.uhdr.BaseWorker.Companion.OUTPUT_DIR
import com.hu.demo.main.works.uhdr.UhdrSpiltWorker

class ActionReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent) {
        Log.d(TAG, "onReceive: $intent")
        if (intent.action == TYPE_UHDR_SPLIT) {
            val inputData = Data.Builder()
                .putString(INPUT_DIR, intent.getStringExtra(INPUT_DIR))
                .putString(OUTPUT_DIR, intent.getStringExtra(OUTPUT_DIR))
                .build()
            val workRequest = OneTimeWorkRequestBuilder<UhdrSpiltWorker>()
                .setInputData(inputData)
                .build()
            val operation = WorkManager.getInstance(context).enqueue(workRequest)
            operation.result
        }
    }

    companion object {
        private const val TAG = "OutReceiver"
        private const val TYPE_UHDR_SPLIT = "com.hu.demo.splitUhdr"
    }
}