package com.hu.demo.main.works.fileparse.box.jpg

import com.hu.demo.main.works.fileparse.IRead
import com.hu.demo.main.works.fileparse.box.Tree
import com.hu.demo.utils.ByteOrderedDataInputStream
import com.hu.demo.utils.contentHexString

internal abstract class Section(parent: Tree?) : JpgTree("section", parent?.level?.plus(1) ?: 1, parent), IRead {
    var marker: ByteArray? = null
    override val realSize: Long = 2 // marker size

    override fun read(bis: ByteOrderedDataInputStream) {
        marker = bis.nReadBytes(2)
    }

    open fun skipRemain(bis: ByteOrderedDataInputStream) = Unit

    override fun toString(): String {
        return "${super.toString()}\nmarker: ${marker?.contentHexString()}"
    }
}
