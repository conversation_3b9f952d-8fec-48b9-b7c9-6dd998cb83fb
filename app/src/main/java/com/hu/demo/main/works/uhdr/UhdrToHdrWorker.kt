package com.hu.demo.main.works.uhdr

import android.content.Context
import android.graphics.ColorSpace
import android.graphics.ImageDecoder
import android.util.Log
import androidx.core.graphics.decodeBitmap
import androidx.work.Data
import androidx.work.WorkerParameters
import com.hu.demo.main.util.toRgbaFileN
import com.hu.demo.main.works.gl.egl.GLContext
import com.hu.demo.main.works.gl.renderer.addToPool
import com.hu.demo.main.works.gl.renderer.helper.RenderHelper
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_TEXTURE_POOL
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.getKey
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.STABLE_NP
import com.hu.demo.main.works.gl.renderer.param.RenderArgs
import com.hu.demo.main.works.gl.texture.BitmapTexture
import com.hu.demo.main.works.gl.texture.GainmapTexture
import com.hu.demo.main.works.gl.texture.ITexture
import com.hu.demo.utils.ApiLevelUtil
import com.hu.demo.utils.ColorSpaceExt.BT2020_HLG
import com.hu.demo.utils.ColorSpaceExt.DISPLAY_P3_HLG
import com.hu.demo.utils.toRgbaFile
import java.io.File
import java.io.FileFilter

class UhdrToHdrWorker(context: Context, workerParams: WorkerParameters) : BaseWorker(context, workerParams) {
    override fun doWork(): Result {
        return toHdr()
    }

    private fun toHdr(): Result {
        Log.d(TAG, "toHdr: start")
        val inputImagesDir = inputData.getString(INPUT_DIR)?.let { File(it) } ?: run {
            Log.d(TAG, "toHdr: end 1")
            return Result.failure()
        }
        val outputImagesDir = inputData.getString(OUTPUT_DIR)?.let { File(it) } ?: inputImagesDir
        outputImagesDir.mkdirs()
        val imageFiles = inputImagesDir.listFiles(FileFilter {
            it.path.endsWith(".jpg") && !it.isHidden
        }) ?: return Result.failure()

        var count: Int = 0
        Log.d(TAG, "toHdr: scan image : ${imageFiles.size}")
        GLContext.runOnGL {
            val renderArgs = RenderHelper.obtainArgs()
            for ((index, file) in imageFiles.withIndex()) {
                val fileName = file.name.substringBefore(".jpg")
                val bitmap = ImageDecoder.createSource(file).decodeBitmap { _, _ ->
                    allocator = ImageDecoder.ALLOCATOR_SOFTWARE
                }
                reuseTexture(renderArgs)
                if (ApiLevelUtil.isAtLeastAndroidU() && bitmap.hasGainmap()) {
                    val sdrTexture = BitmapTexture(bitmap).addToPool(renderArgs)
                    val gainTexture = GainmapTexture(bitmap.gainmap!!).addToPool(renderArgs)
                    val combineHdr = RenderHelper.combineHdr(
                        renderArgs,
                        sdrTexture,
                        gainTexture,
                        bitmap.gainmap!!.displayRatioForFullHdr
                    ).let { tex ->
                        getColorSpace(inputData.getString(COLOR_SPACE))?.let { cs ->
                            RenderHelper.transformColorSpace(renderArgs, tex, cs)
                        } ?: tex
                    }

                    val outBitmap = combineHdr.toBitmap()

                    val lastTime = System.currentTimeMillis()
                    Log.d(TAG, "toHdr: start save $file, $index")

                    outBitmap.toRgbaFile(File(outputImagesDir, "${fileName}_hdr.rgba"))
                    outBitmap.toRgbaFileN(File(outputImagesDir, "${fileName}_hdr.rgba"))

                    File(outputImagesDir, "${fileName}_to_hdr_info.txt").writer().buffered().use {
                        it.appendLine("config=${combineHdr.texConfig}")
                        it.appendLine("colorSpace=${combineHdr.colorSpace.name}")
                        it.appendLine("width=${combineHdr.width}")
                        it.appendLine("height=${combineHdr.height}")
                    }
                    Log.d(TAG, "toHdr: end save $file, $index cost: ${System.currentTimeMillis() - lastTime}ms")
                    count++
                }
            }
            RenderHelper.release(renderArgs)
        }

        Log.d(TAG, "toHdr: end 2")
        return Result.success(Data.Builder().putAll(mapOf(OUTPUT_COUNT to count)).build())
    }

    /**
     * 对上次渲染请求过程中的纹理执行复用
     */
    private fun reuseTexture(renderArgs: RenderArgs) {
        renderArgs.require<MutableSet<ITexture>>(STABLE_NP.getKey(KEY_TEXTURE_POOL)).removeAll {
            it.reuse()
            true
        }
    }

    companion object {
        private const val TAG = "UhdrToHdrWorker"

        private fun getColorSpace(csName: String?): ColorSpace? {
            return when (csName) {
                "Display-P3 HLG" -> DISPLAY_P3_HLG
                "Bt2020 HLG" -> BT2020_HLG
                else -> null
            }
        }
    }
}