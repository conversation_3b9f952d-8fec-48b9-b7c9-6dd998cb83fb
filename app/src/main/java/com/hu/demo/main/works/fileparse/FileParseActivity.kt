package com.hu.demo.main.works.fileparse

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.View
import android.view.View.OnClickListener
import android.view.ViewGroup
import android.widget.Button
import android.widget.TextView
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.contract.ActivityResultContract
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.adobe.xmp.XMPMetaFactory
import com.adobe.xmp.options.PropertyOptions
import com.adobe.xmp.options.SerializeOptions
import com.hu.demo.main.R
import com.hu.demo.main.adapter.BaseData
import com.hu.demo.main.adapter.BaseVH
import com.hu.demo.main.adapter.DefaultAdapter
import com.hu.demo.base.ui.BaseActivity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class FileParseActivity : BaseActivity(), OnClickListener {
    private var btnSelectImg: Button? = null
    private var rvRecycler: RecyclerView? = null
    private var logAdapter: LogAdapter? = null
    private val pickTypeResultContract = PickTypeResultContract()
    private val pickImageLauncher = registerForActivityResult(
        pickTypeResultContract,
        PickOnlyCallback { changeData(it) }
    )

    override fun getLayoutId(): Int {
        return R.layout.activity_file_parse
    }

    override fun initToolbar() {
        super.initToolbar()
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowTitleEnabled(true)
            setDisplayShowHomeEnabled(false)
        }
    }

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        btnSelectImg = findViewById(R.id.btnSelectImg)
        rvRecycler = findViewById(R.id.rv_list)
    }

    override fun initEvent(savedInstanceState: Bundle?) {
        super.initEvent(savedInstanceState)
        btnSelectImg?.setOnClickListener(this)
        rvRecycler?.apply {
            addItemDecoration(DividerItemDecoration(this@FileParseActivity, RecyclerView.VERTICAL))
            adapter = LogAdapter(this).apply {
                resetData(emptyList())
                logAdapter = this
            }
            layoutManager = LinearLayoutManager(this@FileParseActivity, RecyclerView.VERTICAL, false)
        }

//        TestObj.test()
    }

    object TestObj {
        const val GOOGLE_GCAMERA_NAMESPACE = "http://ns.google.com/photos/1.0/camera/"
        const val GOOGLE_PHOTOS_CONTAINER_NAMESPACE = "http://ns.google.com/photos/1.0/container/"
        const val CAMERA_PREFIX = "GCamera"
        const val CONTAINER_PREFIX = "Container"

        fun test() {
            XMPMetaFactory.getSchemaRegistry().registerNamespace(GOOGLE_GCAMERA_NAMESPACE, CAMERA_PREFIX)
            XMPMetaFactory.getSchemaRegistry().registerNamespace(GOOGLE_PHOTOS_CONTAINER_NAMESPACE, CONTAINER_PREFIX)
            val options = SerializeOptions()
            options.useCompactFormat = true
            options.omitPacketWrapper = true

            val xmpMeta = XMPMetaFactory.create()
            xmpMeta.setProperty(GOOGLE_GCAMERA_NAMESPACE, "vStr", "aaaaa")
            xmpMeta.appendArrayItem(GOOGLE_GCAMERA_NAMESPACE, "vArr", PropertyOptions().apply { isArrayOrdered = true }, null, null)
            xmpMeta.insertArrayItem(GOOGLE_GCAMERA_NAMESPACE, "vArr", 1, "value1")
            val xmp = XMPMetaFactory.serializeToString(xmpMeta, options)

            Log.e(TAG, "test: $xmp")
        }
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.btnSelectImg -> {
                pickImageLauncher.launch(MIME_TYPE)
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun changeData(obj: Any?) {
        lifecycleScope.launch(Dispatchers.Default) {
            closeLog()
            contentResolver.openFileDescriptor(obj as Uri, "r")?.use {
                FileParser.getParser(it.fileDescriptor)?.parse(it.fileDescriptor)?.onEach { line ->
                    Log.d(TAG, "changeData: $line")
                    addLog(LogData(line))
                }?.collect()
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private suspend fun closeLog() = withContext(Dispatchers.Main) {
        logAdapter?.resetData(emptyList())
        logAdapter?.notifyDataSetChanged()
    }

    private suspend fun addLog(logData: LogData) = withContext(Dispatchers.Main) {
        logAdapter?.apply {
            dataList.add(logData)
            notifyItemInserted(dataList.lastIndex)
        }
    }

    class LogVH(parent: ViewGroup) : BaseVH<LogData>(parent, R.layout.item_sensor_log) {
        private val tvItem: TextView = findViewById(R.id.tv_item)

        override fun bind(extraData: Map<String, Any>, data: LogData, position: Int) {
            tvItem.text = data.name
        }
    }

    data class LogData(val name: CharSequence?) : BaseData(TYPE_DEFAULT)

    class LogAdapter(recyclerView: RecyclerView) : DefaultAdapter<BaseData, BaseVH<BaseData>>(recyclerView) {
        @Suppress("UNCHECKED_CAST")
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH<BaseData> {
            return LogVH(parent) as BaseVH<BaseData>
        }

        override fun onBindViewHolder(holder: BaseVH<BaseData>, position: Int) {
            holder.bind(emptyMap(), dataList[position], position)
        }
    }

    private open class PickOnlyCallback(private val resultFunc: (Uri) -> Unit) : ActivityResultCallback<Uri?> {
        override fun onActivityResult(result: Uri?) {
            result ?: run {
                Log.d(TAG, "PickOnlyCallback.onActivityResult: ")
                return
            }
            Log.d(TAG, "onActivityResult: $result")
            resultFunc(result)
        }
    }

    private class PickTypeResultContract : ActivityResultContract<String, Uri?>() {
        override fun createIntent(context: Context, input: String): Intent {
            return Intent(Intent.ACTION_PICK).setType(input)
        }

        override fun parseResult(resultCode: Int, intent: Intent?): Uri? {
            return if (resultCode != Activity.RESULT_OK) null else intent?.data
        }
    }

    companion object {
        private const val TAG = "FileParseActivity"
        private const val MIME_TYPE = "*/*"
        private const val TYPE_DEFAULT = 0
    }
}