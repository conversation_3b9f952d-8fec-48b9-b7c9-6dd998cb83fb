/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - CombineMode.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/10/14
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery		2024/10/14		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.renderer.brighten

import android.annotation.SuppressLint
import android.graphics.ColorSpace
import androidx.graphics.surface.SurfaceControlCompat
import com.hu.demo.main.works.gl.renderer.brighten.ISubHdrMode.Companion.DATASPACE_CDISPLAY_P3
import com.hu.demo.main.works.gl.renderer.brighten.ISubHdrMode.Companion.DATASPACE_SCRGB
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_COLOR_SPACE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_DESIRED_RATIO
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_DEVICE_HDR_SDR_RATIO
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.getKey
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.PROCEDURAL_NP
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.STABLE_NP
import com.hu.demo.main.works.gl.renderer.param.RenderArgs
import com.hu.demo.utils.ColorSpaceExt
import com.hu.demo.utils.ColorSpaceExt.HDR_DISPLAY_P3
import com.hu.demo.utils.ColorSpaceExt.HDR_DISPLAY_P3_GAMMA_2_2
import com.hu.demo.utils.ColorSpaceExt.HDR_SRGB
import com.hu.demo.utils.ColorSpaceExt.HDR_SRGB_GAMMA_2_2

/**
 * 单图HDR情况下，提亮信息下发模式
 */
class CombineImageOnlyHdrMode : ISubHdrMode {
    override var inWarn: Boolean = false

    override fun startShow() = Unit

    /**
     * 检查是否要处理当前的显示模式，满足单图HDR显示的要求：
     * 1. 设备支持UHDR显示；
     * 2. 管线返回的图片是UHDR图片；
     * 3. 图片与视频混合，图片的混合参数大于0.5
     *
     * @param renderArgs 渲染参数
     *
     * @return 返回检查结果，true表示需要当前模式处理，false不能使用当前模式处理
     */
    override fun checkHandle(renderArgs: RenderArgs): Boolean {
        return true
    }

    @SuppressLint("NewApi")
    override fun render(renderArgs: RenderArgs, surfaceControl: SurfaceControlCompat, transaction: SurfaceControlCompat.Transaction) {
        val deviceHdrSdrRatio = renderArgs.get<Float>(STABLE_NP.getKey(KEY_DEVICE_HDR_SDR_RATIO)) ?: 1.0f
        val desiredRatio = renderArgs.get<Float>(STABLE_NP.getKey(KEY_DESIRED_RATIO)) ?: 1.0f

        when (renderArgs.get<ColorSpace>(PROCEDURAL_NP.getKey(KEY_COLOR_SPACE))) {
            HDR_DISPLAY_P3, HDR_DISPLAY_P3_GAMMA_2_2 -> transaction.setDataSpace(surfaceControl, DATASPACE_CDISPLAY_P3)
            HDR_SRGB, HDR_SRGB_GAMMA_2_2 -> transaction.setDataSpace(surfaceControl, DATASPACE_SCRGB)
        }
        transaction.setExtendedRangeBrightness(surfaceControl, deviceHdrSdrRatio, desiredRatio)
    }

    @SuppressLint("NewApi")
    override fun unrender(renderArgs: RenderArgs, surfaceControl: SurfaceControlCompat, transaction: SurfaceControlCompat.Transaction) {
        val deviceHdrSdrRatio = renderArgs.get<Float>(STABLE_NP.getKey(KEY_DEVICE_HDR_SDR_RATIO)) ?: 1.0f

        if (renderArgs.get<ColorSpace>(STABLE_NP.getKey(KEY_COLOR_SPACE)) == ColorSpaceExt.DISPLAY_P3) {
            transaction.setDataSpace(surfaceControl, DATASPACE_CDISPLAY_P3)
        } else {
            transaction.setDataSpace(surfaceControl, DATASPACE_SCRGB)
        }
        transaction.setExtendedRangeBrightness(surfaceControl, 1.0f, 1.0f)
    }

    override fun stopShow() = Unit

    override fun toString(): String {
        return TAG
    }

    companion object {
        private const val TAG = "CombineImageHdrMode"
    }
}