package com.hu.demo.main.works.gl

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.graphics.SurfaceTexture
import android.hardware.HardwareBuffer
import android.media.MediaFormat
import android.media.MediaMetadataRetriever
import android.net.Uri
import android.opengl.Matrix
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.util.Size
import android.view.Display
import android.view.Surface
import android.view.View
import android.view.View.OnClickListener
import android.widget.Button
import android.widget.ImageButton
import android.widget.RadioButton
import android.widget.RadioGroup
import android.widget.SeekBar
import android.widget.SeekBar.OnSeekBarChangeListener
import android.widget.TextView
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.contract.ActivityResultContract
import androidx.annotation.RequiresApi
import androidx.graphics.opengl.egl.EGLConfigAttributes
import androidx.lifecycle.lifecycleScope
import com.hu.demo.base.ui.BaseActivity
import com.hu.demo.main.R
import com.hu.demo.main.works.gl.renderer.image.ImageRendererGroup
import com.hu.demo.main.works.gl.renderer.normal.Renderer
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_COLOR_SPACE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_DESIRED_RATIO
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_DEVICE_HDR_SDR_RATIO
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_GAINMAP_TEXTURE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_IMAGE_TEXTURE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_MIX_RATIO
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_VIDEO_EXTRA_MATRIX
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_VIDEO_TEXTURE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.getKey
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.STABLE_NP
import com.hu.demo.main.works.gl.renderer.video.VideoRendererGroup
import com.hu.demo.main.works.gl.texture.BitmapTexture
import com.hu.demo.main.works.gl.texture.GainmapTexture
import com.hu.demo.main.works.gl.texture.TexConfig
import com.hu.demo.main.works.gl.utils.EditingEglSpec
import com.hu.demo.utils.ColorSpaceExt
import com.hu.demo.utils.decodeImage
import com.oplus.gallery.foundation.opengl2.texture.OesRawTexture
import com.oplus.tblplayer.Constants
import com.oplus.tblplayer.IMediaPlayer
import com.oplus.tblplayer.TBLPlayerManager
import com.oplus.tblplayer.config.PlayerConfiguration
import kotlinx.coroutines.launch
import java.util.function.Consumer

@RequiresApi(Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
class GlActivity : BaseActivity(), OnClickListener, RadioGroup.OnCheckedChangeListener, OnSeekBarChangeListener2 {
    private var player: IMediaPlayer? = null
    private var glView: EditingSurfaceView? = null
    private var sbSeek: SeekBar? = null
    private var btnSelectImg: Button? = null
    private var btnSelectVideo: Button? = null
    private var ibPlay: ImageButton? = null
    private var tvSdrHdrRatio: TextView? = null
    private var rbBrightnessGroup: RadioGroup? = null
    private val pickTypeResultContract = PickTypeResultContract()
    private val pickImageLauncher = registerForActivityResult(
        pickTypeResultContract,
        PickOnlyCallback { changeImageData(it) }
    )
    private val pickVideoLauncher = registerForActivityResult(
        pickTypeResultContract,
        PickOnlyCallback { changeVideoData(it) }
    )
    private var percent: Float = 0f
    private var hdrSdrRatio: Float = 1.0f
    private var desiredRatio: Float = 1.0f
    private val hdrSdrListener = Consumer<Display> { display ->
        hdrSdrRatio = display?.hdrSdrRatio ?: 1.0f
        updateModeInfoDisplay()
        glView?.setDataAndRender(STABLE_NP.getKey(KEY_DEVICE_HDR_SDR_RATIO), hdrSdrRatio)
    }

    override fun getLayoutId(): Int {
        return R.layout.activity_gl_show
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        if (display?.isHdrSdrRatioAvailable == true) {
            display?.registerHdrSdrRatioChangedListener({ it.run() }, hdrSdrListener)
        }
        val list = listOf<String>()
        list.toTypedArray()
    }

    override fun initToolbar() {
        super.initToolbar()
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowTitleEnabled(true)
            setDisplayShowHomeEnabled(false)
        }
    }

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        window.colorMode = ActivityInfo.COLOR_MODE_WIDE_COLOR_GAMUT
        rbBrightnessGroup = findViewById(R.id.rbBrightnessGroup)
        glView = findViewById(R.id.glView)
        tvSdrHdrRatio = findViewById(R.id.tvSdrHdrRatio)
        sbSeek = findViewById(R.id.sbSeek)
        btnSelectImg = findViewById(R.id.btnSelectImg)
        btnSelectVideo = findViewById(R.id.btnSelectVideo)
        ibPlay = findViewById(R.id.ibPlay)
    }

    override fun initEvent(savedInstanceState: Bundle?) {
        super.initEvent(savedInstanceState)
        sbSeek?.setOnSeekBarChangeListener(this)
        btnSelectImg?.setOnClickListener(this)
        btnSelectVideo?.setOnClickListener(this)
        ibPlay?.setOnClickListener(this)
        rbBrightnessGroup?.setOnCheckedChangeListener(this)
        initRenderer()
        initPlayer()
        rbBrightnessGroup?.check(R.id.opengl_brightness_100)
    }

    private fun initPlayer() {
        val playerConfiguration = PlayerConfiguration.Builder()
            .setLowMemoryModeEnabled(true)
            .setHighPerformanceEnabled(true)
            .setExtractorMode(Constants.SOURCE_EXTRACTOR_MODE_ALL)
            .build()
        player = TBLPlayerManager.createPlayer(this, playerConfiguration)
        player?.setOnPreparedListener {
            Log.d(TAG, "prepare: is prepared")
            it.start()
        }
        player?.setOnErrorListener { _, i, i2, s ->
            Log.e(TAG, "onError: what: $i, extra: $i2, $s")
            true
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun initRenderer() {
        glView?.apply {
            initGlRenderer(
                EditingEglSpec(),
                {
                    loadConfig(EGLConfigAttributes.RGBA_1010102)!!
                }
            )

            initBufferRenderer()

            glView?.setDataAndRender(STABLE_NP.getKey(KEY_MIX_RATIO), sbSeek!!.progress.toFloat() / sbSeek!!.max.toFloat())

            // 设置屏幕色域
            setData(STABLE_NP.getKey(KEY_COLOR_SPACE), if (window.isWideColorGamut) ColorSpaceExt.DISPLAY_P3 else ColorSpaceExt.SRGB)

            // 添加渲染节点
            changeNode {
                it.removeChildren()
                it.addChild(Renderer.new(ImageRendererGroup::class.java))
                it.addChild(Renderer.new(VideoRendererGroup::class.java))
            }
        }
    }

    private val cache = mutableSetOf<HardwareBuffer?>()
    override fun onClick(v: View) {
        when (v.id) {
            R.id.btnSelectImg -> {
                pickImageLauncher.launch(MIME_TYPE_IMAGE)
            }

            R.id.btnSelectVideo -> {
                pickVideoLauncher.launch(MIME_TYPE_VIDEO)
            }

            R.id.ibPlay -> {
                if (player?.isPlaying == true) {
                    pause()
                } else {
                    play()
                }
            }
        }
    }

    private fun play() {
        if (player?.isPlayable != true) {
            Log.e(TAG, "onClick: current is not playable!")
            return
        }
        if (player?.isStop == true) {
            player?.reset()
        }
        player?.start()
        ibPlay?.setImageResource(R.drawable.ic_pause)

    }

    private fun pause() {
        player?.pause()
        ibPlay?.setImageResource(R.drawable.ic_play)
    }

    override fun onProgressChanged(seekBar: SeekBar, progress: Int, fromUser: Boolean) {
        glView?.setDataAndRender(STABLE_NP.getKey(KEY_MIX_RATIO), progress.toFloat() / seekBar.max.toFloat())
    }

    override fun onCheckedChanged(group: RadioGroup, checkedId: Int) {
        lifecycleScope.launch {
            val percent = group.findViewById<RadioButton>(checkedId).text.toString().toInt() / 100.0f
            <EMAIL> = percent
            calcDisiredRatio()
        }
    }

    private suspend fun calcDisiredRatio() {
        val gainmapTexture = glView?.getData<GainmapTexture>(STABLE_NP.getKey(KEY_GAINMAP_TEXTURE))
        desiredRatio = maxOf(1.0f, (gainmapTexture?.gainmap?.displayRatioForFullHdr?.let { it * percent }) ?: 1.0f)
        glView?.setDataAndRender(STABLE_NP.getKey(KEY_DESIRED_RATIO), desiredRatio)
    }

    private fun updateModeInfoDisplay() {
        lifecycleScope.launch {
            Log.d(TAG, "HDR/SDR changed $hdrSdrRatio")
            val colormode = window.colorMode
            val mode = "ColorMode:" + when (colormode) {
                ActivityInfo.COLOR_MODE_DEFAULT -> "SDR | Ratio = "

                ActivityInfo.COLOR_MODE_HDR -> "HDR | Ratio = "

                else -> "Unknown | Ratio = "
            } + "$hdrSdrRatio"
            tvSdrHdrRatio?.text = mode
        }
    }

    @SuppressLint("HalfFloat")
    private fun changeImageData(obj: Any?) {
        lifecycleScope.launch {
            val bitmap = (obj as Uri).decodeImage(this@GlActivity, 8000)
            glView?.setDatasAndRender(mapOf(
                STABLE_NP.getKey(KEY_IMAGE_TEXTURE) to BitmapTexture(bitmap),
                STABLE_NP.getKey(KEY_GAINMAP_TEXTURE) to bitmap.gainmap?.let { GainmapTexture(it) }
            ))
            calcDisiredRatio()
        }
    }

    private fun changeVideoData(obj: Any?) {
        val retriever = MediaMetadataRetriever()
        retriever.setDataSource(this, obj as Uri)
        val rotation = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_ROTATION)?.toInt() ?: 0
        val size = retriever.let {
            val width = it.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH)?.toInt() ?: 0
            val height = it.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT)?.toInt() ?: 0
            if (rotation / 180 == 0) {
                Size(width, height)
            } else {
                Size(height, width)
            }
        }
        val colorStandard = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_COLOR_STANDARD)?.toInt()
        val colorTransfer = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_COLOR_TRANSFER)?.toInt()
        val colorSpace = if (colorStandard == MediaFormat.COLOR_STANDARD_BT2020 && colorTransfer == MediaFormat.COLOR_TRANSFER_HLG) {
            ColorSpaceExt.BT2020_HLG
        } else {
            ColorSpaceExt.SRGB
        } ?: ColorSpaceExt.SRGB
        val transformMat = FloatArray(16)
        Matrix.setIdentityM(transformMat, 0)
        glView?.setData(STABLE_NP.getKey(KEY_VIDEO_EXTRA_MATRIX), transformMat)

        glView?.glRenderer?.execute {
            val oesTexture = OesRawTexture(size.width, size.height, TexConfig.RGBA_1010102, colorSpace).apply {
                load()
            }
            oesTexture.onFrameAvailableListener = SurfaceTexture.OnFrameAvailableListener {
                glView?.glRenderer?.execute {
                    it.updateTexImage()
                }
                glView?.postRender()
            }
            glView?.setData(STABLE_NP.getKey(KEY_VIDEO_TEXTURE), oesTexture)
            lifecycleScope.launch {
                player?.setSurface(Surface(oesTexture.surfaceTexture))
            }
        }

        player?.reset()
        player?.setDataSource(obj)
        player?.prepareAsync()
        player?.start()
        ibPlay?.isEnabled = true
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        display?.unregisterHdrSdrRatioChangedListener(hdrSdrListener)
    }

    override fun onDestroy() {
        super.onDestroy()
        glView?.apply {
            bufferRenderer.release(false)
            glRenderer.stop(false)
        }
    }

    private open class PickOnlyCallback(private val resultFunc: (Uri) -> Unit) : ActivityResultCallback<Uri?> {
        override fun onActivityResult(result: Uri?) {
            result ?: run {
                Log.d(TAG, "PickOnlyCallback.onActivityResult: ")
                return
            }
            Log.d(TAG, "onActivityResult: $result")
            resultFunc(result)
        }
    }

    private class PickTypeResultContract : ActivityResultContract<String, Uri?>() {
        override fun createIntent(context: Context, input: String): Intent {
            return Intent(Intent.ACTION_PICK).setType(input)
        }

        override fun parseResult(resultCode: Int, intent: Intent?): Uri? {
            return if (resultCode != Activity.RESULT_OK) null else intent?.data
        }
    }

    companion object {
        private const val TAG = "GLShowActivity"
        private const val MIME_TYPE_IMAGE = "image/*"
        private const val MIME_TYPE_VIDEO = "video/*"
    }
}

interface OnSeekBarChangeListener2 : OnSeekBarChangeListener {
    override fun onProgressChanged(seekBar: SeekBar, progress: Int, fromUser: Boolean) = Unit

    override fun onStartTrackingTouch(seekBar: SeekBar) = Unit

    override fun onStopTrackingTouch(seekBar: SeekBar) = Unit
}
