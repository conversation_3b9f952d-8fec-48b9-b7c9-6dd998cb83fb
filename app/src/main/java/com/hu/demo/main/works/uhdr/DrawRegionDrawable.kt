package com.hu.demo.main.works.uhdr

import android.graphics.*
import android.graphics.drawable.Drawable
import androidx.core.graphics.toRectF

class DrawRegionDrawable(private val array: Array<Array<Pair<Rect, Bitmap>?>>?) : Drawable() {
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val rectPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.RED
        strokeWidth = 2f
        style = Paint.Style.STROKE
    }
    private val matrix = Matrix()

    override fun draw(canvas: Canvas) {
        val array = array ?: return
        canvas.save()
        val scale = bounds.width().toFloat() / (array[0].size * array[0][0]!!.first.width())
        canvas.translate(0f, ((bounds.height() - array.size * array[0][0]!!.first.height() * scale) / 2))
        canvas.scale(scale, scale)
        for (col in array) {
            for (row in col) {
                row ?: continue
                matrix.setRectToRect(
                    RectF(0f, 0f, row.first.width().toFloat(), row.first.height().toFloat()),
                    row.first.toRectF(),
                    Matrix.ScaleToFit.CENTER
                )
                paint.shader?.setLocalMatrix(matrix)
                canvas.drawBitmap(row.second, matrix, paint)
                canvas.drawRect(row.first, rectPaint)
            }
        }
        canvas.restore()
    }

    override fun setAlpha(alpha: Int) = Unit

    override fun setColorFilter(filter: ColorFilter?) = Unit

    @Deprecated("Deprecated in Java")
    override fun getOpacity(): Int = PixelFormat.TRANSPARENT
}