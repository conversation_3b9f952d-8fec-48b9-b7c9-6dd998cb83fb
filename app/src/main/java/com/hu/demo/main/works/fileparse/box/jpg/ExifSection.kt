package com.hu.demo.main.works.fileparse.box.jpg

import com.hu.demo.main.works.fileparse.ExifReader
import com.hu.demo.main.works.fileparse.box.Tree
import com.hu.demo.utils.ByteOrderedDataInputStream
import com.hu.demo.utils.mark
import com.hu.demo.utils.readByteOrder

internal class ExifSection(parent: Tree) : IdentifierSection(parent) {
    private var sb = StringBuilder()

    override fun read(bis: ByteOrderedDataInputStream) {
        super.read(bis)
        // identifier只读取了一个\0，需要skip一个
        bis.nSkipFully(1)

        // 因为size没有包含marker，需要加上
        val contentSize = allSize - alreadyReadSize.toInt()
        val exifBis = bis.mark(contentSize) {
            ByteOrderedDataInputStream(ByteArray(contentSize).apply(::read))
        }
        val contentArray = bis.mark(contentSize) {
            ByteArray(contentSize).apply(::read)
        }

        // tiff数据，所以backupBis不需要skip，后面计算offset是从原始位置计算
        val byteOrder = exifBis.readByteOrder()

        sb.append("\n$byteOrder")
        exifBis.byteOrder = byteOrder

        val ifdOffset = exifBis.readInt()

        sb.append("\noffset No.0 IFD: $ifdOffset")

        ExifReader.readExifTag2(byteOrder, ifdOffset, sb, contentArray)
    }

    override fun toString(): String {
        return "${super.toString()}\n$sb"
    }
}
