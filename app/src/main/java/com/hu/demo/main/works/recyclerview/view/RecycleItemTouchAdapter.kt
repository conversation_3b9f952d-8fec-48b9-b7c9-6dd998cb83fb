/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - RecycleItemTouchAdapter.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2022/01/13
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery		2022/01/13		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.recyclerview.view

import android.graphics.Canvas
import android.view.MotionEvent
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.hu.demo.main.works.recyclerview.slidingselect.ISelectable
import kotlin.math.pow
import kotlin.math.sqrt

class RecycleItemTouchAdapter(targetView: RecyclerView) : ViewTouchAdapter<RecyclerView>(targetView) {
    private val callbacks = mutableSetOf<IItemTouchProcessor<RecyclerView>>()
    private var mInterceptProcessor: IItemTouchProcessor<RecyclerView>? = null
    private val onItemTouchListener = object : RecyclerView.OnItemTouchListener {
        override fun onInterceptTouchEvent(rv: RecyclerView, e: MotionEvent): Boolean {
            for (callback in callbacks) {
                if (callback.onInterceptTouchEvent(e)) {
                    mInterceptProcessor = callback
                    break
                }
            }
            return (mInterceptProcessor != null).apply {
                if ((e.actionMasked == MotionEvent.ACTION_UP) || (e.actionMasked == MotionEvent.ACTION_CANCEL)) {
                    mInterceptProcessor = null
                }
            }
        }

        override fun onTouchEvent(rv: RecyclerView, e: MotionEvent) {
            mInterceptProcessor?.onTouchEvent(e)
            if ((e.actionMasked == MotionEvent.ACTION_UP) || (e.actionMasked == MotionEvent.ACTION_CANCEL)) {
                mInterceptProcessor = null
            }
        }

        override fun onRequestDisallowInterceptTouchEvent(disallowIntercept: Boolean) {
            if (mInterceptProcessor == null) {
                for (callback in callbacks) {
                    callback.onRequestDisallowInterceptTouchEvent(disallowIntercept)
                }
            } else {
                mInterceptProcessor?.onRequestDisallowInterceptTouchEvent(disallowIntercept)
            }
        }
    }

    private val itemDecoration = object : RecyclerView.ItemDecoration() {
        override fun onDraw(c: Canvas, parent: RecyclerView, state: RecyclerView.State) {
            mInterceptProcessor?.onDraw(c, view)
        }
    }

    override fun attach() {
        view.addOnItemTouchListener(onItemTouchListener)
        view.addItemDecoration(itemDecoration)
    }

    override fun detach() {
        view.removeOnItemTouchListener(onItemTouchListener)
        view.removeItemDecoration(itemDecoration)
    }

    override fun addCallback(processor: IItemTouchProcessor<RecyclerView>) {
        callbacks.add(processor)
    }

    override fun removeCallback(processor: IItemTouchProcessor<RecyclerView>) {
        callbacks.remove(processor)
    }

    override fun findChildPosition(child: View): Int {
        return view.getChildViewHolder(child).bindingAdapterPosition
    }

    override fun findChildView(x: Float, y: Float, findNear: Boolean): View? {
        return view.findNearbyChildViewUnder(x, y, findNear)
    }

    override fun isSelectable(position: Int): Boolean {
        return (view.findViewHolderForAdapterPosition(position) as? ISelectable)?.isSelectable() ?: false
    }

    /**
     * 手指触摸的是空白区域：
     * 触摸空白区域是在照片最后一行（照片未占满一行）的空白区域
     */
    private fun RecyclerView.findNearbyChildViewUnder(tX: Float, tY: Float, findNear: Boolean): View? {
        val count: Int = childCount
        if (count <= 0) {
            return null
        }
        val isInside = (tX >= 0) && (tX <= width) && (tY >= 0) && (tY <= height)
        val range: IntProgression = if (tY < (width shr 2)) {
            0 until count
        } else {
            count - 1 downTo 0
        }
        var lastView: Pair<View, Float>? = null
        for (index in range) {
            val child = getChildAt(index) ?: return null
            val childLeft = child.left
            val childRight = child.right
            val childTop = child.top
            val childBottom = child.bottom
            if (findNear) {
                if ((isInside && (tY >= childTop && tY <= childBottom)) || !isInside) {
                    val dis = sqrt((tX - ((childLeft + childRight) shr 1)).pow(2) + (tY - ((childTop + childBottom) shr 1)).pow(2))
                    lastView = if ((lastView == null) || (dis < lastView.second)) {
                        child to dis
                    } else {
                        break
                    }
                } else if (lastView != null) {
                    break
                }
            } else {
                if ((tX >= childLeft) && (tX <= childRight) && (tY >= childTop) && (tY <= childBottom)) {
                    lastView = child to 0f
                    break
                }
            }
        }
        return lastView?.first
    }
}