package com.hu.demo.main.works.fileparse

import android.util.Log
import com.google.common.base.Ascii.NUL
import com.hu.demo.main.works.fileparse.box.jpg.DhtSection
import com.hu.demo.main.works.fileparse.box.jpg.DqtSection
import com.hu.demo.main.works.fileparse.box.jpg.DriSection
import com.hu.demo.main.works.fileparse.box.jpg.ExifSection
import com.hu.demo.main.works.fileparse.box.jpg.IccSection
import com.hu.demo.main.works.fileparse.box.jpg.IdentifierSection
import com.hu.demo.main.works.fileparse.box.jpg.JpgFile
import com.hu.demo.main.works.fileparse.box.jpg.MpfSection
import com.hu.demo.main.works.fileparse.box.jpg.Sof0Section
import com.hu.demo.main.works.fileparse.box.jpg.SoiSection
import com.hu.demo.main.works.fileparse.box.jpg.SosSection
import com.hu.demo.main.works.fileparse.box.jpg.StringSection
import com.hu.demo.main.works.fileparse.box.jpg.XapExtensionSection
import com.hu.demo.main.works.fileparse.box.jpg.XmpSection
import com.hu.demo.utils.ByteOrderedDataInputStream
import com.hu.demo.utils.mark
import com.hu.demo.utils.toHexString
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.buffer
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import java.io.ByteArrayOutputStream
import java.io.DataInputStream
import java.io.InputStream
import kotlin.text.Charsets.US_ASCII

class JpgFileParser : IFileParser {
    override fun canParse(iStream: IStreamSource): Boolean {
        val fis = DataInputStream(iStream.newStream().buffered())
        val buffer = fis.mark(CHECK_SIZE) {
            ByteArray(CHECK_SIZE).apply(::readFully)
        }
        fis.close()
        return checkStartWith(JPEG_SIGNATURE, buffer)
    }

    override suspend fun parse(iStream: IStreamSource): Flow<CharSequence> {
        val flow = flow<CharSequence> {
            val bis = ByteOrderedDataInputStream(iStream.newStream().buffered())

            val jpgFile = JpgFile()
            while (true) {
                val (first, second) = runCatching {
                    bis.mark(2) {
                        readByte() to readByte()
                    }
                }.onFailure {
                    Log.e(TAG, "parse: ", it)
                }.getOrNull() ?: break
                Log.e(TAG, "parse: <${first.toHexString()}, ${second.toHexString()}>")
                val section = when (first) {
                    MARKER -> {
                        when (second) {
                            MARKER_SOI -> {
                                SoiSection(jpgFile)
                            }

                            in MARKER_APP_SET -> {
                                val identifier = bis.mark(100) {
                                    // 跳过maker
                                    readShort()
                                    // 跳过长度
                                    readShort()
                                    getIdentifier()
                                }
                                when (second) {
                                    MARKER_APP1 -> {
                                        when {
                                            identifier.contentEquals(IDENTIFIER_EXIF_APP1) -> {
                                                ExifSection(jpgFile)
                                            }

                                            identifier.contentEquals(IDENTIFIER_ADOBE_XAP_APP1) -> {
                                                XmpSection(jpgFile)
                                            }

                                            identifier.contentEquals(IDENTIFIER_ADOBE_XMP_EXTENSION_APP1) -> {
                                                XapExtensionSection(jpgFile)
                                            }

                                            else -> {
                                                IdentifierSection(jpgFile, 4)
                                            }
                                        }
                                    }

                                    MARKER_APP2 -> {
                                        when {
                                            identifier.contentEquals(IDENTIFIER_MPF_APP2) -> {
                                                MpfSection(jpgFile)
                                            }

                                            identifier.contentEquals(IDENTIFIER_ICC_PROFILE_APP2) -> {
                                                IccSection(jpgFile)
                                            }

                                            else -> {
                                                IdentifierSection(jpgFile, 28)
                                            }
                                        }
                                    }

                                    else -> {
                                        IdentifierSection(jpgFile, 4)
                                    }
                                }
                            }

                            MARKER_COM -> {
                                StringSection("COM", jpgFile)
                            }

                            MARKER_DQT -> {
                                DqtSection(jpgFile)
                            }

                            MARKER_DRI -> {
                                DriSection(jpgFile)
                            }

                            MARKER_SOF0 -> {
                                Sof0Section(jpgFile)
                            }

                            MARKER_DHT -> {
                                DhtSection(jpgFile)
                            }

                            MARKER_SOS -> {
                                SosSection(jpgFile)
                            }

                            else -> {
                                IdentifierSection(jpgFile, 4)
                            }
                        }
                    }

                    else -> null
                } ?: break
                section.read(bis)
                section.skipRemain(bis)
                emit(section.toString())
            }
        }.flowOn(Dispatchers.IO).catch {
            Log.e(TAG, "parse: ", it)
        }.buffer()
        return flow
    }


    private fun checkStartWith(comp: ByteArray, cur: ByteArray): Boolean {
        if (cur.size < comp.size) {
            return false
        }
        for ((index, byte) in comp.withIndex()) {
            if (cur[index] != byte) {
                return false
            }
        }
        return true
    }

    companion object {
        private const val TAG = "JpgFileParser"
        private const val MARKER = 0xFF.toByte()
        private const val MARKER_SOI = 0xD8.toByte()
        private const val MARKER_SOF0 = 0xC0.toByte()
        private const val MARKER_SOF1 = 0xC1.toByte()
        private const val MARKER_SOF2 = 0xC2.toByte()
        private const val MARKER_SOF3 = 0xC3.toByte()
        private const val MARKER_SOF5 = 0xC5.toByte()
        private const val MARKER_SOF6 = 0xC6.toByte()
        private const val MARKER_SOF7 = 0xC7.toByte()
        private const val MARKER_SOF9 = 0xC9.toByte()
        private const val MARKER_SOF10 = 0xCA.toByte()
        private const val MARKER_SOF11 = 0xCB.toByte()
        private const val MARKER_SOF13 = 0xCD.toByte()
        private const val MARKER_SOF14 = 0xCE.toByte()
        private const val MARKER_SOF15 = 0xCF.toByte()
        private const val MARKER_SOS = 0xDA.toByte()
        private const val MARKER_DHT = 0xC4.toByte()
        private const val MARKER_APP0 = 0xE0.toByte()
        private const val MARKER_APP1 = 0xE1.toByte()
        private const val MARKER_APP2 = 0xE2.toByte()
        private const val MARKER_APP3 = 0xE3.toByte()
        private const val MARKER_APP4 = 0xE4.toByte()
        private const val MARKER_APP5 = 0xE5.toByte()
        private const val MARKER_APP6 = 0xE6.toByte()
        private const val MARKER_APP7 = 0xE7.toByte()
        private const val MARKER_APP8 = 0xE8.toByte()
        private const val MARKER_APP9 = 0xE9.toByte()
        private const val MARKER_APPA = 0xEA.toByte()
        private const val MARKER_APPB = 0xEB.toByte()
        private const val MARKER_APPC = 0xEC.toByte()
        private const val MARKER_APPD = 0xED.toByte()
        private const val MARKER_APPE = 0xEE.toByte()
        private const val MARKER_APPF = 0xEF.toByte()
        private const val MARKER_DQT = 0xDB.toByte()
        private const val MARKER_DRI = 0xDD.toByte()
        private const val MARKER_COM = 0xFE.toByte()
        private const val MARKER_EOI = 0xD9.toByte()
        private const val MARKER_ESCAPE = 0x00.toByte()

        val JPEG_SIGNATURE = byteArrayOf(MARKER, MARKER_SOI, MARKER)
        private val MARKER_APP_SET = setOf(
            MARKER_APP0,
            MARKER_APP1,
            MARKER_APP2,
            MARKER_APP3,
            MARKER_APP4,
            MARKER_APP5,
            MARKER_APP6,
            MARKER_APP7,
            MARKER_APP8,
            MARKER_APP9,
            MARKER_APPA,
            MARKER_APPB,
            MARKER_APPC,
            MARKER_APPD,
            MARKER_APPE,
            MARKER_APPF
        )
        private val IDENTIFIER_JFIF_APP0 = "JFIF".toByteArray(US_ASCII)
        private val IDENTIFIER_EXIF_APP1 = "Exif".toByteArray(US_ASCII)
        private val IDENTIFIER_ADOBE_XAP_APP1 = "http://ns.adobe.com/xap/1.0/".toByteArray(US_ASCII)
        private val IDENTIFIER_ADOBE_XMP_EXTENSION_APP1 = "http://ns.adobe.com/xmp/extension/".toByteArray(US_ASCII)
        private val IDENTIFIER_MPF_APP2 = "MPF".toByteArray(US_ASCII)
        private val IDENTIFIER_ICC_PROFILE_APP2 = "ICC_PROFILE".toByteArray(US_ASCII)
        private val IDENTIFIER_URN_ISO_STD_ISO_TS_21496_1 = "urn:iso:std:iso:ts:21496:-1".toByteArray(US_ASCII)
        private val EXIF_SUB_IFD_TAG = 0x8769.toShort()
        private const val CHECK_SIZE = 10

        private const val MPF_IFD_TAG_VERSION = 0xB000.toShort()
        private const val MPF_IFD_TAG_NUMBER = 0xB001.toShort()
        private const val MPF_IFD_TAG_MP_ENTRY = 0xB002.toShort()

        private const val MPF_MP_TYPE_CODE_BASE_IMAGE = 0x030000.toLong()
        private const val MPF_MP_TYPE_CODE_UNDEFINED = 0x000000.toLong()

        private fun <T : InputStream> T.getIdentifier(maxLength: Int = Int.MAX_VALUE): ByteArray {
            val bis = ByteArrayOutputStream()
            val buffer = ByteArray(1)
            while (read(buffer) > 0 && (bis.size() < maxLength) && buffer[0] != NUL) {
                bis.write(buffer, 0, buffer.size)
            }
            return bis.toByteArray()
        }
    }
}