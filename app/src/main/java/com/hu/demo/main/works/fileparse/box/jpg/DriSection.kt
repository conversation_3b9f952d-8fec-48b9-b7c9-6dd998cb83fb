package com.hu.demo.main.works.fileparse.box.jpg

import com.hu.demo.main.works.fileparse.box.Tree
import com.hu.demo.utils.ByteOrderedDataInputStream
import com.hu.demo.utils.contentHexString

internal class DriSection(parent: Tree) : SizeSection(parent) {
    private var data: ByteArray? = null

    override fun read(bis: ByteOrderedDataInputStream) {
        super.read(bis)
        val contentSize = allSize - alreadyReadSize.toInt()
        data = bis.nReadBytes(contentSize)
    }

    override fun toString(): String {
        return "${super.toString()} SRI\ndata:${data?.contentHexString()}"
    }
}
