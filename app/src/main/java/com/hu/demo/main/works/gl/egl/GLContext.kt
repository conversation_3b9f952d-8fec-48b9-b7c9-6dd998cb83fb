/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - GLContextRender.kt.kt
 ** Description:使用OpenGL的的渲染类，提供功能独立的OpenGL渲染实现
 ** Version: 1.0
 ** Date : 2023/6/29
 ** Author: xiewujie@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  xiewujie@Apps.Gallery3D      2023/06/29    1.0         first created
 ********************************************************************************/
package com.hu.demo.main.works.gl.egl

import android.util.Log

/**
 * 使用OpenGL的的渲染类，提供依赖OpenGL线程渲染的单一功能实现
 */
object GLContext {

    private const val TAG = "GLContext"

    /**
     * 将函数运行在GL线程中，并默认使用GLContext作为上下文
     * 需要注意的是此方法每次都会创建一组egl实例，禁止批量调用此方法
     * @param block 需要运行在GL线程的函数
     * @return 返回运行结果，如果初始化EGL环境失败，会返回null
     */
    fun <T> runOnGL(block: () -> T): T? {
        val offlineEglCore = OfflineEGLCore()
        try {
            if (offlineEglCore.makeCurrent()) {
                return block.invoke()
            }
        } catch (e: Throwable) {
            Log.e(TAG, "runOnGL: error.", e)
        } finally {
            offlineEglCore.release()
        }
        return null
    }
}