package com.hu.demo.main.works.fileparse

import android.util.Log
import com.hu.demo.main.works.fileparse.box.Box
import com.hu.demo.main.works.fileparse.box.Tree
import com.hu.demo.main.works.fileparse.box.mp4.FileTypeBox
import com.hu.demo.main.works.fileparse.box.mp4.HandlerBox
import com.hu.demo.main.works.fileparse.box.mp4.MetaBox
import com.hu.demo.main.works.fileparse.box.mp4.MetaItemKeysBox
import com.hu.demo.main.works.fileparse.box.mp4.MetaItemListBox
import com.hu.demo.main.works.fileparse.box.mp4.MovieBox
import com.hu.demo.main.works.fileparse.box.mp4.MovieHeaderBox
import com.hu.demo.main.works.fileparse.box.mp4.Mp4File
import com.hu.demo.main.works.fileparse.box.mp4.TitleBox
import com.hu.demo.main.works.fileparse.box.mp4.TrackBox
import com.hu.demo.main.works.fileparse.box.mp4.TrackHeaderBox
import com.hu.demo.utils.ByteOrderedDataInputStream
import com.hu.demo.utils.mark
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.FlowCollector
import kotlinx.coroutines.flow.buffer
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import java.io.DataInputStream

class Mp4FileParser : IFileParser {
    override fun canParse(iStream: IStreamSource): Boolean {
        val fis = DataInputStream(iStream.newStream().buffered())
        val buffer = fis.mark(CHECK_SIZE) {
            ByteArray(CHECK_SIZE).apply(::readFully)
        }
        fis.close()
        return checkStartWith(buffer)
    }

    private fun buildTree(): List<Box> {
        return buildList {
            val root = Mp4File(-1)
            FileTypeBox(root).apply(::add)
            MovieBox(root).apply(::add).apply {
                MetaBox(this).also(::add).apply {
                    HandlerBox(this).also(::add)
                    MetaItemKeysBox(this).also(::add)
                    MetaItemListBox(this).also(::add)
                }
                MovieHeaderBox(this).also(::add)
                TrackBox(this).apply(::add).apply {
                    TrackHeaderBox(this).apply(::add)
                }
                TitleBox(this).apply(::add)
            }
        }
    }

    override suspend fun parse(iStream: IStreamSource): Flow<CharSequence> {
        val flow = flow {
            runCatching {
                val mp4File = Mp4File(Long.MAX_VALUE)
                val tree = buildTree()
                ByteOrderedDataInputStream(iStream.newStream().buffered()).use {
                    readMp4(it, tree, mp4File)
                }
            }.onFailure {
                Log.e(TAG, "parse: ", it)
            }
        }.flowOn(Dispatchers.IO).catch {
            Log.e(TAG, "parse: ", it)
        }.buffer()
        return flow
    }

    private suspend fun FlowCollector<CharSequence>.readMp4(bis: ByteOrderedDataInputStream, checkTree: List<Box>, parent: Tree) {
        while (bis.mark(1) { read() != -1 }) {
            readBox(bis, checkTree, parent)
        }
    }

    private suspend fun FlowCollector<CharSequence>.readBox(bis: ByteOrderedDataInputStream, checkTree: List<Box>, parent: Tree) {
        val checkBox = bis.mark(4 + 4 + 14) {
            Box("unknown", parent.level + 1).apply { read(this@mark) }
        }
        val box = checkTree.find {
            it.nodeName == checkBox.type && it.level == checkBox.level
        }?.fork(parent) ?: Box(checkBox.nodeName, checkBox.level, parent)
        box.read(bis)
        val levelStr = buildLevelStr(box.level)
        emit("$levelStr$box \n")

        while (box.boxNeedReadChild || !box.boxReadOver) {
            readBox(bis, checkTree, box)
        }
        box.skipRemain(bis)
    }

    private fun buildLevelStr(level: Int): String {
        return buildString {
            for (index in 1 until level) {
                append("—")
            }
        }
    }

    private fun checkStartWith(cur: ByteArray): Boolean {
        val boxLength = 4
        if (!checkArray(cur, boxLength, FTYP_SIGNATURE)) {
            return false
        }
        if (checkArray(cur, boxLength + FTYP_SIGNATURE.size, MP41_BRAND)) {
            return true
        }
        if (checkArray(cur, boxLength + FTYP_SIGNATURE.size, MP42_BRAND)) {
            return true
        }
        if (checkArray(cur, boxLength + FTYP_SIGNATURE.size, ISOM_BRAND)) {
            return true
        }
        if (checkArray(cur, boxLength + FTYP_SIGNATURE.size, ISO2_BRAND)) {
            return true
        }
        return true
    }

    private fun checkArray(check: ByteArray, checkOffset: Int, src: ByteArray): Boolean {
        for (index in src.indices) {
            if (check[index + checkOffset] != src[index]) {
                return false
            }
        }
        return true
    }


    companion object {
        private const val TAG = "HeifFileParser"
        val FTYP_SIGNATURE = "ftyp".toByteArray(Charsets.US_ASCII)
        val MP41_BRAND = "mp41".toByteArray(Charsets.US_ASCII)
        val MP42_BRAND = "mp42".toByteArray(Charsets.US_ASCII)
        val ISOM_BRAND = "isom".toByteArray(Charsets.US_ASCII)
        val ISO2_BRAND = "iso2".toByteArray(Charsets.US_ASCII)
        private const val CHECK_SIZE = 20

        private const val BYTE_ALIGN_II: Int = 0x49492A00 // II: Intel order

        private const val BYTE_ALIGN_MM: Int = 0x4d4d002A // MM: Motorola order
    }
}