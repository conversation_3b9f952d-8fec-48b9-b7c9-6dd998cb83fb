package com.hu.demo.main.works.flow

import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Button
import android.widget.TextView
import androidx.lifecycle.lifecycleScope
import com.hu.demo.base.ui.BaseActivity
import com.hu.demo.main.R
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.conflate
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.mapLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class FlowActivity : BaseActivity(), View.OnClickListener {
    private var btnEmit1: Button? = null
    private var btnEmit2: Button? = null
    private var tvText: TextView? = null
    private val testFlow = MutableSharedFlow<String>()
    private var number: Int = 0
    override fun getLayoutId(): Int = R.layout.activity_flow

    override fun initToolbar() {
        super.initToolbar()
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowTitleEnabled(true)
            setDisplayShowHomeEnabled(false)
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
    }

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        btnEmit1 = findViewById(R.id.btnEmit1)
        btnEmit2 = findViewById(R.id.btnEmit2)
        tvText = findViewById(R.id.tvText)
        btnEmit1?.setOnClickListener(this)
        btnEmit2?.setOnClickListener(this)
        lifecycleScope.launch {
            testFlow.collect {
                Log.d(TAG, "initView: $it")
            }
        }
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    override fun initEvent(savedInstanceState: Bundle?) {
        super.initEvent(savedInstanceState)
        lifecycleScope.launch {
            testFlow
                .mapLatest {
                    delay(500)
                    it
                }.filter {
                    Log.d(TAG, "initEvent: hucanhua")
                    true
                }
                .conflate()
                .collect {
                    withContext(Dispatchers.Main) {
                        tvText?.append("from collect <1> $it\n")
                    }
                }
        }
        lifecycleScope.launch {
            testFlow
                .conflate()
                .collect {
                    withContext(Dispatchers.Main) {
                        tvText?.append("from collect <2> $it\n")
                    }
                }
        }
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.btnEmit1 -> {
                lifecycleScope.launch {
                    testFlow.emit("emit 1 ${number++}")
                }
            }

            R.id.btnEmit2 -> {
                lifecycleScope.launch {
                    testFlow.emit("emit 2 ${number++}")
                }
            }
        }
    }

    companion object {
        private const val TAG = "FlowActivity"
    }
}