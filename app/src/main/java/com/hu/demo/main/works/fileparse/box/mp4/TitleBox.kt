package com.hu.demo.main.works.fileparse.box.mp4

import com.hu.demo.main.works.fileparse.box.Box
import com.hu.demo.main.works.fileparse.box.Tree
import com.hu.demo.utils.ByteOrderedDataInputStream

internal class TitleBox(parent: Tree) : Box("titl", 2, parent) {
    var title: String? = null
    override fun readInner(bis: ByteOrderedDataInputStream) {
        super.readInner(bis)
        title = bis.nReadBytes(remainSize.toInt()).decodeToString()
    }

    override fun fork(parent: Tree): Box {
        return TitleBox(parent)
    }

    override fun toString(): String {
        return "${super.toString()}, title:$title"
    }
}