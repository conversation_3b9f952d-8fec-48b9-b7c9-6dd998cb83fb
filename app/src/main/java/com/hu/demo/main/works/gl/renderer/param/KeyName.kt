/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - KeyName.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/08/27
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery		2024/08/27		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.renderer.param

import android.opengl.GLES30
import android.opengl.Matrix
import android.os.Trace
import android.util.ArrayMap
import android.view.SurfaceHolder
import android.view.SurfaceView
import androidx.graphics.lowlatency.BufferInfo
import androidx.graphics.opengl.GLFrameBufferRenderer
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.getKey
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.NEXT_NP
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.PROCEDURAL_NP
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.STABLE_NP

/**
 * 渲染器执行时的参数的Key，获取参数需要通过此Key对象获取。
 *
 * 为了避免Key的频繁生成，讲构造函数设置为私有，需要通过[NodePath.getKey]来获取Key。
 *
 * Key的定义目前分为三类，由一个虚拟的[NodePath]生成：
 * - [STABLE_NP] : 持久的参数集，此类参数会在渲染器中一直保存，在每次渲染循环中都会使用
 * - [PROCEDURAL_NP] : 临时的参数集，此类参数是当前渲染循环中的参数，仅在当此渲染循环中使用
 * - [NEXT_NP] : 上一步的参数集，此类参数是上一步[Renderer]的产物，当前[Renderer]会（可能）使用此参数进行处理
 */
class KeyName private constructor(
    /**
     * 参数的[NodePath]，通常是虚拟的Path，不是对应[Renderer]中的[NodePath]
     */
    val nodePath: NodePath,
    /**
     * 参数的名称
     */
    val keyName: String,
) {
    /**
     * 参数完整名称，包括[nodePath]和[keyName]。
     *
     * 例： stable{thumbnail}
     */
    val pathName by lazy { buildPathName(nodePath, keyName) }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as KeyName

        return pathName == other.pathName
    }

    override fun hashCode(): Int {
        return pathName.hashCode()
    }

    override fun toString(): String {
        return pathName
    }

    companion object {
        /**
         * [KeyName]的缓存池
         */
        private val keyCache = ArrayMap<String, KeyName>()

        /**
         * 图片和视频需要渲染的mix阈值
         */
        const val VALUE_NEED_RENDER_THRESHOLD = 0.0f

        /**
         * 图片和视频切换显示模式的阈值
         */
        const val VALUE_IMAGE_VIDEO_THRESHOLD = 0.5f

        /**
         * [EGLManager] 可用于配置在发出 OpenGL 命令以渲染到前端缓冲层时要使用的 EGL 对象
         *
         * @see GLFrameBufferRenderer.Callback
         */
        const val KEY_EGL_MANAGER = "eglManager"

        /**
         * 要渲染的内容的逻辑宽度。此尺寸与[SurfaceHolder.Callback.surfaceChanged]提供的尺寸相匹配
         *
         * @see GLFrameBufferRenderer.Callback
         */
        const val KEY_WIDTH = "width"

        /**
         * 要渲染的内容的逻辑高度。此尺寸与[SurfaceHolder.Callback.surfaceChanged]提供的尺寸一致
         *
         * @see GLFrameBufferRenderer.Callback
         */
        const val KEY_HEIGHT = "height"

        /**
         * 有关正在渲染到的缓冲区的[BufferInfo]。这包括缓冲区的宽度和高度，它们可能与提供给[GLFrameBufferRenderer]的[SurfaceView]的相应尺寸不同，
         * 因为预旋转有时会交换宽度和高度参数，以避免 GPU 组合来旋转内容。这应该用作[GLES30.glViewport]的输入。
         *
         * 此外，这还包含一个帧缓冲区标识符，可用于在渲染到中间临时缓冲区后将渲染操作重新定位到原始目标。
         *
         * @see GLFrameBufferRenderer.Callback
         */
        const val KEY_BUFFER_INFO = "bufferInfo"

        /**
         * Matrix that should be applied to the rendering in this callback.
         * This should be consumed as input to any vertex shader implementations. Buffers are
         * pre-rotated in advance in order to avoid unnecessary overhead of GPU composition to
         * rotate content in the same install orientation of the display.
         * This is a 4 x 4 matrix is represented as a flattened array of 16 floating point values.
         * Consumers are expected to leverage [Matrix.multiplyMM] with this parameter alongside
         * any additional transformations that are to be applied.
         *
         * @see GLFrameBufferRenderer.Callback
         */
        const val KEY_TRANSFORM = "transform"

        /**
         * 当前渲染循环下需要设置给SurfaceFlinger的colorspace
         */
        const val KEY_COLOR_SPACE = "colorspace"

        /**
         * 图像混合的比例，在渲染时需要将[KEY_STABLE_IMAGE_THUMBNAIL]复制一份到当前KEY
         */
        const val KEY_MIX_RATIO = "mixRatio"

        /**
         * 输入的视频纹理
         */
        const val KEY_VIDEO_TEXTURE = "videoTexture"

        /**
         * 输入的图片纹理
         */
        const val KEY_IMAGE_TEXTURE = "imageTexture"

        /**
         * UHDR增益纹理
         */
        const val KEY_GAINMAP_TEXTURE = "gainmapTexture"

        /**
         * 输入的图片纹理
         */
        const val KEY_IMAGE_TEXTURE_2 = "imageTexture2"

        /**
         * UHDR增益纹理
         */
        const val KEY_GAINMAP_TEXTURE_2 = "gainmapTexture2"

        /**
         * vigTable纹理
         */
        const val KEY_VIG_TABLE_TEXTURE = "vigTableTexture"

        /**
         * ditherTable纹理
         */
        const val KEY_DITHER_TABLE_TEXTURE = "ditherTableTexture"

        /**
         * vigTable纹理
         */
        const val KEY_VIG_RATIO = "vigRatio"

        /**
         * ditherTable纹理
         */
        const val KEY_DITHER_RATIO = "ditherRatio"
        const val KEY_EXTRA_MATRIX = "extraMatrix"
        const val KEY_OUTPUT_TEXTURE = "outputTexture"
        const val KEY_TEXTURE_POOL = "texturePool"
        const val KEY_VIDEO_EXTRA_MATRIX = "videoExtraMatrix"
        const val KEY_DESIRED_RATIO = "desiredRatio"
        const val KEY_DEVICE_HDR_SDR_RATIO = "deviceHdrSdrRatio"
        const val KEY_THUMBNAIL = "thumbnail"
        const val KEY_COMPARE_IMAGE_TEXTURE = "compareImageTexture"
        const val KEY_3D_LUT_TEXTURE = "3dLutTexture"
        const val KEY_3D_LUT_SIZE = "3dLutSize"

        /**
         * 通过[NodePath]生成[KeyName]。
         *
         * 当前生成[KeyName]需限制[NodePath]为：[STABLE_NP]、[PROCEDURAL_NP]、[NEXT_NP]
         *
         * @param keyName 参数的名称
         *
         * @return 返回生成的[KeyName]对象
         */
        @Synchronized
        fun NodePath.getKey(keyName: String): KeyName {
            try {
                Trace.beginSection("getKey")
                return keyCache.getOrPut(buildPathName(this, keyName)) { KeyName(this, keyName) }
            }finally {
                Trace.endSection()
            }
        }

        private fun buildPathName(nodePath: NodePath, keyName: String): String {
            return "$nodePath{$keyName}"
        }
    }
}