package com.hu.demo.main.works.fileparse.box

import com.hu.demo.utils.ByteOrderedDataInputStream
import com.hu.demo.utils.toHexString

internal open class FullBox(boxName: String, level: Int = 0, parent: Tree? = null) : Box(boxName, level, parent) {
    var version: Byte? = null
        private set
    var flags: ByteArray? = null
        private set

    override fun readInner(bis: ByteOrderedDataInputStream) {
        super.readInner(bis)
        version = bis.nReadByte()
        flags = bis.nReadBytes(3)
    }

    override fun toString(): String {
        return "${super.toString()}, version:${version?.toHexString()}, flags:${flags.contentToString()}"
    }
}