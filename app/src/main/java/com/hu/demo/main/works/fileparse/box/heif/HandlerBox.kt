package com.hu.demo.main.works.fileparse.box.heif

import com.hu.demo.main.works.fileparse.box.Box
import com.hu.demo.main.works.fileparse.box.FullBox
import com.hu.demo.main.works.fileparse.box.Tree
import com.hu.demo.utils.ByteOrderedDataInputStream
import com.hu.demo.utils.toByteString

internal open class HandlerBox(parent: Tree) : FullBox("hdlr", 2, parent) {
    var preDefined: Int? = null
    var handlerType: Int? = null
    var name: String? = null

    override fun readInner(bis: ByteOrderedDataInputStream) {
        super.readInner(bis)
        preDefined = bis.nReadInt()
        handlerType = bis.nReadInt()
        bis.nSkipFully(4 * 3)
        name = bis.nReadUTF()
    }

    override fun fork(parent: Tree): Box {
        return HandlerBox(parent)
    }

    override fun toString(): String {
        return "${super.toString()}, preDefined:$preDefined, handlerType:${handlerType?.toByteString()}, name:$name"
    }
}