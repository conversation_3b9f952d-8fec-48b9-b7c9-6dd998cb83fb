/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : IRendererTag.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/11/15
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** dengchuk<PERSON>@Apps.Gallery     2024/11/15      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.foundation.opengl2.texture

/**
 * Renerer设置特殊tag的借口
 * 实现该接口后可便于RendererAdapter依据该接口中实现的属性，将其在绘制完一帧后回调出去
 */
interface IRendererTag {
    /**
     * 当前texture携带的[requestCode]，也就是触发一帧请求的requestCode
     */
    val requestCode: Int
}