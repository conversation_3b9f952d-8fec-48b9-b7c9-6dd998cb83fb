/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - CachePool.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/08/24
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * huca<PERSON><PERSON>@Apps.Gallery		2024/08/24		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.utils

import android.util.ArrayMap
import com.hu.demo.main.works.gl.IRecyclable
import java.util.LinkedList

/**
 * 无键的缓存池接口
 */
interface INoKeyCachePool<V : IRecyclable> {
    /**
     * 支持的最大数量
     */
    val maxSize: Int

    /**
     * 创建对象的接口
     */
    val createFunc: () -> V

    /**
     * 从缓存池中获取对象
     *
     * @return 返回一个对象
     */
    fun obtain(): V

    /**
     * 回收对象到缓存池，如果缓存池满了，则直接释放
     *
     * @param value 需要回收的对象
     */
    fun reuse(value: V)

    /**
     * 将缓存池清空
     */
    fun clean()
}

/**
 * 键值对方式的缓存池接口
 */
interface IkeyCachePool<K, V : IRecyclable> {
    /**
     * 支持的最大数量
     */
    val maxSize: Int

    /**
     * 创建对象的接口
     */
    val createFunc: (K) -> V?

    /**
     * 计算大小
     */
    val sizeOf: (V) -> Int

    /**
     * 从缓存池中获取对象
     *
     * @param key 指定[key]
     *
     * @return 返回[key]对应的value
     */
    fun obtain(key: K): V?

    /**
     * 回收对象到缓存池，如果缓存池满了，则直接释放
     *
     * @param key 对应的[key]
     * @param value 需要回收的对象
     */
    fun reuse(key: K, value: V)

    /**
     * 将缓存池清空
     */
    fun clean()
}

/**
 * 无键的缓存池
 *
 * @param maxSize 允许的最大数量
 * @param createFunc 构造对象的方法，当缓存不存在对象时，则使用此方法构造对象
 */
class NoKeyCachePool<V : IRecyclable>(
    override val maxSize: Int,
    override val createFunc: () -> V,
) : INoKeyCachePool<V> {
    private val cache = LinkedList<V>()

    @Synchronized
    override fun obtain(): V {
        return cache.poll() ?: createFunc()
    }

    @Synchronized
    override fun reuse(value: V) {
        if (cache.size < maxSize) {
            cache.offer(value)
        } else {
            value.recycle()
        }
    }

    @Synchronized
    override fun clean() {
        cache.forEach {
            it.recycle()
        }
        cache.clear()
    }
}

/**
 * 键值对方式的缓存池
 *
 * @param maxSize 允许的最大数量
 * @param createFunc 构造对象的方法，当缓存不存在对象时，则使用此方法构造对象
 */
open class KeyCachePool<K, V : IRecyclable>(
    override val maxSize: Int,
    override val createFunc: (K) -> V,
    override val sizeOf: (V) -> Int = { 1 },
) : IkeyCachePool<K, V> {
    private val cache = ArrayMap<K, LinkedList<V>>()

    private val createListFun = {
        LinkedList<V>()
    }

    @Volatile
    var size = 0
        private set

    @Synchronized
    override fun obtain(key: K): V {
        return cache.getOrPut(key, createListFun).poll()?.also {
            size -= sizeOf(it)
        } ?: createFunc(key)
    }

    @Synchronized
    override fun reuse(key: K, value: V) {
        val valueSize = sizeOf(value)
        while (size + valueSize > maxSize) {
            val valueAt = cache.valueAt(0)
            if (valueAt.size > 0) {
                valueAt.pollLast()?.also {
                    size -= sizeOf(it)
                }?.recycle()
            } else {
                cache.removeAt(0)
            }
        }
        size += valueSize
        cache.getOrPut(key, createListFun).offer(value)
    }

    @Synchronized
    override fun clean() {
        cache.values.forEach { list ->
            list.forEach {
                it.recycle()
            }
        }
        cache.clear()
        size = 0
    }
}