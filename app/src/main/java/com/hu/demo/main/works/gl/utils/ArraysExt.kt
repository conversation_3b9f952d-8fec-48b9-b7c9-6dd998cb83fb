/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - ArraysExt.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/08/28
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery		2024/08/28		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.utils

import android.annotation.SuppressLint
import android.util.Half
import java.io.File
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.nio.FloatBuffer
import java.nio.IntBuffer

/**
 * [ByteArray]转换成ByteBuffer
 *
 * @param direct true：采用[DirectorByteBuffer]，false：采用[HeapByteBuffer]，默认true
 * @param order 字节序
 *
 * @return 返回转换后的[ByteBuffer]
 */
fun ByteArray.toBuffer(direct: Boolean = true, order: ByteOrder = ByteOrder.nativeOrder()): ByteBuffer {
    val buffer = if (direct) {
        ByteBuffer.allocateDirect(size * Byte.SIZE_BYTES)
    } else {
        ByteBuffer.allocate(size * Byte.SIZE_BYTES)
    }
    return buffer
        .order(order)
        .put(this)
        .apply {
            rewind()
        }
}

/**
 * [FloatArray]转换成[FloatBuffer]
 *
 * @param direct true：采用[DirectorByteBuffer]，false：采用[HeapByteBuffer]，默认true
 * @param order 字节序
 *
 * @return 返回转换后的[FloatBuffer]
 */
fun FloatArray.toBuffer(direct: Boolean = true, order: ByteOrder = ByteOrder.nativeOrder()): FloatBuffer {
    val buffer = if (direct) {
        ByteBuffer.allocateDirect(size * Float.SIZE_BYTES)
    } else {
        ByteBuffer.allocate(size * Float.SIZE_BYTES)
    }
    return buffer
        .order(order)
        .asFloatBuffer()
        .put(this)
        .apply {
            rewind()
        }
}

fun FloatArray.toByteBuffer(direct: Boolean = true, order: ByteOrder = ByteOrder.nativeOrder()): ByteBuffer {
    val buffer = if (direct) {
        ByteBuffer.allocateDirect(size * Float.SIZE_BYTES)
    } else {
        ByteBuffer.allocate(size * Float.SIZE_BYTES)
    }
    buffer
        .order(order)
        .asFloatBuffer()
        .put(this)
    return buffer
}

/**
 * [IntArray]转换成[IntBuffer]
 *
 * @param direct true：采用[DirectorByteBuffer]，false：采用[HeapByteBuffer]，默认true
 * @param order 字节序
 *
 * @return 返回转换后的[IntBuffer]
 */
fun IntArray.toBuffer(direct: Boolean = true, order: ByteOrder = ByteOrder.nativeOrder()): IntBuffer {
    val buffer = if (direct) {
        ByteBuffer.allocateDirect(size * Int.SIZE_BYTES)
    } else {
        ByteBuffer.allocate(size * Int.SIZE_BYTES)
    }
    return buffer
        .order(order)
        .asIntBuffer()
        .put(this)
        .apply {
            rewind()
        }
}


@SuppressLint("HalfFloat")
fun FloatArray.toHalfBuffer(): ByteBuffer {
    val buffer = ByteBuffer.allocate(size * 2).order(ByteOrder.nativeOrder())
    val shortBuffer = buffer.asShortBuffer()
    for (v in this) {
        shortBuffer.put(Half.toHalf(v))
    }
    buffer.rewind()
    return buffer
}

@SuppressLint("HalfFloat")
fun ByteArray.toHalfFloatArray(): FloatArray {
    val floatBuffer = FloatBuffer.allocate(size / 2)
    val shortBuffer = ByteBuffer.wrap(this).order(ByteOrder.nativeOrder()).asShortBuffer()
    for (value in 0 until shortBuffer.capacity()) {
        floatBuffer.put(Half.toFloat(shortBuffer.get()))
    }
    floatBuffer.rewind()
    return floatBuffer.array()
}

fun ByteArray.toFile(file: File) {
    file.outputStream().use {
        it.write(this)
    }
}

fun ByteBuffer.toFile(file: File) {
    rewind()
    array().toFile(file)
}