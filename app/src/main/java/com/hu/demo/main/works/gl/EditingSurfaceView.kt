/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - EditingSurfaceView.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/09/20
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery		2024/09/20		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl

import android.content.Context
import android.opengl.EGLConfig
import android.util.AttributeSet
import android.view.Choreographer
import android.view.SurfaceView
import androidx.graphics.opengl.GLFrameBufferRenderer
import androidx.graphics.opengl.GLRenderer
import androidx.graphics.opengl.GLRenderer.EGLContextCallback
import androidx.graphics.opengl.egl.EGLManager
import androidx.graphics.opengl.egl.EGLSpec
import com.hu.demo.main.works.gl.renderer.IRenderCallback
import com.hu.demo.main.works.gl.renderer.RendererAdapter
import com.hu.demo.main.works.gl.renderer.brighten.HdrModeManager
import com.hu.demo.main.works.gl.renderer.param.KeyName
import com.hu.demo.main.works.gl.renderer.param.NodePath
import com.hu.demo.main.works.gl.renderer.normal.RendererGroup

class EditingSurfaceView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
) : SurfaceView(context, attrs), IEditingView {
    private val choreographer: Choreographer = Choreographer.getInstance()
    private val frameCallback = Choreographer.FrameCallback {
        bufferRenderer.render()
    }
    lateinit var glRenderer: GLRenderer
        private set
    lateinit var bufferRenderer: GLFrameBufferRenderer
        private set
    lateinit var rendererAdapter: RendererAdapter
        private set

    /**
     * 初始化[GLRenderer]，要求此初始化需在比较前的时序调用
     *
     * @param eglSpec 用于访问独立于 EGL 版本的各种 EGL 设施的接口
     * @param eglConfigFactory 调用回调以确定用于创建 EGL 上下文的适当 EGLConfig
     */
    fun initGlRenderer(eglSpec: EGLSpec, eglConfigFactory: EGLManager.() -> EGLConfig) {
        glRenderer = GLRenderer(
            eglSpecFactory = { eglSpec },
            eglConfigFactory = eglConfigFactory
        ).apply {
            start(EDITING_GL_THREAD)
        }
    }

    /**
     * 初始化[RendererAdapter](渲染器的适配层，连接真正的渲染器)以及[GLFrameBufferRenderer](帧缓冲区对象的类)，
     * 要求调用前必须调用[initGlRenderer]方法
     *
     * @param pipelineEditMode 管线对HDR的编辑模式
     */
    fun initBufferRenderer(hdrModeManager: HdrModeManager = HdrModeManager(this)) {
        rendererAdapter = RendererAdapter(glRenderer, hdrModeManager)
        registerGlCallback(rendererAdapter)

        bufferRenderer = GLFrameBufferRenderer.Builder(this, rendererAdapter)
            .setGLRenderer(glRenderer)
            .build()
    }

    /**
     * 添加EGLContextCallback以接收构造和销毁 EGL 依赖项的回调。
     * 这些回调在支持线程上调用。
     *
     * @param callback 回调接口
     */
    fun registerGlCallback(callback: EGLContextCallback) {
        glRenderer.registerEGLContextCallback(callback)
    }

    /**
     * 删除EGLContextCallback以不再接收用于构造和销毁 EGL 依赖项的回调。
     *
     * @param callback 回调接口
     */
    fun unregisterGlCallback(callback: EGLContextCallback) {
        glRenderer.unregisterEGLContextCallback(callback)
    }

    /**
     * 注册渲染的回调
     *
     * @param callback 渲染的回调
     */
    fun registerRenderCallback(callback: IRenderCallback) {
        rendererAdapter.registerRenderCallback(callback)
    }

    /**
     * 反注册渲染的回调
     *
     * @param callback 渲染的回调
     */
    fun unregisterRenderCallback(callback: IRenderCallback) {
        rendererAdapter.unregisterRenderCallback(callback)
    }

    override fun postRender() {
        choreographer.removeFrameCallback(frameCallback)
        choreographer.postFrameCallback(frameCallback)
    }

    override fun <T> setDataAndRender(keyName: KeyName, value: T) {
        setData(keyName, value)
        postRender()
    }

    override fun setDatasAndRender(values: Map<KeyName, Any?>) {
        setData(values)
        postRender()
    }

    override fun setData(keyName: KeyName, value: Any?) {
        rendererAdapter.setData(keyName, value)
    }

    override fun setData(values: Map<KeyName, Any?>) {
        rendererAdapter.setData(values)
    }

    override suspend fun <T> getData(keyName: KeyName): T? {
        return rendererAdapter.getData(keyName)
    }

    override fun changeNode(func: (RendererGroup) -> Unit) {
        rendererAdapter.changeNode(func)
    }

    companion object {
        private const val EDITING_GL_THREAD = "EditingGLThread"
    }
}

interface IEditingView {
    /**
     * 执行一次渲染
     */
    fun postRender()

    /**
     * 设置数据后渲染
     *
     * @param keyName 参数的key
     * @param value 参数值
     */
    fun <T> setDataAndRender(keyName: KeyName, value: T)

    /**
     * 设置数据后渲染
     *
     * @param values 需要设置的参数集合
     */
    fun setDatasAndRender(values: Map<KeyName, Any?>)

    /**
     * 设置参数，一般情况下，设置的参数需是[NodePath.STABLE_NP]下的
     *
     * @param keyName key名称
     * @param value 值
     */
    fun setData(keyName: KeyName, value: Any?)

    /**
     * 设置参数，一般情况下，设置的参数需是[NodePath.STABLE_NP]下的
     *
     * @param values 需要设置的参数集合
     */
    fun setData(values: Map<KeyName, Any?>)

    /**
     * 通过[keyName]获取数据
     *
     * @param keyName 数据的key，数据一般是[NodePath.STABLE_NP]下的内容
     *
     * @return 返回获取到到的指定的数据
     */
    suspend fun <T> getData(keyName: KeyName): T?

    /**
     * 修改节点树
     */
    fun changeNode(func: (RendererGroup) -> Unit)
}