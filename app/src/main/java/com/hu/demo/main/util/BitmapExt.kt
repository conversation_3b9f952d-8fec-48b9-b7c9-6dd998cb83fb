package com.hu.demo.main.util

import android.graphics.Bitmap
import android.os.ParcelFileDescriptor
import android.os.ParcelFileDescriptor.MODE_WRITE_ONLY
import com.hu.demo.buffer.Buffer
import java.io.File

fun Bitmap.toRgbaFileN(file: File) {
    file.parentFile?.mkdirs()
    file.delete()
    runCatching {
        file.createNewFile()
        ParcelFileDescriptor.open(file, MODE_WRITE_ONLY).use { pfd ->
            Buffer.toPixels(this, pfd.detachFd())
        }
    }.onFailure {
        file.delete()
    }
}
