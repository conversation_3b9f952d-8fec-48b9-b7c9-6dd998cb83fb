package com.hu.demo.main.adapter

import android.annotation.SuppressLint
import androidx.recyclerview.widget.RecyclerView
import com.hu.demo.main.works.recyclerview.view.IEditable

abstract class DefaultAdapter<DATA : BaseData, VH : BaseVH<DATA>>(
    protected val recyclerView: RecyclerView,
) : RecyclerView.Adapter<VH>(), IEditable {
    val dataList = mutableListOf<DATA>()
    override var isEditMode: Boolean = false
        @SuppressLint("NotifyDataSetChanged")
        set(value) {
            field = value
            notifyDataSetChanged()
        }

    override fun getItemId(position: Int): Long {
        return dataList[position].hashCode().toLong()
    }

    override fun getItemViewType(position: Int): Int {
        return dataList[position].type
    }

    override fun getItemCount(): Int {
        return dataList.size
    }

    fun resetData(newDatas: List<DATA>) {
        dataList.clear()
        dataList.addAll(newDatas)
    }
}