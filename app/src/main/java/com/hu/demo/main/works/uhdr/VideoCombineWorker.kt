package com.hu.demo.main.works.uhdr

import android.annotation.SuppressLint
import android.content.Context
import android.media.MediaCodec
import android.media.MediaExtractor
import android.media.MediaFormat
import android.media.MediaMuxer
import android.media.MediaMuxer.OutputFormat.MUXER_OUTPUT_MPEG_4
import android.util.Log
import androidx.work.Data
import androidx.work.WorkerParameters
import com.oplus.tblplayer.utils.LogUtil
import java.io.File
import java.nio.ByteBuffer

class VideoCombineWorker(context: Context, workerParams: WorkerParameters) : BaseWorker(context, workerParams) {
    @SuppressLint("WrongConstant")
    override fun doWork(): Result {
        val inputDir = inputData.getString(INPUT_DIR)?.let { File(it) } ?: run {
            Log.e(TAG, "doWork: 输入文件路径为空")
            return Result.failure()
        }

        if (!inputDir.exists() || !inputDir.isDirectory) {
            Log.e(TAG, "doWork: 输入目录不存在或不是文件: ${inputDir.absolutePath}")
            return Result.failure()
        }

        val outputDir = inputData.getString(OUTPUT_DIR)?.let { File(it) } ?: inputDir

        outputDir.mkdirs()

        return try {
            val success = doCombine(inputDir, outputDir)

            if (success) {
                Result.success(Data.Builder().putAll(mapOf(OUTPUT_COUNT to 1)).build())
            } else {
                Result.failure()
            }
        } catch (e: Exception) {
            Log.e(TAG, "doWork: 视频分割过程中发生未知异常", e)
            Result.failure()
        }
    }

    @SuppressLint("WrongConstant")
    private fun doCombine(inputDir: File, outputDir: File): Boolean {
        val files = inputDir.listFiles {
            it.isFile && it.name.endsWith(".mp4")
        }?.sortedWith { first, second ->
            if (first.name.length > second.name.length) {
                1
            } else if (first.name.length < second.name.length) {
                -1
            } else {
                first.name.compareTo(second.name)
            }

        }

        if (files.isNullOrEmpty()) {
            return false
        }

        val outputFile = File(outputDir, files.first().name + "_combine." + files.first().extension)
        outputFile.delete()
        val mediaMuxer = MediaMuxer(outputFile.absolutePath, MUXER_OUTPUT_MPEG_4)
        val extractor = MediaExtractor()
        extractor.setDataSource(files.first().absolutePath)
        val trackIndex = mediaMuxer.addTrack(extractor.getTrackFormat(extractor.getVideoTrack()))
        mediaMuxer.setOrientationHint(extractor.getRotation())
        extractor.release()

        val buffer = ByteBuffer.allocateDirect(4 * 1024 * 1024) // 2MB缓冲区，提高性能
        val bufferInfo = MediaCodec.BufferInfo()

        mediaMuxer.start()
        var lastEndTime = 0L
        try {
            files.forEachIndexed { index, file ->
                Log.d(TAG, "doCombine: 合并文件：$file")
                lastEndTime = mediaMuxer.muxVideo(trackIndex, buffer, bufferInfo, index, file, lastEndTime)
            }
        } catch (e: Exception) {
            Log.e(TAG, "doCombine: ", e)
            return false
        } finally {
            mediaMuxer.stop()
            mediaMuxer.release()
        }

        return true
    }

    /**
     * 合并视频
     * @param file 输入文件
     * @param buffer 缓冲区
     * @param bufferInfo 缓冲区信息
     * @param lastEndTime 上一个文件的结束时间
     * @return 合并后的结束时间
     */
    @SuppressLint("WrongConstant")
    private fun MediaMuxer.muxVideo(
        trackIndex: Int,
        buffer: ByteBuffer,
        bufferInfo: MediaCodec.BufferInfo,
        index: Int,
        file: File,
        lastEndTime: Long
    ): Long {
        // 初始化提取器
        val extractor = MediaExtractor()
        extractor.setDataSource(file.absolutePath)
        try {
            var effectiveSampleTime = 0L
            // 获取视频轨道信息
            val videoTrackIndex = extractor.getVideoTrack()
            if (videoTrackIndex < 0) {
                LogUtil.e(TAG, "doWork: cannot find video track")
                extractor.release()
                return lastEndTime
            }

            extractor.selectTrack(videoTrackIndex)
            extractor.seek(0)
            var frameCount = 0
            do {
                buffer.clear()
                val sampleSize = extractor.readSampleData(buffer, 0)
                val sampleTime = extractor.sampleTime
                frameCount++
                if (sampleTime < 0) break

                effectiveSampleTime = sampleTime
                bufferInfo.set(0, sampleSize, lastEndTime + effectiveSampleTime, extractor.sampleFlags)

                if (frameCount > 0 || index == 0) {
                    writeSampleData(trackIndex, buffer, bufferInfo)
                }
            } while (extractor.advance())
            extractor.unselectTrack(videoTrackIndex)
            return lastEndTime + effectiveSampleTime
        } finally {
            extractor.release()
        }

    }

    private fun MediaExtractor.seek(timeUs: Long): Boolean {
        Log.d(TAG, "seek: try seek to ${timeUs}us")

        val mode = arrayOf(
            MediaExtractor.SEEK_TO_CLOSEST_SYNC,
            MediaExtractor.SEEK_TO_NEXT_SYNC,
            MediaExtractor.SEEK_TO_PREVIOUS_SYNC
        ).firstOrNull {
            seekTo(timeUs, it)
            sampleTime >= 0
        }

        return mode != null
    }

    private fun MediaExtractor.getRotation(): Int {
        return getTrackFormat(getVideoTrack()).getInteger(MediaFormat.KEY_ROTATION, 0)
    }

    private fun MediaExtractor.getVideoTrack(): Int {
        for (index in 0 until trackCount) {
            val format = getTrackFormat(index)
            val mime = format.getString(MediaFormat.KEY_MIME)!!
            if (mime.startsWith(MIME_VIDEO_START)) {
                return index
            }
        }
        return -1
    }

    companion object {
        private const val TAG = "VideoSplitWorker"
        private const val MIME_VIDEO_START = "video/"
    }
}