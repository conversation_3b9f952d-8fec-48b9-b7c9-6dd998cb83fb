/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - IEditable.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2022/01/18
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * huca<PERSON><PERSON>@Apps.Gallery		2022/01/18		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.recyclerview.view

interface IEditable {
    /**
     * 是否是编辑模式，当在Adapter中编辑模式可以通过调用notifyDataSetChange刷新，但在ViewHolder中禁止通过set方法进行刷新
     */
    var isEditMode: Boolean

    companion object {
        const val KEY_IS_EDIT = "isEdit.key"
    }
}