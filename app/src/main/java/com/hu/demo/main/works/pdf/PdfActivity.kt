package com.hu.demo.main.works.pdf

import android.app.ProgressDialog
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.Environment
import android.os.Environment.DIRECTORY_DOCUMENTS
import android.util.Log
import android.view.View
import android.view.View.OnClickListener
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.Toast
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.contract.ActivityResultContracts
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.hu.demo.base.provider.DFileProvider
import com.hu.demo.main.R
import com.hu.demo.main.adapter.BaseData
import com.hu.demo.main.adapter.BaseVH
import com.hu.demo.main.adapter.DefaultAdapter
import com.hu.demo.utils.setImageUriAny
import com.hu.demo.base.ui.BaseActivity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File


class PdfActivity : BaseActivity(), OnClickListener {
    private var selectImageAdapter: ImageAdapter? = null
    private var rvImages: RecyclerView? = null
    private var btnSelectImg: Button? = null
    private var btnToPdf: Button? = null
    private val mediaRegex = Regex("^(image|video)/.*$")

    private val getContentAllMultiLauncher = registerForActivityResult(
        ActivityResultContracts.GetMultipleContents(),
        PickMultiCallback { changeData(it) }
    )

    override fun getLayoutId(): Int {
        return R.layout.activity_pdf
    }

    override fun initToolbar() {
        super.initToolbar()
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowTitleEnabled(true)
            setDisplayShowHomeEnabled(false)
        }
    }

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        rvImages = findViewById(R.id.rvImages)
        btnSelectImg = findViewById(R.id.btnSelectImg)
        btnToPdf = findViewById(R.id.btnToPdf)
    }

    override fun initEvent(savedInstanceState: Bundle?) {
        super.initEvent(savedInstanceState)
        rvImages?.apply {
            addItemDecoration(DividerItemDecoration(this@PdfActivity, RecyclerView.HORIZONTAL))
            adapter = ImageAdapter(this).apply {
                resetData(emptyList())
                selectImageAdapter = this
            }
            layoutManager = LinearLayoutManager(this@PdfActivity, RecyclerView.HORIZONTAL, false)
        }
        btnSelectImg?.setOnClickListener(this)
        btnToPdf?.setOnClickListener(this)
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.btnSelectImg -> {
                getContentAllMultiLauncher.launch(MIME_TYPE)
            }

            R.id.btnToPdf -> {
                selectImageAdapter?.dataList?.also {
                    val progressDialog = ProgressDialog(this)
                    lifecycleScope.launch(Dispatchers.Default) {
                        lifecycleScope.launch(Dispatchers.Main) {
                            progressDialog.show()
                        }
                        val list = it.mapNotNull { it.uri }
                        val file = PdfCreator.savePdfToExternal(
                            this@PdfActivity,
                            list,
                            Environment.getExternalStoragePublicDirectory(DIRECTORY_DOCUMENTS)!!
                        ) { msg ->
                            lifecycleScope.launch(Dispatchers.Main) {
                                progressDialog.setMessage(msg)
                            }
                        } ?: run {
                            Log.e(TAG, "onClick: file is null")
                            return@launch
                        }
                        lifecycleScope.launch(Dispatchers.Main) {
                            progressDialog.dismiss()
                            startActivity(getPdfFileIntent(file))
                        }
                    }
                }
            }
        }
    }

    // android获取一个用于打开PDF文件的intent
    private fun getPdfFileIntent(file: File): Intent {
        val intent = Intent(Intent.ACTION_VIEW)
        intent.addCategory(Intent.CATEGORY_DEFAULT)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        val uri = DFileProvider.getUriForFile(this, file)
        intent.setDataAndType(uri, "application/pdf")
        intent.setFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        return intent
    }

    @Suppress("NotifyDataSetChanged", "UNCHECKED_CAST")
    private fun changeData(obj: Any?) {
        val list = (obj as List<Uri>).mapNotNull {
            if (contentResolver.getType(it)?.matches(mediaRegex) == true) {
                UriData(it)
            } else null
        }
        selectImageAdapter?.resetData(list)
        selectImageAdapter?.notifyDataSetChanged()
        btnToPdf?.isEnabled = true
        Toast.makeText(this, "选择${list.size}个", Toast.LENGTH_SHORT).show()
    }

    data class UriData(val uri: Uri?) : BaseData(TYPE_URI)

    class UriVH(parent: ViewGroup) : BaseVH<UriData>(parent, R.layout.item_clipboard_uri) {
        private val ivImage: ImageView = findViewById(R.id.ivImage)

        override fun bind(extraData: Map<String, Any>, data: UriData, position: Int) {
            ivImage.setImageUriAny(data.uri, 800)
        }
    }

    class ImageAdapter(recyclerView: RecyclerView) : DefaultAdapter<UriData, BaseVH<UriData>>(recyclerView) {
        @Suppress("UNCHECKED_CAST")
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH<UriData> {
            return UriVH(parent)
        }

        override fun onBindViewHolder(holder: BaseVH<UriData>, position: Int) {
            holder.bind(emptyMap(), dataList[position], position)
        }
    }

    private open class PickMultiCallback(private val resultFunc: (List<Uri>) -> Unit) : ActivityResultCallback<List<Uri>> {
        override fun onActivityResult(result: List<Uri>) {
            if (result.isEmpty()) {
                Log.d(TAG, "PickMultiCallback.onActivityResult: ")
                return
            }
            Log.d(TAG, "onActivityResult: $result")
            resultFunc(result)
        }
    }

    companion object {
        private const val TAG = "PdfActivity"
        private const val MIME_TYPE = "image/*"
        private const val TYPE_URI = 0
    }
}