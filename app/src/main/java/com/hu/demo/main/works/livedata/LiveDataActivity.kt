package com.hu.demo.main.works.livedata

import android.os.Bundle
import android.widget.Button
import android.widget.TextView
import androidx.lifecycle.MutableLiveData
import com.hu.demo.base.ui.BaseActivity
import com.hu.demo.main.R
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

class LiveDataActivity : BaseActivity() {
    private var tvText: TextView? = null
    private var btnUpdate: Button? = null
    private var btnCreateNoData: Button? = null
    private var btnCreateHaveData: Button? = null
    private var btnCreateNoDataAndUpdate: Button? = null
    private var btnCreateHaveDataAndUpdate: Button? = null
    private var btnClear: Button? = null
    private var livedata: MutableLiveData<String>? = null

    override fun getLayoutId(): Int = R.layout.activity_live_data

    override fun initToolbar() {
        super.initToolbar()
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowTitleEnabled(true)
            setDisplayShowHomeEnabled(false)
        }
    }

    override fun initView(savedInstanceState: Bundle?) {
        tvText = findViewById(R.id.tvText)

        btnCreateNoData = findViewById(R.id.btnCreateNoData)
        btnCreateHaveData = findViewById(R.id.btnCreateHaveData)

        btnCreateNoDataAndUpdate = findViewById(R.id.btnCreateNoDataAndUpdate)
        btnCreateHaveDataAndUpdate = findViewById(R.id.btnCreateHaveDataAndUpdate)

        btnUpdate = findViewById(R.id.btnUpdate)
        btnClear = findViewById(R.id.btnClear)
    }

    override fun initEvent(savedInstanceState: Bundle?) {
        btnUpdate?.setOnClickListener {
            livedata?.postValue("更新: ${DateTimeFormatter.ISO_DATE_TIME.format(LocalDateTime.now())}")
        }
        btnCreateNoData?.setOnClickListener {
            initLiveData(MutableLiveData())
        }
        btnCreateHaveData?.setOnClickListener {
            initLiveData(MutableLiveData("有初值: ${DateTimeFormatter.ISO_DATE_TIME.format(LocalDateTime.now())}"))
        }
        btnCreateNoDataAndUpdate?.setOnClickListener {
            val livedata = MutableLiveData<String>()
            livedata.postValue("无初值更新: ${DateTimeFormatter.ISO_DATE_TIME.format(LocalDateTime.now())}")
            initLiveData(livedata)
        }
        btnCreateHaveDataAndUpdate?.setOnClickListener {
            val liveData = MutableLiveData("初值: ${DateTimeFormatter.ISO_DATE_TIME.format(LocalDateTime.now())}")
            liveData.postValue("有初值更新: ${DateTimeFormatter.ISO_DATE_TIME.format(LocalDateTime.now())}")
            initLiveData(liveData)
        }
        btnClear?.setOnClickListener {
            tvText?.text = ""
        }
    }

    private fun initLiveData(liveData: MutableLiveData<String>) {
        livedata = liveData
        livedata?.observe(this) {
            tvText?.apply {
                append(it)
                append("\n")
            }
        }
    }

    companion object {
        private const val TAG = "LiveDataActivity"
    }
}