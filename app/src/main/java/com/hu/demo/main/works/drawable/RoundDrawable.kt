package com.hu.demo.main.works.drawable

import android.graphics.Canvas
import android.graphics.Color
import android.graphics.ColorFilter
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PixelFormat
import android.graphics.drawable.Drawable
import androidx.core.graphics.toRectF

class RoundDrawable(
    drawable: Drawable? = null,
    private val background: Drawable? = null,
    val rotate: Float = 0f,
    radius: Float = 0f,
    lineWidth: Float = 0f,
    lineColor: Int = Color.TRANSPARENT,
    drawType: DrawType = DrawType.CENTER_CROP
) : Drawable() {
    var drawType: DrawType = drawType
        set(value) {
            field = value
            invalidateSelf()
        }
    var drawable: Drawable? = drawable
        set(value) {
            field = value
            invalidateSelf()
        }
    var radius: Float = radius
        set(value) {
            field = value
            invalidateSelf()
        }
    private val linePaint = Paint().apply {
        isAntiAlias = true
        style = Paint.Style.STROKE
        strokeWidth = lineWidth
        color = lineColor
    }

    override fun draw(canvas: Canvas) {
        canvas.save()
        val path = Path().apply {
            addRoundRect(bounds.toRectF(), radius, radius, Path.Direction.CW)
        }
        canvas.clipPath(path)
        canvas.drawDrawable(background, bounds.width().toFloat(), bounds.height().toFloat())
        canvas.drawDrawable(drawable, bounds.width().toFloat(), bounds.height().toFloat(), rotate, drawType)
        canvas.drawPath(path, linePaint)
        canvas.restore()
    }

    override fun setAlpha(alpha: Int) {
        drawable?.alpha = alpha
    }

    override fun getAlpha(): Int {
        return drawable?.alpha ?: ALPHA_OPAQUE
    }

    override fun setColorFilter(colorFilter: ColorFilter?) {
        drawable?.colorFilter = colorFilter
    }

    override fun getColorFilter(): ColorFilter? {
        return drawable?.colorFilter
    }

    @Deprecated("Deprecated in Java")
    override fun getOpacity(): Int {
        return drawable?.opacity ?: PixelFormat.TRANSPARENT
    }

    companion object {
        const val ALPHA_OPAQUE = 255
    }
}

fun Drawable.toRoundDrawable(
    background: Drawable? = null,
    rotate: Float = 0f,
    radius: Float = 0f,
    lineWidth: Float = 0f,
    lineColor: Int = Color.TRANSPARENT,
    drawType: DrawType = DrawType.CENTER_CROP
): RoundDrawable {
    return RoundDrawable(this, background, rotate, radius, lineWidth, lineColor, drawType)
}