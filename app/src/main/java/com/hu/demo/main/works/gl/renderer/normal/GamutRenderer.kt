/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - GamutRenderer.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/10/09
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery		2024/10/09		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.renderer.normal

import android.graphics.ColorSpace
import android.graphics.RectF
import android.opengl.GLES30
import android.opengl.Matrix
import androidx.annotation.IntDef
import com.hu.demo.main.works.gl.canvas.FboGlCanvas
import com.hu.demo.main.works.gl.canvas.FboKey
import com.hu.demo.main.works.gl.canvas.GlCanvas
import com.hu.demo.main.works.gl.renderer.addToPool
import com.hu.demo.main.works.gl.renderer.getGamutMode
import com.hu.demo.main.works.gl.renderer.normal.GamutRenderer.GamutMode.Companion.MODE_BT709
import com.hu.demo.main.works.gl.renderer.normal.GamutRenderer.GamutMode.Companion.MODE_GAMMA_2_2
import com.hu.demo.main.works.gl.renderer.normal.GamutRenderer.GamutMode.Companion.MODE_HLG
import com.hu.demo.main.works.gl.renderer.normal.GamutRenderer.GamutMode.Companion.MODE_LINEAR
import com.hu.demo.main.works.gl.renderer.normal.GamutRenderer.GamutMode.Companion.MODE_PQ
import com.hu.demo.main.works.gl.renderer.normal.GamutRenderer.GamutMode.Companion.MODE_SRGB
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_COLOR_SPACE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_IMAGE_TEXTURE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_OUTPUT_TEXTURE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.getKey
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.NEXT_NP
import com.hu.demo.main.works.gl.renderer.param.RenderArgs
import com.hu.demo.main.works.gl.shader.GlShader
import com.hu.demo.main.works.gl.shader.ProgramShader
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.CONST_TF_PARAMS_BT709
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.CONST_TF_PARAMS_GAMMA_2_2
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.CONST_TF_PARAMS_HLG
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.CONST_TF_PARAMS_PQ
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.CONST_TF_PARAMS_SRGB
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.EXEC_CALC_CLAMP
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.EXEC_COLOR_TRANSFORM
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.EXEC_EOTF
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.EXEC_OETF
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.EXEC_SET_FRAGCOLOR
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.EXEC_SET_GL_POSITION
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.EXEC_SET_TEXCOORD
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.EXEC_TEXTURE_COLOR
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_IN_VEC2_TEX_COORD
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_LAYOUT_IN_VEC3_POSITION
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_LAYOUT_IN_VEC3_TEX_COORD
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_OUT_VEC2_TEX_COORD
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_OUT_VEC4_FRAGCOLOR
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_UNIFORM_FLOAT_CLAMP
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_UNIFORM_FLOAT_DEST_CLAMP
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_UNIFORM_INT_DEST_MODE
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_UNIFORM_INT_SRC_MODE
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_UNIFORM_MAT3_GAMUT_TRANSFORM
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_UNIFORM_MAT4_MODEL_M
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_UNIFORM_MAT4_PROJECTION_M
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_UNIFORM_MAT4_VIEW_M
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_UNIFORM_SAMPLER2D_TEX
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.METHOD_BT709_EOTF
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.METHOD_BT709_OETF
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.METHOD_EOTF
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.METHOD_GAMMA_2_2_EOTF
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.METHOD_GAMMA_2_2_OETF
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.METHOD_HLG_EOTF
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.METHOD_HLG_OETF
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.METHOD_OETF
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.METHOD_PQ_EOTF
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.METHOD_PQ_OETF
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.METHOD_SRGB_EOTF
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.METHOD_SRGB_OETF
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.STRUCT_TF_HLG_PARAMS
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.STRUCT_TF_PARAMS
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.STRUCT_TF_PQ_PARAMS
import com.hu.demo.main.works.gl.texture.ITexture
import com.hu.demo.main.works.gl.texture.MsgTexture
import com.hu.demo.main.works.gl.texture.RawTexture
import com.hu.demo.main.works.gl.utils.AttributeShaderParam
import com.hu.demo.main.works.gl.utils.BufferDataBinder
import com.hu.demo.main.works.gl.utils.GlProgram
import com.hu.demo.main.works.gl.utils.GlUtil
import com.hu.demo.main.works.gl.utils.ShaderParam
import com.hu.demo.main.works.gl.utils.Uniform1fShaderParam
import com.hu.demo.main.works.gl.utils.Uniform1iShaderParam
import com.hu.demo.main.works.gl.utils.UniformMat3fvShaderParam
import com.hu.demo.main.works.gl.utils.UniformMat4fvShaderParam
import com.hu.demo.main.works.gl.utils.toBuffer
import com.hu.demo.utils.ColorSpaceExt.SRGB
import com.hu.demo.utils.getOrLog
import com.hu.demo.utils.mul3x3MM
import kotlin.math.min

/**
 * 执行对图片纹理应用gamma
 *
 * 输入：
 *
 * | key                       | 类型          | 是否必须 | 解释                                                               |
 * | -                         | -             | -       | -                                                                  |
 * | [KEY_NEXT_TEXTURE]        | [Texture]     | 是      | 输入纹理                                                            |
 * | [KEY_NEXT_COLOR_SPACE]  | [ColorSpace]  | 是      | 目标的色域                                                          |
 * | [KEY_NEXT_OUTPUT_TEXTURE] | [Texture]     | 否      | 渲染到目标纹理                                                      |
 *
 * 输出：
 *
 * | key                | 类型       | 是否必须 | 解释      |
 * | -                  | -          | -       | -         |
 * | [KEY_NEXT_TEXTURE] | [Texture]  | 是      | 输出纹理  |
 */
class GamutRenderer private constructor() : Renderer(TAG) {
    // 顶点着色器中attribute、uniform
    private val aPosition = AttributeShaderParam("aPosition", POSITION_SIZE)
    private val aTexCoord = AttributeShaderParam("aTexCoord", COORD_SIZE)
    private val uViewM = UniformMat4fvShaderParam("uViewM")
    private val uModelM = UniformMat4fvShaderParam("uModelM")
    private val uProjectionM = UniformMat4fvShaderParam("uProjectionM")

    // 片段着色器中attribute、uniform
    private val uTex = Uniform1iShaderParam("uTex")
    private val uGamutTransform = UniformMat3fvShaderParam("uGamutTransform")
    private val uSrcMode = Uniform1iShaderParam("uSrcMode")
    private val uDestMode = Uniform1iShaderParam("uDestMode")
    private val uClamp = Uniform1fShaderParam("uClamp")
    private val uDestClamp = Uniform1fShaderParam("uDestClamp")
    override val handleParams: Array<ShaderParam> = arrayOf(
        aPosition,
        aTexCoord,
        uViewM,
        uModelM,
        uProjectionM,
        uTex,
        uGamutTransform,
        uSrcMode,
        uDestMode,
        uClamp,
        uDestClamp
    )

    private val viewM = FloatArray(MATRIX_SIZE).apply {
        Matrix.setIdentityM(this, 0)
    }
    private val modelM = FloatArray(MATRIX_SIZE).apply {
        Matrix.setIdentityM(this, 0)
    }
    private val projectionM = FloatArray(MATRIX_SIZE).apply {
        Matrix.setIdentityM(this, 0)
    }

    override val programShader: ProgramShader = ProgramShader(VS_SHADER_BUILDER, FS_SHADER_BUILDER)

    override fun install(shader: ProgramShader?): GlProgram {
        val glProgram = super.install(shader)!!
        // 如果shader为null，则代表program是当前Renderer内部通过programShader生成的，需要设定相关参数
        if (shader == null) {
            glProgram.use()
            val bufferDataBinder = BufferDataBinder.obtain(vertices)
            bufferDataBinder.install()

            bufferDataBinder.use()
            aPosition.setValue(STRIDE, POSITION_OFFSET)
            aTexCoord.setValue(STRIDE, COORD_OFFSET)
            bufferDataBinder.unUse()

            Matrix.setIdentityM(viewM, 0)
            uViewM.setValue(viewM)
            glProgram.unUse()
        }

        return glProgram
    }

    override fun render(renderArgs: RenderArgs, glProgram: GlProgram?) {
        glProgram!!

        val texture = renderArgs.get<ITexture>(NEXT_NP.getKey(KEY_IMAGE_TEXTURE))
            ?.addToPool(renderArgs).getOrLog(TAG, "texture") ?: return
        texture.load()

        glProgram.use()

        val portWidth = texture.width
        val portHeight = texture.height

        val srcColorSpace = texture.displayColorSpace
        val destColorSpace = renderArgs.get<ColorSpace>(NEXT_NP.getKey(KEY_COLOR_SPACE)) ?: SRGB
        val srcD65 = ColorSpace.adapt(srcColorSpace, ColorSpace.ILLUMINANT_D65) as ColorSpace.Rgb
        val destD65 = ColorSpace.adapt(destColorSpace, ColorSpace.ILLUMINANT_D65) as ColorSpace.Rgb

        // 色域转换矩阵
        val gamutTransform = destD65.inverseTransform mul3x3MM srcD65.transform
        uGamutTransform.setValue(gamutTransform)

        // 源模式
        val srcMode = srcColorSpace.getGamutMode()
        uSrcMode.setValue(srcMode)

        // 目标模式
        val destMode = destColorSpace.getGamutMode()
        uDestMode.setValue(destMode)

        Matrix.setIdentityM(viewM, 0)
        uViewM.setValue(viewM)

        calcModelM(texture, portWidth, portHeight)
        uModelM.setValue(modelM)

        calcProjectionM(portWidth, portHeight)
        uProjectionM.setValue(projectionM)

        val texUnitIndex0 = 0
        texture.active(texUnitIndex0)
        uTex.setValue(texUnitIndex0)

        // 纹理取值范围
        val clamp: Float = texture.clamp
        uClamp.setValue(clamp)

        val outTexture: ITexture
        if (glProgram.programShader == programShader) {
            val bufferDataBinder = BufferDataBinder.obtain(vertices)
            bufferDataBinder.bind()

            // 由于 OpenGL的状态机特性，以下的纹理active后的bind操作都会绑定到刚刚的纹理单元上，故需要提前创建输出纹理
            outTexture = (renderArgs.get<ITexture>(NEXT_NP.getKey(KEY_OUTPUT_TEXTURE))
                ?: RawTexture(portWidth, portHeight, destColorSpace, texture.texConfig))
                .addToPool(renderArgs)
            GLES30.glActiveTexture(GLES30.GL_TEXTURE31)
            outTexture.load()

            val destClamp: Float = outTexture.clamp
            uDestClamp.setValue(destClamp)

            val fboKey = FboKey(vertices.capacity() / STRIDE, portWidth, portHeight)
            val glCanvas = GlCanvas.obtain(fboKey) as FboGlCanvas
            glCanvas.draw(outTexture)
            GlUtil.checkGlError()
            bufferDataBinder.unbind()
            glCanvas.reuse()
        } else {
            outTexture = MsgTexture(portWidth, portHeight,0, destColorSpace, texture.texConfig)
            val destClamp: Float = outTexture.clamp
            uDestClamp.setValue(destClamp)
        }
        glProgram.unUse()
        renderArgs[NEXT_NP.getKey(KEY_IMAGE_TEXTURE)] = outTexture
    }

    private fun calcProjectionM(portWidth: Int, portHeight: Int) {
        Matrix.setIdentityM(projectionM, 0)
        Matrix.orthoM(projectionM, 0, 0f, portWidth.toFloat(), portHeight.toFloat(), 0f, -1f, 1f)
    }

    private fun calcModelM(texture: ITexture, portWidth: Int, portHeight: Int) {
        Matrix.setIdentityM(modelM, 0)
        val displayRect = calcDisplayRect(texture.width.toFloat(), texture.height.toFloat(), portWidth.toFloat(), portHeight.toFloat())
        Matrix.translateM(modelM, 0, displayRect.left, displayRect.top, 0f)
        Matrix.scaleM(modelM, 0, displayRect.width(), displayRect.height(), 1f)
    }

    private fun calcDisplayRect(imageWidth: Float, imageHeight: Float, portWidth: Float, portHeight: Float): RectF {
        val ratio = min(portWidth / imageWidth, portHeight / imageHeight)
        val displayWidth = imageWidth * ratio
        val displayHeight = imageHeight * ratio
        val left = (portWidth - displayWidth) / 2
        val top = (portHeight - displayHeight) / 2
        return RectF(left, top, left + displayWidth, top + displayHeight)
    }

    @Retention(AnnotationRetention.SOURCE)
    @IntDef(
        MODE_LINEAR,
        MODE_HLG,
        MODE_PQ,
        MODE_SRGB,
        MODE_BT709,
        MODE_GAMMA_2_2,
    )
    annotation class GamutMode {
        companion object {
            const val MODE_LINEAR = 0
            const val MODE_HLG = 1
            const val MODE_PQ = 2
            const val MODE_SRGB = 3
            const val MODE_BT709 = 4
            const val MODE_GAMMA_2_2 = 5
        }
    }

    companion object {
        private const val TAG = "GamutRenderer"

        private val VS_SHADER_BUILDER by lazy {
            GlShader().apply {
                addField(FIELD_LAYOUT_IN_VEC3_POSITION)
                addField(FIELD_LAYOUT_IN_VEC3_TEX_COORD)
                addField(FIELD_UNIFORM_MAT4_VIEW_M)
                addField(FIELD_UNIFORM_MAT4_MODEL_M)
                addField(FIELD_UNIFORM_MAT4_PROJECTION_M)
                addField(FIELD_OUT_VEC2_TEX_COORD)

                addExecEnd(EXEC_SET_GL_POSITION)
                addExecEnd(EXEC_SET_TEXCOORD)
            }
        }

        private val FS_SHADER_BUILDER by lazy {
            GlShader().apply {
                addStruct(STRUCT_TF_PARAMS)
                addStruct(STRUCT_TF_HLG_PARAMS)
                addStruct(STRUCT_TF_PQ_PARAMS)

                addConst(CONST_TF_PARAMS_SRGB)
                addConst(CONST_TF_PARAMS_GAMMA_2_2)
                addConst(CONST_TF_PARAMS_BT709)
                addConst(CONST_TF_PARAMS_BT709)
                addConst(CONST_TF_PARAMS_HLG)
                addConst(CONST_TF_PARAMS_PQ)

                addField(FIELD_IN_VEC2_TEX_COORD)
                addField(FIELD_OUT_VEC4_FRAGCOLOR)
                addField(FIELD_UNIFORM_SAMPLER2D_TEX)
                addField(FIELD_UNIFORM_MAT3_GAMUT_TRANSFORM)
                addField(FIELD_UNIFORM_INT_SRC_MODE)
                addField(FIELD_UNIFORM_INT_DEST_MODE)
                addField(FIELD_UNIFORM_FLOAT_CLAMP)
                addField(FIELD_UNIFORM_FLOAT_DEST_CLAMP)

                addMethod(METHOD_SRGB_EOTF)
                addMethod(METHOD_SRGB_OETF)
                addMethod(METHOD_HLG_EOTF)
                addMethod(METHOD_HLG_OETF)
                addMethod(METHOD_PQ_EOTF)
                addMethod(METHOD_PQ_OETF)
                addMethod(METHOD_BT709_EOTF)
                addMethod(METHOD_BT709_OETF)
                addMethod(METHOD_GAMMA_2_2_EOTF)
                addMethod(METHOD_GAMMA_2_2_OETF)
                addMethod(METHOD_EOTF)
                addMethod(METHOD_OETF)

                addExecStart(EXEC_TEXTURE_COLOR)
                addExec(EXEC_EOTF)
                addExec(EXEC_COLOR_TRANSFORM)
                addExec(EXEC_CALC_CLAMP)
                addExec(EXEC_OETF)
                addExecEnd(EXEC_SET_FRAGCOLOR)
            }
        }

        /**
         * 步长
         */
        private const val STRIDE = 5

        /**
         * 矩阵大小
         */
        private const val MATRIX_SIZE = 16

        /**
         * 顶点位置在buffer中的偏移
         */
        private const val POSITION_OFFSET = 0

        /**
         * 坐标在buffer中的偏移
         */
        private const val COORD_OFFSET = 3

        /**
         * 顶点位置的大小
         */
        private const val POSITION_SIZE = 3

        /**
         * 坐标的大小
         */
        private const val COORD_SIZE = 2

        /**
         * 顶点缓冲数组
         */
        private val vertices = floatArrayOf(
            // X, Y, Z, U, V
            0f, 0f, 0.0f, 0.0f, 1.0f,
            1f, 0f, 0.0f, 1.0f, 1.0f,
            0f, 1f, 0.0f, 0.0f, 0.0f,
            1f, 1f, 0.0f, 1.0f, 0.0f
        ).toBuffer()
    }
}