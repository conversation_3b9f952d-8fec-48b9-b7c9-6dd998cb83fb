package com.hu.demo.main.works.recyclerview.view

import android.view.ViewGroup

class AutoScroll(private val runCallback: (() -> Unit)) : Runnable {
    var isAutoScrolling = false
    private var viewGroup: ViewGroup? = null
    private var currentVelocity = 0

    fun setVelocityScale(scale: Float) {
        currentVelocity = (MAX_VELOCITY * scale.coerceIn(-1f..1f)).toInt()
    }

    fun start(viewGroup: ViewGroup) {
        if (this.viewGroup !== viewGroup) {
            cancel()
        }
        if (isAutoScrolling) return
        this.viewGroup = viewGroup
        isAutoScrolling = true
        viewGroup.apply { this.postOnAnimation(this@AutoScroll) }

    }

    fun cancel() {
        if (!isAutoScrolling) return
        isAutoScrolling = false
        setVelocityScale(0f)
        viewGroup?.removeCallbacks(this)
        viewGroup = null
    }

    override fun run() {
        val viewGroup = viewGroup ?: return
        runCallback.invoke()
        viewGroup.scrollBy(0, currentVelocity)
        viewGroup.postOnAnimation(this)
    }

    companion object {
        private const val MAX_VELOCITY = 20
    }
}