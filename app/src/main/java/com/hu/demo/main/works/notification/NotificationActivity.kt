package com.hu.demo.main.works.notification

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.os.Bundle
import android.widget.Button
import android.widget.EditText
import com.hu.demo.main.R
import com.hu.demo.base.ui.BaseActivity

class NotificationActivity : BaseActivity() {
    private var etText: EditText? = null
    private var btnSend: Button? = null
    private var btnClearChanelNotification: Button? = null
    private var index = 0
    override fun getLayoutId(): Int {
        return R.layout.activity_notification
    }

    override fun initToolbar() {
        super.initToolbar()
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowTitleEnabled(true)
            setDisplayShowHomeEnabled(false)
        }
    }

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        etText = findViewById(R.id.etText)
        btnSend = findViewById(R.id.btnSend)
        btnClearChanelNotification = findViewById(R.id.btnClearChanelNotification)
    }

    override fun initEvent(savedInstanceState: Bundle?) {
        super.initEvent(savedInstanceState)
        btnSend?.setOnClickListener {

            val channel = NotificationChannel(
                etText!!.text.toString(),
                etText!!.text.toString(),
                NotificationManager.IMPORTANCE_HIGH
            )
            getSystemService(NotificationManager::class.java)?.createNotificationChannel(channel)
            val notification = Notification.Builder(this, etText!!.text.toString())
                .setContentTitle("通知${index++}")
                .setContentText("测试通知")
                .setSmallIcon(R.mipmap.ic_launcher)
                .setAutoCancel(true)
                .setShowWhen(true)
                .setWhen(System.currentTimeMillis())
                .build()
            getSystemService(NotificationManager::class.java)?.notify(notification.hashCode(), notification)
        }
        btnClearChanelNotification?.setOnClickListener {
            getSystemService(NotificationManager::class.java)?.apply {
                activeNotifications?.forEach {
                    if (it.notification.channelId == etText!!.text.toString()) {
                        cancel(it.id)
                    }
                }
            }
        }
    }
}