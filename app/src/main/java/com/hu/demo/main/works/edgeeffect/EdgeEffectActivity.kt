package com.hu.demo.main.works.edgeeffect

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Button
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.contract.ActivityResultContract
import com.hu.demo.main.R
import com.hu.demo.base.ui.BaseActivity

class EdgeEffectActivity : BaseActivity(), View.OnClickListener {
    private var eeView: EdgeEffectView? = null
    private var btnSelectImg: Button? = null
    private val pickTypeResultContract = PickTypeResultContract()
    private val pickImageLauncher = registerForActivityResult(
        pickTypeResultContract,
        PickOnlyCallback { changeData(it) }
    )

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        eeView = findViewById(R.id.eeView)
        btnSelectImg = findViewById(R.id.btnSelectImg)
    }

    override fun initEvent(savedInstanceState: Bundle?) {
        super.initEvent(savedInstanceState)
        eeView?.setOnClickListener(this)
        btnSelectImg?.setOnClickListener(this)
    }

    override fun getLayoutId(): Int {
        return R.layout.activity_edge_effect
    }

    override fun initToolbar() {
        super.initToolbar()
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowTitleEnabled(true)
            setDisplayShowHomeEnabled(false)
        }
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.btnSelectImg -> {
                pickImageLauncher.launch(MIME_TYPE)
            }
        }
    }

    private fun changeData(obj: Any?) {
        runCatching {
            val eeView = eeView ?: return
            contentResolver.openFileDescriptor(obj as Uri, "r")?.use { pfd ->
                val option = BitmapFactory.Options().apply {
                    inJustDecodeBounds = true
                }
                BitmapFactory.decodeFileDescriptor(pfd.fileDescriptor, null, option)
                option.inJustDecodeBounds = false
                option.inSampleSize = (option.outWidth * option.outHeight) / (eeView.width * eeView.height)
                eeView.bitmap = BitmapFactory.decodeFileDescriptor(pfd.fileDescriptor, null, option)
            }
        }.onFailure {
            Log.e(TAG, "changeData: ", it)
        }
    }

    private open class PickOnlyCallback(private val resultFunc: (Uri) -> Unit) : ActivityResultCallback<Uri?> {
        override fun onActivityResult(result: Uri?) {
            result ?: run {
                Log.d(TAG, "PickOnlyCallback.onActivityResult: ")
                return
            }
            Log.d(TAG, "onActivityResult: $result")
            resultFunc(result)
        }
    }

    private class PickTypeResultContract : ActivityResultContract<String, Uri?>() {
        override fun createIntent(context: Context, input: String): Intent {
            return Intent(Intent.ACTION_PICK).setType(input)
        }

        override fun parseResult(resultCode: Int, intent: Intent?): Uri? {
            return if (resultCode != Activity.RESULT_OK) null else intent?.data
        }
    }

    companion object {
        private const val TAG = "EdgeEffectActivity"
        private const val MIME_TYPE = "image/*"
    }
}