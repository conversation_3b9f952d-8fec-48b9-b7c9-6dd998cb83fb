package com.hu.demo.main.works.videoinfo

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.media.MediaExtractor
import android.media.MediaFormat.TYPE_BYTE_BUFFER
import android.media.MediaFormat.TYPE_FLOAT
import android.media.MediaFormat.TYPE_INTEGER
import android.media.MediaFormat.TYPE_LONG
import android.media.MediaFormat.TYPE_STRING
import android.media.MediaMetadataRetriever
import android.media.MediaMetadataRetriever.METADATA_KEY_ALBUM
import android.media.MediaMetadataRetriever.METADATA_KEY_ALBUMARTIST
import android.media.MediaMetadataRetriever.METADATA_KEY_ARTIST
import android.media.MediaMetadataRetriever.METADATA_KEY_AUTHOR
import android.media.MediaMetadataRetriever.METADATA_KEY_BITRATE
import android.media.MediaMetadataRetriever.METADATA_KEY_BITS_PER_SAMPLE
import android.media.MediaMetadataRetriever.METADATA_KEY_CAPTURE_FRAMERATE
import android.media.MediaMetadataRetriever.METADATA_KEY_CD_TRACK_NUMBER
import android.media.MediaMetadataRetriever.METADATA_KEY_COLOR_RANGE
import android.media.MediaMetadataRetriever.METADATA_KEY_COLOR_STANDARD
import android.media.MediaMetadataRetriever.METADATA_KEY_COLOR_TRANSFER
import android.media.MediaMetadataRetriever.METADATA_KEY_COMPILATION
import android.media.MediaMetadataRetriever.METADATA_KEY_COMPOSER
import android.media.MediaMetadataRetriever.METADATA_KEY_DATE
import android.media.MediaMetadataRetriever.METADATA_KEY_DISC_NUMBER
import android.media.MediaMetadataRetriever.METADATA_KEY_DURATION
import android.media.MediaMetadataRetriever.METADATA_KEY_EXIF_LENGTH
import android.media.MediaMetadataRetriever.METADATA_KEY_EXIF_OFFSET
import android.media.MediaMetadataRetriever.METADATA_KEY_GENRE
import android.media.MediaMetadataRetriever.METADATA_KEY_HAS_AUDIO
import android.media.MediaMetadataRetriever.METADATA_KEY_HAS_IMAGE
import android.media.MediaMetadataRetriever.METADATA_KEY_HAS_VIDEO
import android.media.MediaMetadataRetriever.METADATA_KEY_IMAGE_COUNT
import android.media.MediaMetadataRetriever.METADATA_KEY_IMAGE_HEIGHT
import android.media.MediaMetadataRetriever.METADATA_KEY_IMAGE_PRIMARY
import android.media.MediaMetadataRetriever.METADATA_KEY_IMAGE_ROTATION
import android.media.MediaMetadataRetriever.METADATA_KEY_IMAGE_WIDTH
import android.media.MediaMetadataRetriever.METADATA_KEY_LOCATION
import android.media.MediaMetadataRetriever.METADATA_KEY_MIMETYPE
import android.media.MediaMetadataRetriever.METADATA_KEY_NUM_TRACKS
import android.media.MediaMetadataRetriever.METADATA_KEY_SAMPLERATE
import android.media.MediaMetadataRetriever.METADATA_KEY_TITLE
import android.media.MediaMetadataRetriever.METADATA_KEY_VIDEO_FRAME_COUNT
import android.media.MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT
import android.media.MediaMetadataRetriever.METADATA_KEY_VIDEO_ROTATION
import android.media.MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH
import android.media.MediaMetadataRetriever.METADATA_KEY_WRITER
import android.media.MediaMetadataRetriever.METADATA_KEY_XMP_LENGTH
import android.media.MediaMetadataRetriever.METADATA_KEY_XMP_OFFSET
import android.media.MediaMetadataRetriever.METADATA_KEY_YEAR
import android.net.Uri
import android.os.Bundle
import android.os.ParcelFileDescriptor
import android.system.Os
import android.system.OsConstants
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.TextView
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.contract.ActivityResultContract
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.hu.demo.base.ui.BaseActivity
import com.hu.demo.main.R
import com.hu.demo.main.adapter.BaseData
import com.hu.demo.main.adapter.BaseVH
import com.hu.demo.main.adapter.DefaultAdapter
import com.hu.demo.main.works.main.TYPE_DEFAULT
import com.hu.demo.utils.contentHexString
import com.oplus.tblplayer.retriever.IMediaMetadataRetriever.METADATA_KEY_IS_DRM
import com.oplus.tblplayer.retriever.IMediaMetadataRetriever.METADATA_KEY_TIMED_TEXT_LANGUAGES
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext


class VideoInfoActivity : BaseActivity(), View.OnClickListener {
    private var contentUri: Uri? = null
    private var rvList: RecyclerView? = null
    private var btnSelectImg: Button? = null
    private var infoAdapter: InfoAdapter? = null
    private val pickTypeResultContract = PickTypeResultContract()
    private val pickImageLauncher = registerForActivityResult(
        pickTypeResultContract,
        PickOnlyCallback { changeData(it) }
    )

    override fun getLayoutId(): Int {
        return R.layout.activity_video_info
    }

    override fun initToolbar() {
        super.initToolbar()
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowTitleEnabled(true)
            setDisplayShowHomeEnabled(false)
        }
    }

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        rvList = findViewById(R.id.rv_list)
        btnSelectImg = findViewById(R.id.btnSelectImg)
    }

    override fun initEvent(savedInstanceState: Bundle?) {
        super.initEvent(savedInstanceState)
        btnSelectImg?.setOnClickListener(this)
        rvList?.apply {
            addItemDecoration(DividerItemDecoration(this@VideoInfoActivity, RecyclerView.VERTICAL))
            adapter = InfoAdapter(this).apply {
                resetData(emptyList())
                infoAdapter = this
            }
            layoutManager = LinearLayoutManager(this@VideoInfoActivity, RecyclerView.VERTICAL, false)
        }
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.btnSelectImg -> {
                pickImageLauncher.launch(MIME_TYPE)
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun changeData(obj: Any?) {
        lifecycleScope.launch(Dispatchers.Default) {
            val datas = mutableListOf<InfoData>()
            contentResolver.openFileDescriptor(obj as Uri, "r")?.use {
                fillExtractor(it, datas)

                fillMetadata(it, datas)
                withContext(Dispatchers.Main) {
                    infoAdapter?.resetData(datas)
                    infoAdapter?.notifyDataSetChanged()
                }
            }
        }
    }

    private fun fillExtractor(pfd: ParcelFileDescriptor, datas: MutableList<InfoData>) {
        val extractor = MediaExtractor()
        extractor.setDataSource(pfd.fileDescriptor)
        Os.lseek(pfd.fileDescriptor, 0, OsConstants.SEEK_SET)
        for (index in 0..<extractor.trackCount) {
            val format = extractor.getTrackFormat(index)
            format.keys.forEach {
                val value = when (format.getValueTypeForKey(it)) {
                    TYPE_INTEGER -> format.getInteger(it).toString()
                    TYPE_LONG -> format.getLong(it).toString()
                    TYPE_FLOAT -> format.getFloat(it).toString()
                    TYPE_STRING -> format.getString(it)
                    TYPE_BYTE_BUFFER -> format.getByteBuffer(it)?.array()?.contentHexString()
                    else -> null
                }
                datas.add(InfoData("$index-$it", value))
            }
        }
        extractor.release()
    }

    private fun fillMetadata(pfd: ParcelFileDescriptor, datas: MutableList<InfoData>) {
        val retriever = MediaMetadataRetriever()
        Os.lseek(pfd.fileDescriptor, 0, OsConstants.SEEK_SET)
        retriever.setDataSource(pfd.fileDescriptor)

        datas.add(InfoData("METADATA_KEY_CD_TRACK_NUMBER", retriever.extractMetadata(METADATA_KEY_CD_TRACK_NUMBER)))
        datas.add(InfoData("METADATA_KEY_ALBUM", retriever.extractMetadata(METADATA_KEY_ALBUM)))
        datas.add(InfoData("METADATA_KEY_ARTIST", retriever.extractMetadata(METADATA_KEY_ARTIST)))
        datas.add(InfoData("METADATA_KEY_AUTHOR", retriever.extractMetadata(METADATA_KEY_AUTHOR)))
        datas.add(InfoData("METADATA_KEY_COMPOSER", retriever.extractMetadata(METADATA_KEY_COMPOSER)))
        datas.add(InfoData("METADATA_KEY_DATE", retriever.extractMetadata(METADATA_KEY_DATE)))
        datas.add(InfoData("METADATA_KEY_GENRE", retriever.extractMetadata(METADATA_KEY_GENRE)))
        datas.add(InfoData("METADATA_KEY_TITLE", retriever.extractMetadata(METADATA_KEY_TITLE)))
        datas.add(InfoData("METADATA_KEY_YEAR", retriever.extractMetadata(METADATA_KEY_YEAR)))
        datas.add(InfoData("METADATA_KEY_DURATION", retriever.extractMetadata(METADATA_KEY_DURATION)))
        datas.add(InfoData("METADATA_KEY_NUM_TRACKS", retriever.extractMetadata(METADATA_KEY_NUM_TRACKS)))
        datas.add(InfoData("METADATA_KEY_WRITER", retriever.extractMetadata(METADATA_KEY_WRITER)))
        datas.add(InfoData("METADATA_KEY_MIMETYPE", retriever.extractMetadata(METADATA_KEY_MIMETYPE)))
        datas.add(InfoData("METADATA_KEY_ALBUMARTIST", retriever.extractMetadata(METADATA_KEY_ALBUMARTIST)))
        datas.add(InfoData("METADATA_KEY_DISC_NUMBER", retriever.extractMetadata(METADATA_KEY_DISC_NUMBER)))
        datas.add(InfoData("METADATA_KEY_COMPILATION", retriever.extractMetadata(METADATA_KEY_COMPILATION)))
        datas.add(InfoData("METADATA_KEY_HAS_AUDIO", retriever.extractMetadata(METADATA_KEY_HAS_AUDIO)))
        datas.add(InfoData("METADATA_KEY_HAS_VIDEO", retriever.extractMetadata(METADATA_KEY_HAS_VIDEO)))
        datas.add(InfoData("METADATA_KEY_VIDEO_WIDTH", retriever.extractMetadata(METADATA_KEY_VIDEO_WIDTH)))
        datas.add(InfoData("METADATA_KEY_VIDEO_HEIGHT", retriever.extractMetadata(METADATA_KEY_VIDEO_HEIGHT)))
        datas.add(InfoData("METADATA_KEY_BITRATE", retriever.extractMetadata(METADATA_KEY_BITRATE)))
        datas.add(InfoData("METADATA_KEY_TIMED_TEXT_LANGUAGES", retriever.extractMetadata(METADATA_KEY_TIMED_TEXT_LANGUAGES)))
        datas.add(InfoData("METADATA_KEY_IS_DRM", retriever.extractMetadata(METADATA_KEY_IS_DRM)))
        datas.add(InfoData("METADATA_KEY_LOCATION", retriever.extractMetadata(METADATA_KEY_LOCATION)))
        datas.add(InfoData("METADATA_KEY_VIDEO_ROTATION", retriever.extractMetadata(METADATA_KEY_VIDEO_ROTATION)))
        datas.add(InfoData("METADATA_KEY_CAPTURE_FRAMERATE", retriever.extractMetadata(METADATA_KEY_CAPTURE_FRAMERATE)))
        datas.add(InfoData("METADATA_KEY_HAS_IMAGE", retriever.extractMetadata(METADATA_KEY_HAS_IMAGE)))
        datas.add(InfoData("METADATA_KEY_IMAGE_COUNT", retriever.extractMetadata(METADATA_KEY_IMAGE_COUNT)))
        datas.add(InfoData("METADATA_KEY_IMAGE_PRIMARY", retriever.extractMetadata(METADATA_KEY_IMAGE_PRIMARY)))
        datas.add(InfoData("METADATA_KEY_IMAGE_WIDTH", retriever.extractMetadata(METADATA_KEY_IMAGE_WIDTH)))
        datas.add(InfoData("METADATA_KEY_IMAGE_HEIGHT", retriever.extractMetadata(METADATA_KEY_IMAGE_HEIGHT)))
        datas.add(InfoData("METADATA_KEY_IMAGE_ROTATION", retriever.extractMetadata(METADATA_KEY_IMAGE_ROTATION)))
        datas.add(InfoData("METADATA_KEY_VIDEO_FRAME_COUNT", retriever.extractMetadata(METADATA_KEY_VIDEO_FRAME_COUNT)))
        datas.add(InfoData("METADATA_KEY_EXIF_OFFSET", retriever.extractMetadata(METADATA_KEY_EXIF_OFFSET)))
        datas.add(InfoData("METADATA_KEY_EXIF_LENGTH", retriever.extractMetadata(METADATA_KEY_EXIF_LENGTH)))
        datas.add(InfoData("METADATA_KEY_COLOR_STANDARD", retriever.extractMetadata(METADATA_KEY_COLOR_STANDARD)))
        datas.add(InfoData("METADATA_KEY_COLOR_TRANSFER", retriever.extractMetadata(METADATA_KEY_COLOR_TRANSFER)))
        datas.add(InfoData("METADATA_KEY_COLOR_RANGE", retriever.extractMetadata(METADATA_KEY_COLOR_RANGE)))
        datas.add(InfoData("METADATA_KEY_SAMPLERATE", retriever.extractMetadata(METADATA_KEY_SAMPLERATE)))
        datas.add(InfoData("METADATA_KEY_BITS_PER_SAMPLE", retriever.extractMetadata(METADATA_KEY_BITS_PER_SAMPLE)))
        datas.add(InfoData("METADATA_KEY_VIDEO_CODEC_MIME_TYPE", retriever.extractMetadata(40)))
        datas.add(InfoData("METADATA_KEY_XMP_OFFSET", retriever.extractMetadata(METADATA_KEY_XMP_OFFSET)))
        datas.add(InfoData("METADATA_KEY_XMP_LENGTH", retriever.extractMetadata(METADATA_KEY_XMP_LENGTH)))

        retriever.close()
    }

    private fun fillInfo(pfd: ParcelFileDescriptor, datas: MutableList<InfoData>) {

    }

    private open class PickOnlyCallback(private val resultFunc: (Uri) -> Unit) : ActivityResultCallback<Uri?> {
        override fun onActivityResult(result: Uri?) {
            result ?: run {
                Log.d(TAG, "PickOnlyCallback.onActivityResult: ")
                return
            }
            Log.d(TAG, "onActivityResult: $result")
            resultFunc(result)
        }
    }

    private class PickTypeResultContract : ActivityResultContract<String, Uri?>() {
        override fun createIntent(context: Context, input: String): Intent {
            return Intent(Intent.ACTION_PICK).setType(input)
        }

        override fun parseResult(resultCode: Int, intent: Intent?): Uri? {
            return if (resultCode != RESULT_OK) null else intent?.data
        }
    }

    class InfoVH(parent: ViewGroup) : BaseVH<InfoData>(parent, R.layout.item_video_info) {
        private val tvKey: TextView = findViewById(R.id.tv_item_key)
        private val tvValue: TextView = findViewById(R.id.tv_item_value)

        override fun bind(extraData: Map<String, Any>, data: InfoData, position: Int) {
            tvKey.text = data.key
            tvValue.text = data.value
        }
    }

    data class InfoData(val key: String?, val value: String?) : BaseData(TYPE_DEFAULT)


    class InfoAdapter(recyclerView: RecyclerView) : DefaultAdapter<BaseData, BaseVH<BaseData>>(recyclerView) {
        @Suppress("UNCHECKED_CAST")
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH<BaseData> {
            return InfoVH(parent) as BaseVH<BaseData>
        }

        override fun onBindViewHolder(holder: BaseVH<BaseData>, position: Int) {
            holder.bind(emptyMap(), dataList[position], position)
        }
    }

    companion object {
        private const val TAG = "VideoInfoActivity"
        private const val MIME_TYPE = "video/*"

        private val HLG_GAMMA = intArrayOf(
            0, 35, 62, 89, 114, 138, 162, 184, 206, 225, 245, 265, 283, 300, 318, 334,
            351, 367, 382, 397, 411, 426, 439, 452, 465, 477, 488, 498, 509, 519, 528, 538,
            546, 555, 563, 571, 579, 587, 594, 601, 607, 614, 621, 627, 633, 639, 645, 650,
            656, 661, 666, 671, 676, 681, 686, 691, 695, 700, 704, 708, 713, 717, 721, 725,
            728, 732, 736, 740, 743, 747, 750, 754, 757, 760, 763, 767, 770, 773, 776, 779,
            782, 785, 787, 790, 793, 796, 798, 801, 804, 806, 809, 811, 814, 816, 819, 821,
            823, 826, 828, 830, 832, 834, 837, 839, 841, 843, 845, 847, 849, 851, 853, 855,
            857, 859, 861, 863, 864, 866, 868, 870, 872, 873, 875, 877, 878, 880, 882, 883,
            885, 887, 888, 890, 891, 893, 895, 896, 898, 899, 901, 902, 904, 905, 906, 908,
            909, 911, 912, 913, 915, 916, 918, 919, 920, 922, 923, 924, 925, 927, 928, 929,
            930, 932, 933, 934, 935, 937, 938, 939, 940, 941, 942, 944, 945, 946, 947, 948,
            949, 950, 951, 953, 954, 955, 956, 957, 958, 959, 960, 961, 962, 963, 964, 965,
            966, 967, 968, 969, 970, 971, 972, 973, 974, 975, 976, 977, 978, 979, 979, 980,
            981, 982, 983, 984, 985, 986, 987, 987, 988, 989, 990, 991, 992, 992, 993, 994,
            995, 996, 997, 997, 998, 999, 1000, 1000, 1001, 1002, 1003, 1004, 1004, 1005, 1006,
            1007, 1007, 1008, 1009, 1010, 1011, 1012, 1012, 1013, 1014, 1015, 1016, 1018, 1019,
            1020, 1021, 1023,
        )

        private val SDR_GAMMA = intArrayOf(
            0, 24, 48, 72, 95, 117, 138, 158, 177, 193, 208, 222, 234, 246, 257, 267,
            277, 286, 295, 304, 313, 322, 331, 339, 347, 355, 363, 371, 379, 386, 393, 400,
            408, 414, 421, 428, 434, 441, 447, 453, 459, 465, 471, 477, 482, 488, 493, 499,
            504, 509, 514, 520, 524, 529, 534, 539, 544, 548, 553, 557, 562, 566, 571, 575,
            580, 584, 588, 592, 596, 601, 605, 609, 613, 617, 621, 625, 629, 632, 636, 640,
            644, 648, 651, 655, 659, 662, 666, 669, 673, 676, 680, 683, 687, 690, 693, 697,
            700, 703, 707, 710, 713, 716, 719, 722, 725, 728, 732, 735, 738, 740, 743, 746,
            749, 752, 755, 758, 761, 763, 766, 769, 772, 774, 777, 780, 782, 785, 787, 790,
            793, 795, 798, 800, 803, 805, 807, 810, 812, 815, 817, 819, 822, 824, 826, 829,
            831, 833, 835, 838, 840, 842, 844, 846, 849, 851, 853, 855, 857, 859, 861, 863,
            865, 867, 869, 871, 873, 875, 877, 879, 881, 883, 885, 887, 889, 891, 892, 894,
            896, 898, 900, 902, 904, 905, 907, 909, 911, 912, 914, 916, 918, 919, 921, 923,
            925, 926, 928, 930, 931, 933, 935, 936, 938, 940, 941, 943, 944, 946, 948, 949,
            951, 952, 954, 956, 957, 959, 960, 962, 963, 965, 966, 968, 970, 971, 973, 974,
            976, 977, 979, 980, 982, 983, 985, 986, 988, 989, 991, 992, 994, 995, 997, 998,
            999, 1001, 1002, 1004, 1005, 1007, 1008, 1010, 1011, 1013, 1014, 1016, 1017, 1018,
            1019, 1020,
        )
    }

}