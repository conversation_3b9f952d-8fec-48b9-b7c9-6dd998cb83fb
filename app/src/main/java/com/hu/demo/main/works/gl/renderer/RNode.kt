/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - RNode.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/09/12
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery		2024/09/12		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.renderer

import com.hu.demo.main.works.gl.renderer.param.NodePath
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.toNodePath
import java.util.LinkedList
import kotlin.properties.Delegates

/**
 * 提供节点内容名称的接口
 */
interface IName {
    val name: String
}

class RNode<T : IName>(val element: T) {
    private val nodeName = element.name
    var nodePath = arrayOf(nodeName).toNodePath()
        private set
    private var parent: RNode<T>? by Delegates.observable(null) { _, _, new ->
        nodePath = new?.nodePath?.plus(nodeName) ?: arrayOf(nodeName).toNodePath()
    }

    private val children: LinkedList<RNode<T>> = LinkedList()

    /**
     * 使用指定[nodePath]的渲染器路径，获取对应的[Renderer]渲染器。
     *
     * 如果[nodePath]的顶层节点[NodePath.top]可以在[children]中找到，则标记为发现，反之为未发现，直接返回`null`。
     * - 如果当前[nodePath]只有一个节点，则表示发现，返回结果。
     * - 如果当前[nodePath]不是一个节点，则需要通过[children]继续查找
     *
     * @param nodePath 要查找的渲染器路径，此路径的[NodePath.top]需是[children]中任意一个[Renderer]的[Renderer.name]
     *
     * @return 返回发现的节点[RNode]
     */
    fun get(nodePath: NodePath): RNode<T>? {
        val nodeName = nodePath.top()
        val find = children.find { it.nodeName == nodeName }
        if (find == null) {
            return null
        }
        val path = nodePath.minusTop()
        return if (path == null) {
            find
        } else {
            (find as? RNode)?.get(path)
        }
    }

    /**
     * 添加子渲染器，可指定渲染器的[index]位置
     *
     * @param rNode 需要添加的子节点
     * @param index 需要添加的位置，如果没有指定，模式是[children]的末尾
     */
    fun addChild(rNode: RNode<T>, index: Int? = null) {
        children.add(index ?: children.size, rNode)
    }

    /**
     * 从当前group中移除指定渲染器
     *
     * @param rNode 节点
     */
    fun removeChild(rNode: RNode<T>) {
        children.remove(rNode)
    }

    /**
     * 通过给定的[nodePath]，在[RNode]的树中寻找并将[rNode]添加到[nodePath]下
     *
     * @param nodePath 需要添加到[nodePath]下，做为子节点
     * @param rNode 需要添加的节点
     * @param index 节点在[nodePath]的children中第几个位置，如果指定null或不指定则表示添加的末尾
     */
    fun deepAddChild(nodePath: NodePath, rNode: RNode<T>, index: Int? = null) {
        get(nodePath)?.addChild(rNode, index)
    }

    /**
     * 删除指定节点
     *
     * @param nodePath 要删除的节点的路径
     */
    fun deepRemoveChild(nodePath: NodePath) {
        get(nodePath)?.apply { this.parent?.removeChild(this) }
    }

    fun removeChildren() {
        children.clear()
    }

    /**
     * 前序遍历
     *
     * @param func 回调[element]
     */
    fun preOrder(func: (T) -> Unit) {
        func(element)
        children.forEach {
            it.preOrder(func)
        }
    }

    override fun toString(): String {
        return "RNode($nodePath)"
    }
}