package com.hu.demo.main.works.gl.shader

/**
 * Shader程序的片段
 *
 * @param value 片段的值
 * @param grade 片段的优先级，默认为0，当存在其他程序与当前程序互斥时，需要对[grade]加大，值越大优先级越大
 * @param overrideHash 复写此片段的[hashCode]方法的值
 */
data class ShaderFrag(val value: String, val grade: Int = 0, private val overrideHash: Int? = null) {
    override fun toString(): String {
        return value
    }

    override fun equals(other: Any?): <PERSON><PERSON>an {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as ShaderFrag

        return value == other.value || hashCode() == other.hashCode()
    }

    override fun hashCode(): Int {
        return overrideHash ?: value.hashCode()
    }

    companion object {
        // ------------------------------------ shader的头 ------------------------------------------------
        val GLSL_SHADER_HEAD = ShaderFrag(
            """
                |#version 300 es
                |precision highp float;
                |precision highp int;
                |precision highp sampler2D;
                |precision highp sampler3D;
            """.trimMargin()
        )

        val GLSL_SHADER_OES_HEAD = ShaderFrag(
            """
                |#version 300 es
                |#extension GL_OES_EGL_image_external_essl3: require
                |precision highp float;
                |precision highp int;
                |precision highp sampler2D;
                |precision highp sampler3D;
                |precision highp samplerExternalOES;
            """.trimMargin(),
            1,
            GLSL_SHADER_HEAD.hashCode()
        )

        //--------------------------------------- 成员变量 -------------------------------------------------
        //---------------------------------------- 顶点成员 ------------------------------------------------
        val FIELD_LAYOUT_IN_VEC3_POSITION = ShaderFrag("layout (location = 0) in vec3 aPosition;")
        val FIELD_LAYOUT_IN_VEC3_TEX_COORD = ShaderFrag("layout (location = 1) in vec2 aTexCoord;")
        val FIELD_UNIFORM_MAT4_VIEW_M = ShaderFrag("uniform mat4 uViewM;")
        val FIELD_UNIFORM_MAT4_MODEL_M = ShaderFrag("uniform mat4 uModelM;")
        val FIELD_UNIFORM_MAT4_PROJECTION_M = ShaderFrag("uniform mat4 uProjectionM;")
        val FIELD_OUT_VEC2_TEX_COORD = ShaderFrag("out vec2 vTexCoord;")
        val FIELD_UNIFORM_FLOAT_MIX_RATIO = ShaderFrag("uniform float uMixRatio;")

        val FIELD_OUT_VEC2_OVERLAY_TEX_COORD = ShaderFrag("out vec2 vOverlayTexCoord;")
        val FIELD_UNIFORM_MAT4_OVERLAY_TEX_MAT = ShaderFrag("uniform mat4 uOverlayTexMat;")

        //---------------------------------------- 片段成员 ------------------------------------------------
        val FIELD_UNIFORM_SAMPLER2D_TEX = ShaderFrag("uniform sampler2D uTex;")
        val FIELD_UNIFORM_SAMPLER2D_TEX2 = ShaderFrag("uniform sampler2D uTex2;")
        val FIELD_UNIFORM_SAMPLEROES_TEX = ShaderFrag("uniform samplerExternalOES uTex;", 1, FIELD_UNIFORM_SAMPLER2D_TEX.hashCode())
        val FIELD_UNIFORM_SAMPLER2D_GAINMAP = ShaderFrag("uniform sampler2D uGainmap;")
        val FIELD_UNIFORM_VEC3_LOG_RATIO_MIN = ShaderFrag("uniform vec3 uLogRatioMin;")
        val FIELD_UNIFORM_VEC3_LOG_RATIO_MAX = ShaderFrag("uniform vec3 uLogRatioMax;")
        val FIELD_UNIFORM_VEC3_GAINMAP_GAMMA = ShaderFrag("uniform vec3 uGainmapGamma;")
        val FIELD_UNIFORM_VEC3_EPSILON_SDR = ShaderFrag("uniform vec3 uEpsilonSdr;")
        val FIELD_UNIFORM_VEC3_EPSILON_HDR = ShaderFrag("uniform vec3 uEpsilonHdr;")
        val FIELD_UNIFORM_FLOAT_LOG_DEVICE_HDRSDR_RATIO = ShaderFrag("uniform float uLogDeviceHdrSdrRatio;")
        val FIELD_UNIFORM_FLOAT_LOG_DISPLAY_SDR_RATIO = ShaderFrag("uniform float uLogDisplaySdrRatio;")
        val FIELD_UNIFORM_FLOAT_LOG_DISPLAY_HDR_RATIO = ShaderFrag("uniform float uLogDisplayHdrRatio;")
        val FIELD_UNIFORM_INT_SINGLE_CHANNEL = ShaderFrag("uniform int uSingleChannel;")
        val FIELD_UNIFORM_INT_USE_COMBINE_UHDR = ShaderFrag("uniform int uUseCombineUhdr;")
        val FIELD_UNIFORM_INT_UHDR_SRC_MODE = ShaderFrag("uniform int uUhdrSrcMode;")
        val FIELD_IN_VEC2_TEX_COORD = ShaderFrag("in vec2 vTexCoord;")
        val FIELD_OUT_VEC4_FRAGCOLOR = ShaderFrag("out vec4 FragColor;")

        val FIELD_IN_VEC2_OVERLAY_TEX_COORD = ShaderFrag("in vec2 vOverlayTexCoord;")
        val FIELD_UNIFORM_SAMPLER2D_OVERLAY_TEX = ShaderFrag("uniform sampler2D uOverlayTex;")
        val FIELD_UNIFORM_FLOAT_OVERLAY_ALPHA = ShaderFrag("uniform float uOverlayAlpha;")

        val FIELD_UNIFORM_MAT3_GAMUT_TRANSFORM = ShaderFrag("uniform mat3 uGamutTransform;")
        val FIELD_UNIFORM_INT_SRC_MODE = ShaderFrag("uniform int uSrcMode;")
        val FIELD_UNIFORM_INT_DEST_MODE = ShaderFrag("uniform int uDestMode;")
        val FIELD_UNIFORM_FLOAT_CLAMP = ShaderFrag("uniform float uClamp;")
        val FIELD_UNIFORM_FLOAT_DEST_CLAMP = ShaderFrag("uniform float uDestClamp;")

        val FIELD_UNIFORM_MAT4_POINT = ShaderFrag("uniform mat4 uPoint;")

        val FIELD_UNIFORM_SAMPLER2D_VIG_TABLE_TEX = ShaderFrag("uniform sampler2D uVigTableTex;")
        val FIELD_UNIFORM_SAMPLER2D_DITHER_TABLE_TEX = ShaderFrag("uniform sampler2D uDitherTableTex;")
        val FIELD_UNIFORM_FLOAT_FSTR = ShaderFrag("uniform float uVigRatio;")
        val FIELD_UNIFORM_FLOAT_DVIG = ShaderFrag("uniform float uDitherRatio;")
        val FIELD_UNIFORM_SAMPLER3D_LUT_3D_TEX = ShaderFrag("uniform sampler3D uLut3dTex;")
        val FIELD_UNIFORM_FLOAT_LUT_SIZE = ShaderFrag("uniform float uLutSize;")

        //---------------------------------------- 结构体 ------------------------------------------------
        val STRUCT_TF_PARAMS = ShaderFrag("struct TfParams {\n    float a, b, c, d, e, f, g;\n};")
        val STRUCT_TF_HLG_PARAMS = ShaderFrag("struct HlgTfParams {\n    float a, b, c;\n};")
        val STRUCT_TF_PQ_PARAMS = ShaderFrag("struct PqTfParams {\n    float M1, M2, C1, C2, C3;\n};")

        //---------------------------------------- 常量 ------------------------------------------------
        val CONST_TF_PARAMS_SRGB =
            ShaderFrag("const TfParams srgb = TfParams(1.0f / 1.055f, 0.055f / 1.055f, 1.0f / 12.92f, 0.04045f, 0.0f, 0.0f, 2.4f);")
        val CONST_TF_PARAMS_GAMMA_2_2 = ShaderFrag("const TfParams gamma_2_2 = TfParams(1.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 2.2f);")
        val CONST_TF_PARAMS_BT709 =
            ShaderFrag("const TfParams bt709 = TfParams(1.0f / 1.099f, 0.099f / 1.099f, 1.0f / 4.5f, 0.081f, 0.0f, 0.0f, 1.0f / 0.45f);")
        val CONST_TF_PARAMS_HLG = ShaderFrag("const HlgTfParams hlg = HlgTfParams(0.17883277f, 0.28466892f, 0.55991073f);")
        val CONST_TF_PARAMS_PQ = ShaderFrag(
            "const PqTfParams pq = PqTfParams((2610.0f / 4096.0f) / 4.0f, (2523.0f / 4096.0f) * 128.0f, (3424.0f / 4096.0f), (2413.0f / 4096.0f) * 32.0f, (2392.0f / 4096.0f) * 32.0f);"
        )
        val CONST_BT2020_RGB2YUV = ShaderFrag(
            """
                |mat3 bt2020Rgb2yuv = mat3(
                |    0.2627,  -0.1396,  0.5000,
                |    0.6780,  -0.3604, -0.4598,
                |    0.0593,   0.5000, -0.0402
                |);
            """.trimMargin()
        )
        val CONST_BT2020_YUV2RGB = ShaderFrag(
            """
                |mat3 bt2020Yuv2rgb = mat3(
                |    1.0000f,   1.0000f, 1.0000f,
                |    0.0000f,  -0.1646f, 1.8814f,
                |    1.4746f,  -0.5714f, 0.0000f
                |);
            """.trimMargin()
        )
        val CONST_BT601_RGB2YUV = ShaderFrag(
            """
                |mat3 bt601Rgb2yuv = mat3(
                |    0.299,  -0.147,  0.615,
                |    0.587,  -0.289, -0.515,
                |    0.114,   0.436, -0.100
                |);
            """.trimMargin()
        )
        val CONST_BT601_YUV2RGB = ShaderFrag(
            """
                |mat3 bt601Yuv2rgb = mat3(
                |    1.000,   1.000,   1.000,
                |    0.000,  -0.394,   2.032,
                |    1.140,  -0.581,   0.000
                |);
            """.trimMargin()
        )
        val CONST_BT709_RGB2YUV = ShaderFrag(
            """
                |mat3 bt709Rgb2yuv = mat3(
                |    0.2126, -0.1146,  0.5000,
                |    0.7152, -0.3854, -0.4542,
                |    0.0722,  0.5000, -0.0458
                |);
            """.trimMargin()
        )
        val CONST_BT709_YUV2RGB = ShaderFrag(
            """
                |mat3 bt709Yuv2rgb = mat3(
                |    1.000,  1.000,  1.000,
                |    0.000, -0.1873, 1.8556,
                |    1.5748,-0.4681, 0.000
                |);
            """.trimMargin()
        )

        //----------------------------------------- 方法 ------------------------------------------------
        val METHOD_SRGB_EOTF = ShaderFrag(
            """
                |vec3 srgbEotf(vec3 color) {
                |    vec3 s = sign(color);
                |    color = abs(color);
                |    return s * mix(srgb.c * color + srgb.f, pow(srgb.a * color + srgb.b, vec3(srgb.g)) + srgb.e, step(srgb.d, color));
                |}
            """.trimMargin()
        )
        val METHOD_SRGB_OETF = ShaderFrag(
            """
                |vec3 srgbOetf(vec3 color) {
                |    vec3 s = sign(color);
                |    color = abs(color);
                |    return s * mix((color - srgb.f) / srgb.c, (pow(color - srgb.e, vec3(1.0f / srgb.g)) - srgb.b) / srgb.a, step(srgb.d * srgb.c, color));
                |}
            """.trimMargin()
        )
        val METHOD_BT709_EOTF = ShaderFrag(
            """
                |vec3 bt709Eotf(vec3 color) {
                |    vec3 s = sign(color);
                |    color = abs(color);
                |    return s * mix(bt709.c * color + bt709.f, pow(bt709.a * color + bt709.b, vec3(bt709.g)) + bt709.e, step(bt709.d, color));
                |}
            """.trimMargin()
        )
        val METHOD_BT709_OETF = ShaderFrag(
            """
                |vec3 bt709Oetf(vec3 color) {
                |    vec3 s = sign(color);
                |    color = abs(color);
                |    return s * mix((color - bt709.f) / bt709.c, (pow(color - bt709.e, vec3(1.0f / bt709.g)) - bt709.b) / bt709.a, step(bt709.d * bt709.c, color));
                |}
            """.trimMargin()
        )
        val METHOD_GAMMA_2_2_EOTF = ShaderFrag(
            """
                |vec3 gamma2_2Eotf(vec3 color) {
                |    vec3 s = sign(color);
                |    color = abs(color);
                |    return s * pow(color, vec3(gamma_2_2.g));
                |}
            """.trimMargin()
        )
        val METHOD_GAMMA_2_2_OETF = ShaderFrag(
            """
                |vec3 gamma2_2Oetf(vec3 color) {
                |    vec3 s = sign(color);
                |    color = abs(color);
                |    return s * pow(color, vec3(1.0f / gamma_2_2.g));
                |}
            """.trimMargin()
        )
        val METHOD_HLG_EOTF = ShaderFrag(
            """
                |vec3 hlgOotf(vec3 color) {
                |    vec3 lumaPoint = vec3(0.2627f, 0.6780f, 0.0593f);
                |    float luma = dot(lumaPoint, color.rgb);
                |    color.rgb = color.rgb * pow(luma, 0.2f);
                |
                |    return color;
                |}
                |
                |vec3 hlgEotf(vec3 color) {
                |    return mix(pow(color, vec3(2.0f)) / 3.0f, (exp((color - hlg.c) / hlg.a) + hlg.b) / 12.0f, step(1.0f / 2.0f, color));
                |}
            """.trimMargin()
        )
        val METHOD_HLG_OETF = ShaderFrag(
            """
                |vec3 hlgOotfInv(vec3 color) {
                |    vec3 lumaPoint = vec3(0.2627f, 0.6780f, 0.0593f);
                |    float luma = dot(lumaPoint, color.rgb);
                |    color.rgb = color.rgb * pow(luma, -0.2f / 1.2f);
                |
                |    return color;
                |}
                |
                |vec3 hlgOetf(vec3 color) {
                |    return mix(sqrt(color * 3.0f), hlg.a * log(12.0 * color - hlg.b) + hlg.c, step(1.0f / 12.0f, color));
                |}
            """.trimMargin()
        )
        val METHOD_PQ_EOTF = ShaderFrag(
            """
                |vec3 pqEotf(vec3 color) {
                |    vec3 tmp = pow(color, vec3(1.0f / pq.M2));
                |    return pow(max(tmp - pq.C1, 0.0f) / (pq.C2 - pq.C3 * tmp), vec3(1.0f / pq.M1));
                |}
            """.trimMargin()
        )
        val METHOD_PQ_OETF = ShaderFrag(
            """
                |vec3 pqOetf(vec3 color) {
                |    vec3 tmp = pow(color, vec3(pq.M1));
                |    return pow((pq.C1 + pq.C2 * tmp) / (1.0f + pq.C3 * tmp), vec3(pq.M2));
                |}
            """.trimMargin()
        )
        val METHOD_COMPOSE_UHDR = ShaderFrag(
            """
                |vec4 combineUhdr(vec4 color) {
                |    vec4 gainColor = texture(uGainmap, vTexCoord);
                |    if (uSingleChannel == 1) {
                |        gainColor = vec4(gainColor.rrr, 1.0f);
                |    }
                |
                |    vec3 L = mix(uLogRatioMin, uLogRatioMax, pow(gainColor.rgb, uGainmapGamma));
                |
                |    float targetRatio = uLogDeviceHdrSdrRatio - uLogDisplaySdrRatio;
                |    float maxRatio = uLogDisplayHdrRatio - uLogDisplaySdrRatio;
                |    float W = clamp(targetRatio / maxRatio, 0.0f, 1.0f);
                |    vec3 H = (color.rgb + uEpsilonSdr) * exp(L * W) - uEpsilonHdr;
                |
                |    // 归一化
                |    H *= 1.0f / exp(W * maxRatio);
                |
                |    return vec4(H, color.a);
                |}
            """.trimMargin()
        )

        val METHOD_MULTIPLY_VEC2 = ShaderFrag(
            """
                |float multiply(vec2 p0, vec2 p1, vec2 p2){
                |    return ((p1.x - p0.x) * (p2.y - p0.y) - (p2.x - p0.x) * (p1.y - p0.y));
                |}
            """.trimMargin()
        )

        val METHOD_IS_CONTAIN = ShaderFrag(
            """
                |bool isContain() {
                |    vec2 p0 = vec2(vTexCoord.x, vTexCoord.y);
                |    vec2 pLT = vec2(uPoint[0][0], uPoint[0][1]);
                |    vec2 pLB = vec2(uPoint[1][0], uPoint[1][1]);
                |    vec2 pRB = vec2(uPoint[2][0], uPoint[2][1]);
                |    vec2 pRT = vec2(uPoint[3][0], uPoint[3][1]);
                |    float bT = multiply(pLT, p0, pLB);
                |    float bL = multiply(pLB, p0, pRB);
                |    float bR = multiply(pRB, p0, pRT);
                |    float bB = multiply(pRT, p0, pLT);
                |    // 如果当前纹理坐标在四个点组成的向量的同一侧，则认为包含，否则不包含
                |    return ((bT < 0.0) && (bL < 0.0) && (bR < 0.0) && (bB < 0.0)) || ((bT > 0.0) && (bL > 0.0) && (bR > 0.0) && (bB > 0.0));
                |}
            """.trimMargin()
        )

        val METHOD_DITHER_TABLE = ShaderFrag(
            """
                |vec3 ditherTable(vec3 yuv, vec2 coord) {
                |    float gain = texture(uVigTableTex, coord).r;
                |    float dither = texture(uDitherTableTex, coord).r;
                |    float y = yuv.r;
                |    y = 1.0f - pow((1.0f - y), pow(gain, uVigRatio));
                |    y = clamp(y + (0.5f + uDitherRatio * dither) / 1023.0f, 0.0f, 1.0f);
                |    return vec3(y, yuv.g, yuv.b);
                |}
            """.trimMargin()
        )

        val METHOD_BT2020_YUV2RGB = ShaderFrag(
            """
                |vec3 bt2020Yuv2Rgb(vec3 yuv) {
                |    return bt2020Yuv2rgb * yuv;
                |}
            """.trimMargin()
        )

        val METHOD_BT2020_RGB2YUV = ShaderFrag(
            """
                |vec3 bt2020Rgb2Yuv(vec3 rgb) {
                |    return bt2020Rgb2yuv * rgb;
                |}
            """.trimMargin()
        )

        val METHOD_BT601_YUV2RGB = ShaderFrag(
            """
                |vec3 bt601Yuv2Rgb(vec3 yuv) {
                |    return bt601Yuv2rgb * yuv;
                |}
            """.trimMargin()
        )

        val METHOD_BT601_RGB2YUV = ShaderFrag(
            """
                |vec3 bt601Rgb2Yuv(vec3 rgb) {
                |    return bt601Rgb2yuv * rgb;
                |}
            """.trimMargin()
        )

        val METHOD_BT709_YUV2RGB = ShaderFrag(
            """
                |vec3 bt709Yuv2Rgb(vec3 yuv) {
                |    return bt709Yuv2rgb * yuv;
                |}
            """.trimMargin()
        )

        val METHOD_BT709_RGB2YUV = ShaderFrag(
            """
                |vec3 bt709Rgb2Yuv(vec3 rgb) {
                |    return bt709Rgb2yuv * rgb;
                |}
            """.trimMargin()
        )

        val METHOD_APPLY_LUT = ShaderFrag(
            """
                |vec3 applyLut(vec3 color, sampler3D lut, float lutSize) {
                |    vec3 scale = vec3((lutSize - 1.0f) / lutSize);
                |    vec3 offset = vec3(1.0f / (2.0f * lutSize));
                |    return texture(lut, scale * color.rgb + offset).rgb;
                |}
            """.trimMargin()
        )
        val METHOD_EOTF = ShaderFrag(
            """
                |vec3 eotf(vec3 color, int mode) {
                |    if (mode == 1) {
                |        return hlgOotf(hlgEotf(color.rgb));
                |    } else if (mode == 2) {
                |        return pqEotf(color.rgb);
                |    } else if (mode == 3) {
                |        return srgbEotf(color.rgb);
                |    } else if (mode == 4) {
                |        return bt709Eotf(color.rgb);
                |    } else if (mode == 5) {
                |        return gamma2_2Eotf(color.rgb);
                |    } else {
                |       return color;
                |    }
                |}
            """.trimMargin()
        )
        val METHOD_OETF = ShaderFrag(
            """
                |vec3 oetf(vec3 color, int mode) {
                |    if (mode == 1) {
                |        return hlgOetf(hlgOotfInv(color.rgb));
                |    } else if (mode == 2) {
                |        return pqOetf(color.rgb);
                |    } else if (mode == 3) {
                |        return srgbOetf(color.rgb);
                |    } else if (mode == 4) {
                |        return bt709Oetf(color.rgb);
                |    } else if (mode == 5) {
                |        return gamma2_2Oetf(color.rgb);
                |    } else {
                |        return color;
                |    }
                |}
            """.trimMargin()
        )

        //----------------------------------------- main方法 ----------------------------------------------
        val EXEC_MAIN_START = ShaderFrag("void main() {")
        val EXEC_MAIN_END = ShaderFrag("}")

        val EXEC_MIX_TEXTURE_COLOR = ShaderFrag("    vec4 color = mix(texture(uTex, vTexCoord), texture(uTex2, vTexCoord), uMixRatio);")
        val EXEC_TEXTURE_COLOR = ShaderFrag("    vec4 color = texture(uTex, vTexCoord);")
        val EXEC_TEXTURE_GAINCOLOR = ShaderFrag("    vec4 gainColor = texture(uGainmap, vTexCoord);")
        val EXEC_COMBINE_UHDR = ShaderFrag(
            """
                |    if(uUseCombineUhdr == 1) {
                |        color.rgb = eotf(color.rgb, uUhdrSrcMode);
                |        color = combineUhdr(color);
                |    }
            """.trimMargin()
        )
        val EXEC_SET_FRAGCOLOR = ShaderFrag("    FragColor = color;")

        val EXEC_SET_GL_POSITION = ShaderFrag("    gl_Position = uProjectionM * uViewM * uModelM * vec4(aPosition, 1.0f);")
        val EXEC_SET_TEXCOORD = ShaderFrag("    vTexCoord = aTexCoord;")
        val EXEC_COLOR_ALPHA_MIX = ShaderFrag("    color.a = color.a * uMixRatio;")

        val EXEC_SET_OVERLAY_TEX_COORD = ShaderFrag("    vOverlayTexCoord = (uOverlayTexMat * vec4(aTexCoord, 0.0f, 1.0f)).xy;")

        val EXEC_EOTF = ShaderFrag(
            """
                |    color.rgb = eotf(color.rgb, uSrcMode);
            """.trimMargin()
        )
        val EXEC_COLOR_TRANSFORM = ShaderFrag("    color.rgb = uGamutTransform * color.rgb;")
        val EXEC_CALC_CLAMP = ShaderFrag("    color.rgb *= (uDestClamp / uClamp);")
        val EXEC_OETF = ShaderFrag(
            """
                |    color.rgb = oetf(color.rgb, uDestMode);
            """.trimMargin()
        )
        val EXEC_CHECK_CONTAIN = ShaderFrag(
            """
                |    if (!isContain()) {
                |        color = vec4(0.0f);
                |    }
            """.trimMargin()
        )

        val EXEC_CALC_BT2020_RGB_2_YUV = ShaderFrag("    vec3 yuv = bt2020Rgb2Yuv(color.rgb);")
        val EXEC_CALC_BT601_RGB_2_YUV = ShaderFrag("    vec3 yuv = bt601Rgb2Yuv(color.rgb);")
        val EXEC_CALC_DITHER_TABLE = ShaderFrag("    yuv = ditherTable(yuv, vTexCoord);")
        val EXEC_CALC_BT2020_YUV_2_RGB = ShaderFrag("    color.rgb = bt2020Yuv2Rgb(yuv.rgb);")
        val EXEC_CALC_BT601_YUV_2_RGB = ShaderFrag("    color.rgb = bt601Yuv2Rgb(yuv.rgb);")
        val EXEC_APPLY_LUT = ShaderFrag("    color.rgb = applyLut(color.rgb, uLut3dTex, uLutSize);")
    }
}