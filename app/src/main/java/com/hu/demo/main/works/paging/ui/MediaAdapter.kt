package com.hu.demo.main.works.paging.ui

import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.hu.demo.main.works.paging.data.MediaData
import com.hu.demo.main.works.paging.vh.BaseViewHolder

abstract class MediaAdapter : PagingDataAdapter<MediaData, RecyclerView.ViewHolder>(DIFF_CALLBACK) {

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        getItem(position)?.also {
            (holder as? BaseViewHolder)?.bind(it)
        }
    }

    companion object {
        private val DIFF_CALLBACK = object : DiffUtil.ItemCallback<MediaData>() {
            override fun areItemsTheSame(oldItem: MediaData, newItem: MediaData): Boolean {
                return oldItem.id == newItem.id
            }

            override fun areContentsTheSame(oldItem: MediaData, newItem: MediaData): Boolean =
                oldItem == newItem
        }
    }
}