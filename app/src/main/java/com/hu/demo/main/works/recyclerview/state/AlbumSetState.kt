package com.hu.demo.main.works.recyclerview.state

import android.view.ViewGroup
import com.hu.demo.main.works.recyclerview.longclick.ItemLongClickAddition
import com.hu.demo.main.works.recyclerview.slidingselect.SlidingSelectProcessor
import com.hu.demo.main.works.recyclerview.view.ViewTouchAdapter

class AlbumSetState<V : ViewGroup>(touchAdapter: ViewTouchAdapter<V>) : State<V>(touchAdapter) {
    private val slidingSelectHelper = SlidingSelectProcessor<V>(null)
    private val longClickHelper = ItemLongClickAddition<V>()

    override fun toNormalModel() {
        longClickHelper.attachToView(touchAdapter)
        slidingSelectHelper.attachToView(touchAdapter)
    }

    override fun toEditMode() {
        slidingSelectHelper.attachToView(touchAdapter)
        longClickHelper.attachToView(touchAdapter)
    }
}