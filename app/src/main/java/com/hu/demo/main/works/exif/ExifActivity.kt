package com.hu.demo.main.works.exif

import android.annotation.SuppressLint
import android.app.Activity
import android.app.AlertDialog
import android.content.Context
import android.content.Intent
import android.media.MediaMetadataRetriever
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.EditText
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.contract.ActivityResultContract
import androidx.exifinterface.media.ExifInterface
import androidx.exifinterface.media.ExifInterface.STREAM_TYPE_EXIF_DATA_ONLY
import androidx.exifinterface.media.ExifInterface.TAG_APERTURE_VALUE
import androidx.exifinterface.media.ExifInterface.TAG_ARTIST
import androidx.exifinterface.media.ExifInterface.TAG_BITS_PER_SAMPLE
import androidx.exifinterface.media.ExifInterface.TAG_BODY_SERIAL_NUMBER
import androidx.exifinterface.media.ExifInterface.TAG_BRIGHTNESS_VALUE
import androidx.exifinterface.media.ExifInterface.TAG_CAMERA_OWNER_NAME
import androidx.exifinterface.media.ExifInterface.TAG_CFA_PATTERN
import androidx.exifinterface.media.ExifInterface.TAG_COLOR_SPACE
import androidx.exifinterface.media.ExifInterface.TAG_COMPONENTS_CONFIGURATION
import androidx.exifinterface.media.ExifInterface.TAG_COMPRESSED_BITS_PER_PIXEL
import androidx.exifinterface.media.ExifInterface.TAG_COMPRESSION
import androidx.exifinterface.media.ExifInterface.TAG_CONTRAST
import androidx.exifinterface.media.ExifInterface.TAG_COPYRIGHT
import androidx.exifinterface.media.ExifInterface.TAG_CUSTOM_RENDERED
import androidx.exifinterface.media.ExifInterface.TAG_DATETIME
import androidx.exifinterface.media.ExifInterface.TAG_DATETIME_DIGITIZED
import androidx.exifinterface.media.ExifInterface.TAG_DATETIME_ORIGINAL
import androidx.exifinterface.media.ExifInterface.TAG_DEFAULT_CROP_SIZE
import androidx.exifinterface.media.ExifInterface.TAG_DEVICE_SETTING_DESCRIPTION
import androidx.exifinterface.media.ExifInterface.TAG_DIGITAL_ZOOM_RATIO
import androidx.exifinterface.media.ExifInterface.TAG_DNG_VERSION
import androidx.exifinterface.media.ExifInterface.TAG_EXIF_VERSION
import androidx.exifinterface.media.ExifInterface.TAG_EXPOSURE_BIAS_VALUE
import androidx.exifinterface.media.ExifInterface.TAG_EXPOSURE_INDEX
import androidx.exifinterface.media.ExifInterface.TAG_EXPOSURE_MODE
import androidx.exifinterface.media.ExifInterface.TAG_EXPOSURE_PROGRAM
import androidx.exifinterface.media.ExifInterface.TAG_EXPOSURE_TIME
import androidx.exifinterface.media.ExifInterface.TAG_FILE_SOURCE
import androidx.exifinterface.media.ExifInterface.TAG_FLASH
import androidx.exifinterface.media.ExifInterface.TAG_FLASHPIX_VERSION
import androidx.exifinterface.media.ExifInterface.TAG_FLASH_ENERGY
import androidx.exifinterface.media.ExifInterface.TAG_FOCAL_LENGTH
import androidx.exifinterface.media.ExifInterface.TAG_FOCAL_LENGTH_IN_35MM_FILM
import androidx.exifinterface.media.ExifInterface.TAG_FOCAL_PLANE_RESOLUTION_UNIT
import androidx.exifinterface.media.ExifInterface.TAG_FOCAL_PLANE_X_RESOLUTION
import androidx.exifinterface.media.ExifInterface.TAG_FOCAL_PLANE_Y_RESOLUTION
import androidx.exifinterface.media.ExifInterface.TAG_F_NUMBER
import androidx.exifinterface.media.ExifInterface.TAG_GAIN_CONTROL
import androidx.exifinterface.media.ExifInterface.TAG_GAMMA
import androidx.exifinterface.media.ExifInterface.TAG_GPS_ALTITUDE
import androidx.exifinterface.media.ExifInterface.TAG_GPS_ALTITUDE_REF
import androidx.exifinterface.media.ExifInterface.TAG_GPS_AREA_INFORMATION
import androidx.exifinterface.media.ExifInterface.TAG_GPS_DATESTAMP
import androidx.exifinterface.media.ExifInterface.TAG_GPS_DEST_BEARING
import androidx.exifinterface.media.ExifInterface.TAG_GPS_DEST_BEARING_REF
import androidx.exifinterface.media.ExifInterface.TAG_GPS_DEST_DISTANCE
import androidx.exifinterface.media.ExifInterface.TAG_GPS_DEST_DISTANCE_REF
import androidx.exifinterface.media.ExifInterface.TAG_GPS_DEST_LATITUDE
import androidx.exifinterface.media.ExifInterface.TAG_GPS_DEST_LATITUDE_REF
import androidx.exifinterface.media.ExifInterface.TAG_GPS_DEST_LONGITUDE
import androidx.exifinterface.media.ExifInterface.TAG_GPS_DEST_LONGITUDE_REF
import androidx.exifinterface.media.ExifInterface.TAG_GPS_DIFFERENTIAL
import androidx.exifinterface.media.ExifInterface.TAG_GPS_DOP
import androidx.exifinterface.media.ExifInterface.TAG_GPS_H_POSITIONING_ERROR
import androidx.exifinterface.media.ExifInterface.TAG_GPS_IMG_DIRECTION
import androidx.exifinterface.media.ExifInterface.TAG_GPS_IMG_DIRECTION_REF
import androidx.exifinterface.media.ExifInterface.TAG_GPS_LATITUDE
import androidx.exifinterface.media.ExifInterface.TAG_GPS_LATITUDE_REF
import androidx.exifinterface.media.ExifInterface.TAG_GPS_LONGITUDE
import androidx.exifinterface.media.ExifInterface.TAG_GPS_LONGITUDE_REF
import androidx.exifinterface.media.ExifInterface.TAG_GPS_MAP_DATUM
import androidx.exifinterface.media.ExifInterface.TAG_GPS_MEASURE_MODE
import androidx.exifinterface.media.ExifInterface.TAG_GPS_PROCESSING_METHOD
import androidx.exifinterface.media.ExifInterface.TAG_GPS_SATELLITES
import androidx.exifinterface.media.ExifInterface.TAG_GPS_SPEED
import androidx.exifinterface.media.ExifInterface.TAG_GPS_SPEED_REF
import androidx.exifinterface.media.ExifInterface.TAG_GPS_STATUS
import androidx.exifinterface.media.ExifInterface.TAG_GPS_TIMESTAMP
import androidx.exifinterface.media.ExifInterface.TAG_GPS_TRACK
import androidx.exifinterface.media.ExifInterface.TAG_GPS_TRACK_REF
import androidx.exifinterface.media.ExifInterface.TAG_GPS_VERSION_ID
import androidx.exifinterface.media.ExifInterface.TAG_IMAGE_DESCRIPTION
import androidx.exifinterface.media.ExifInterface.TAG_IMAGE_LENGTH
import androidx.exifinterface.media.ExifInterface.TAG_IMAGE_UNIQUE_ID
import androidx.exifinterface.media.ExifInterface.TAG_IMAGE_WIDTH
import androidx.exifinterface.media.ExifInterface.TAG_INTEROPERABILITY_INDEX
import androidx.exifinterface.media.ExifInterface.TAG_ISO_SPEED
import androidx.exifinterface.media.ExifInterface.TAG_ISO_SPEED_LATITUDE_YYY
import androidx.exifinterface.media.ExifInterface.TAG_ISO_SPEED_LATITUDE_ZZZ
import androidx.exifinterface.media.ExifInterface.TAG_JPEG_INTERCHANGE_FORMAT
import androidx.exifinterface.media.ExifInterface.TAG_JPEG_INTERCHANGE_FORMAT_LENGTH
import androidx.exifinterface.media.ExifInterface.TAG_LENS_MAKE
import androidx.exifinterface.media.ExifInterface.TAG_LENS_MODEL
import androidx.exifinterface.media.ExifInterface.TAG_LENS_SERIAL_NUMBER
import androidx.exifinterface.media.ExifInterface.TAG_LENS_SPECIFICATION
import androidx.exifinterface.media.ExifInterface.TAG_LIGHT_SOURCE
import androidx.exifinterface.media.ExifInterface.TAG_MAKE
import androidx.exifinterface.media.ExifInterface.TAG_MAKER_NOTE
import androidx.exifinterface.media.ExifInterface.TAG_MAX_APERTURE_VALUE
import androidx.exifinterface.media.ExifInterface.TAG_METERING_MODE
import androidx.exifinterface.media.ExifInterface.TAG_MODEL
import androidx.exifinterface.media.ExifInterface.TAG_NEW_SUBFILE_TYPE
import androidx.exifinterface.media.ExifInterface.TAG_OECF
import androidx.exifinterface.media.ExifInterface.TAG_OFFSET_TIME
import androidx.exifinterface.media.ExifInterface.TAG_OFFSET_TIME_DIGITIZED
import androidx.exifinterface.media.ExifInterface.TAG_OFFSET_TIME_ORIGINAL
import androidx.exifinterface.media.ExifInterface.TAG_ORF_ASPECT_FRAME
import androidx.exifinterface.media.ExifInterface.TAG_ORF_PREVIEW_IMAGE_LENGTH
import androidx.exifinterface.media.ExifInterface.TAG_ORF_PREVIEW_IMAGE_START
import androidx.exifinterface.media.ExifInterface.TAG_ORF_THUMBNAIL_IMAGE
import androidx.exifinterface.media.ExifInterface.TAG_ORIENTATION
import androidx.exifinterface.media.ExifInterface.TAG_PHOTOGRAPHIC_SENSITIVITY
import androidx.exifinterface.media.ExifInterface.TAG_PHOTOMETRIC_INTERPRETATION
import androidx.exifinterface.media.ExifInterface.TAG_PIXEL_X_DIMENSION
import androidx.exifinterface.media.ExifInterface.TAG_PIXEL_Y_DIMENSION
import androidx.exifinterface.media.ExifInterface.TAG_PLANAR_CONFIGURATION
import androidx.exifinterface.media.ExifInterface.TAG_PRIMARY_CHROMATICITIES
import androidx.exifinterface.media.ExifInterface.TAG_RECOMMENDED_EXPOSURE_INDEX
import androidx.exifinterface.media.ExifInterface.TAG_REFERENCE_BLACK_WHITE
import androidx.exifinterface.media.ExifInterface.TAG_RELATED_SOUND_FILE
import androidx.exifinterface.media.ExifInterface.TAG_RESOLUTION_UNIT
import androidx.exifinterface.media.ExifInterface.TAG_ROWS_PER_STRIP
import androidx.exifinterface.media.ExifInterface.TAG_RW2_ISO
import androidx.exifinterface.media.ExifInterface.TAG_RW2_JPG_FROM_RAW
import androidx.exifinterface.media.ExifInterface.TAG_RW2_SENSOR_BOTTOM_BORDER
import androidx.exifinterface.media.ExifInterface.TAG_RW2_SENSOR_LEFT_BORDER
import androidx.exifinterface.media.ExifInterface.TAG_RW2_SENSOR_RIGHT_BORDER
import androidx.exifinterface.media.ExifInterface.TAG_RW2_SENSOR_TOP_BORDER
import androidx.exifinterface.media.ExifInterface.TAG_SAMPLES_PER_PIXEL
import androidx.exifinterface.media.ExifInterface.TAG_SATURATION
import androidx.exifinterface.media.ExifInterface.TAG_SCENE_CAPTURE_TYPE
import androidx.exifinterface.media.ExifInterface.TAG_SCENE_TYPE
import androidx.exifinterface.media.ExifInterface.TAG_SENSING_METHOD
import androidx.exifinterface.media.ExifInterface.TAG_SENSITIVITY_TYPE
import androidx.exifinterface.media.ExifInterface.TAG_SHARPNESS
import androidx.exifinterface.media.ExifInterface.TAG_SHUTTER_SPEED_VALUE
import androidx.exifinterface.media.ExifInterface.TAG_SOFTWARE
import androidx.exifinterface.media.ExifInterface.TAG_SPATIAL_FREQUENCY_RESPONSE
import androidx.exifinterface.media.ExifInterface.TAG_STANDARD_OUTPUT_SENSITIVITY
import androidx.exifinterface.media.ExifInterface.TAG_STRIP_BYTE_COUNTS
import androidx.exifinterface.media.ExifInterface.TAG_STRIP_OFFSETS
import androidx.exifinterface.media.ExifInterface.TAG_SUBFILE_TYPE
import androidx.exifinterface.media.ExifInterface.TAG_SUBJECT_AREA
import androidx.exifinterface.media.ExifInterface.TAG_SUBJECT_DISTANCE
import androidx.exifinterface.media.ExifInterface.TAG_SUBJECT_DISTANCE_RANGE
import androidx.exifinterface.media.ExifInterface.TAG_SUBJECT_LOCATION
import androidx.exifinterface.media.ExifInterface.TAG_SUBSEC_TIME
import androidx.exifinterface.media.ExifInterface.TAG_SUBSEC_TIME_DIGITIZED
import androidx.exifinterface.media.ExifInterface.TAG_SUBSEC_TIME_ORIGINAL
import androidx.exifinterface.media.ExifInterface.TAG_THUMBNAIL_IMAGE_LENGTH
import androidx.exifinterface.media.ExifInterface.TAG_THUMBNAIL_IMAGE_WIDTH
import androidx.exifinterface.media.ExifInterface.TAG_TRANSFER_FUNCTION
import androidx.exifinterface.media.ExifInterface.TAG_USER_COMMENT
import androidx.exifinterface.media.ExifInterface.TAG_WHITE_BALANCE
import androidx.exifinterface.media.ExifInterface.TAG_WHITE_POINT
import androidx.exifinterface.media.ExifInterface.TAG_XMP
import androidx.exifinterface.media.ExifInterface.TAG_X_RESOLUTION
import androidx.exifinterface.media.ExifInterface.TAG_Y_CB_CR_COEFFICIENTS
import androidx.exifinterface.media.ExifInterface.TAG_Y_CB_CR_POSITIONING
import androidx.exifinterface.media.ExifInterface.TAG_Y_CB_CR_SUB_SAMPLING
import androidx.exifinterface.media.ExifInterface.TAG_Y_RESOLUTION
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.hu.demo.base.ui.BaseActivity
import com.hu.demo.main.R
import com.hu.demo.main.adapter.BaseData
import com.hu.demo.main.adapter.BaseVH
import com.hu.demo.main.adapter.DefaultAdapter
import com.hu.demo.main.works.main.TYPE_DEFAULT
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.ByteArrayInputStream

class ExifActivity : BaseActivity(), View.OnClickListener {
    private var contentUri: Uri? = null
    private var rvList: RecyclerView? = null
    private var btnSelectImg: Button? = null
    private var exifAdapter: ExifAdapter? = null
    private val pickTypeResultContract = PickTypeResultContract()
    private val pickImageLauncher = registerForActivityResult(
        pickTypeResultContract,
        PickOnlyCallback { changeData(it) }
    )

    override fun getLayoutId(): Int {
        return R.layout.activity_exif
    }

    override fun initToolbar() {
        super.initToolbar()
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowTitleEnabled(true)
            setDisplayShowHomeEnabled(false)
        }
    }

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        rvList = findViewById(R.id.rv_list)
        btnSelectImg = findViewById(R.id.btnSelectImg)
        findViewById<ImageButton>(R.id.changeExif)?.setOnClickListener {
            Log.d(TAG, "initView: hucanhua")
        }
    }

    override fun initEvent(savedInstanceState: Bundle?) {
        super.initEvent(savedInstanceState)
        btnSelectImg?.setOnClickListener(this)
        rvList?.apply {
            addItemDecoration(DividerItemDecoration(this@ExifActivity, RecyclerView.VERTICAL))
            adapter = ExifAdapter(this@ExifActivity, this).apply {
                resetData(emptyList())
                exifAdapter = this
            }
            layoutManager = LinearLayoutManager(this@ExifActivity, RecyclerView.VERTICAL, false)
        }
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.btnSelectImg -> {
                pickImageLauncher.launch(MIME_TYPE)
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun changeData(obj: Any?) {
        contentUri = obj as Uri
        refreshData()
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun refreshData() {
        lifecycleScope.launch(Dispatchers.Default) {
            val uri = contentUri ?: return@launch
            if (contentResolver.getType(uri)?.startsWith("image/") == true) {
                contentResolver.openFileDescriptor(uri, "r")?.use {
                    val exifInterface = ExifInterface(it.fileDescriptor)
                    val datas = buildExifData(exifInterface)
                    withContext(Dispatchers.Main) {
                        exifAdapter?.resetData(datas)
                        exifAdapter?.notifyDataSetChanged()
                    }
                }
            } else {
                val retriever = MediaMetadataRetriever()
                retriever.setDataSource(this@ExifActivity, uri)
                val exifOffset = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_EXIF_OFFSET)?.toLongOrNull()
                val exifLength = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_EXIF_LENGTH)?.toLongOrNull()
                retriever.close()
                exifOffset ?: return@launch
                exifLength ?: return@launch
                val bytes = contentResolver.openInputStream(uri)?.use {
                    it.skipNBytes(exifOffset)
                    it.readNBytes(exifLength.toInt())
                }
                ByteArrayInputStream(bytes).use {
                    val exifInterface = ExifInterface(it, STREAM_TYPE_EXIF_DATA_ONLY)
                    val datas = buildExifData(exifInterface)
                    withContext(Dispatchers.Main) {
                        exifAdapter?.resetData(datas)
                        exifAdapter?.notifyDataSetChanged()
                    }
                }
            }
        }
    }

    private fun buildExifData(exifInterface: ExifInterface): MutableList<ExifData> {
        val datas = mutableListOf<ExifData>()
        datas.add(ExifData(TAG_IMAGE_WIDTH, exifInterface.getAttribute(TAG_IMAGE_WIDTH)))
        datas.add(ExifData(TAG_IMAGE_LENGTH, exifInterface.getAttribute(TAG_IMAGE_LENGTH)))
        datas.add(ExifData(TAG_BITS_PER_SAMPLE, exifInterface.getAttribute(TAG_BITS_PER_SAMPLE)))
        datas.add(ExifData(TAG_COMPRESSION, exifInterface.getAttribute(TAG_COMPRESSION)))
        datas.add(ExifData(TAG_PHOTOMETRIC_INTERPRETATION, exifInterface.getAttribute(TAG_PHOTOMETRIC_INTERPRETATION)))
        datas.add(ExifData(TAG_ORIENTATION, exifInterface.getAttribute(TAG_ORIENTATION)))
        datas.add(ExifData(TAG_SAMPLES_PER_PIXEL, exifInterface.getAttribute(TAG_SAMPLES_PER_PIXEL)))
        datas.add(ExifData(TAG_PLANAR_CONFIGURATION, exifInterface.getAttribute(TAG_PLANAR_CONFIGURATION)))
        datas.add(ExifData(TAG_Y_CB_CR_SUB_SAMPLING, exifInterface.getAttribute(TAG_Y_CB_CR_SUB_SAMPLING)))
        datas.add(ExifData(TAG_Y_CB_CR_POSITIONING, exifInterface.getAttribute(TAG_Y_CB_CR_POSITIONING)))
        datas.add(ExifData(TAG_X_RESOLUTION, exifInterface.getAttribute(TAG_X_RESOLUTION)))
        datas.add(ExifData(TAG_Y_RESOLUTION, exifInterface.getAttribute(TAG_Y_RESOLUTION)))
        datas.add(ExifData(TAG_RESOLUTION_UNIT, exifInterface.getAttribute(TAG_RESOLUTION_UNIT)))
        datas.add(ExifData(TAG_STRIP_OFFSETS, exifInterface.getAttribute(TAG_STRIP_OFFSETS)))
        datas.add(ExifData(TAG_ROWS_PER_STRIP, exifInterface.getAttribute(TAG_ROWS_PER_STRIP)))
        datas.add(ExifData(TAG_STRIP_BYTE_COUNTS, exifInterface.getAttribute(TAG_STRIP_BYTE_COUNTS)))
        datas.add(ExifData(TAG_JPEG_INTERCHANGE_FORMAT, exifInterface.getAttribute(TAG_JPEG_INTERCHANGE_FORMAT)))
        datas.add(ExifData(TAG_JPEG_INTERCHANGE_FORMAT_LENGTH, exifInterface.getAttribute(TAG_JPEG_INTERCHANGE_FORMAT_LENGTH)))
        datas.add(ExifData(TAG_TRANSFER_FUNCTION, exifInterface.getAttribute(TAG_TRANSFER_FUNCTION)))
        datas.add(ExifData(TAG_WHITE_POINT, exifInterface.getAttribute(TAG_WHITE_POINT)))
        datas.add(ExifData(TAG_PRIMARY_CHROMATICITIES, exifInterface.getAttribute(TAG_PRIMARY_CHROMATICITIES)))
        datas.add(ExifData(TAG_Y_CB_CR_COEFFICIENTS, exifInterface.getAttribute(TAG_Y_CB_CR_COEFFICIENTS)))
        datas.add(ExifData(TAG_REFERENCE_BLACK_WHITE, exifInterface.getAttribute(TAG_REFERENCE_BLACK_WHITE)))
        datas.add(ExifData(TAG_DATETIME, exifInterface.getAttribute(TAG_DATETIME)))
        datas.add(ExifData(TAG_IMAGE_DESCRIPTION, exifInterface.getAttribute(TAG_IMAGE_DESCRIPTION)))
        datas.add(ExifData(TAG_MAKE, exifInterface.getAttribute(TAG_MAKE)))
        datas.add(ExifData(TAG_MODEL, exifInterface.getAttribute(TAG_MODEL)))
        datas.add(ExifData(TAG_SOFTWARE, exifInterface.getAttribute(TAG_SOFTWARE)))
        datas.add(ExifData(TAG_ARTIST, exifInterface.getAttribute(TAG_ARTIST)))
        datas.add(ExifData(TAG_COPYRIGHT, exifInterface.getAttribute(TAG_COPYRIGHT)))
        datas.add(ExifData(TAG_EXIF_VERSION, exifInterface.getAttribute(TAG_EXIF_VERSION)))
        datas.add(ExifData(TAG_FLASHPIX_VERSION, exifInterface.getAttribute(TAG_FLASHPIX_VERSION)))
        datas.add(ExifData(TAG_COLOR_SPACE, exifInterface.getAttribute(TAG_COLOR_SPACE)))
        datas.add(ExifData(TAG_GAMMA, exifInterface.getAttribute(TAG_GAMMA)))
        datas.add(ExifData(TAG_PIXEL_X_DIMENSION, exifInterface.getAttribute(TAG_PIXEL_X_DIMENSION)))
        datas.add(ExifData(TAG_PIXEL_Y_DIMENSION, exifInterface.getAttribute(TAG_PIXEL_Y_DIMENSION)))
        datas.add(ExifData(TAG_COMPONENTS_CONFIGURATION, exifInterface.getAttribute(TAG_COMPONENTS_CONFIGURATION)))
        datas.add(ExifData(TAG_COMPRESSED_BITS_PER_PIXEL, exifInterface.getAttribute(TAG_COMPRESSED_BITS_PER_PIXEL)))
        datas.add(ExifData(TAG_MAKER_NOTE, exifInterface.getAttribute(TAG_MAKER_NOTE)))
        datas.add(ExifData(TAG_USER_COMMENT, exifInterface.getAttribute(TAG_USER_COMMENT)))
        datas.add(ExifData(TAG_RELATED_SOUND_FILE, exifInterface.getAttribute(TAG_RELATED_SOUND_FILE)))
        datas.add(ExifData(TAG_DATETIME_ORIGINAL, exifInterface.getAttribute(TAG_DATETIME_ORIGINAL)))
        datas.add(ExifData(TAG_DATETIME_DIGITIZED, exifInterface.getAttribute(TAG_DATETIME_DIGITIZED)))
        datas.add(ExifData(TAG_OFFSET_TIME, exifInterface.getAttribute(TAG_OFFSET_TIME)))
        datas.add(ExifData(TAG_OFFSET_TIME_ORIGINAL, exifInterface.getAttribute(TAG_OFFSET_TIME_ORIGINAL)))
        datas.add(ExifData(TAG_OFFSET_TIME_DIGITIZED, exifInterface.getAttribute(TAG_OFFSET_TIME_DIGITIZED)))
        datas.add(ExifData(TAG_SUBSEC_TIME, exifInterface.getAttribute(TAG_SUBSEC_TIME)))
        datas.add(ExifData(TAG_SUBSEC_TIME_ORIGINAL, exifInterface.getAttribute(TAG_SUBSEC_TIME_ORIGINAL)))
        datas.add(ExifData(TAG_SUBSEC_TIME_DIGITIZED, exifInterface.getAttribute(TAG_SUBSEC_TIME_DIGITIZED)))
        datas.add(ExifData(TAG_EXPOSURE_TIME, exifInterface.getAttribute(TAG_EXPOSURE_TIME)))
        datas.add(ExifData(TAG_F_NUMBER, exifInterface.getAttribute(TAG_F_NUMBER)))
        datas.add(ExifData(TAG_EXPOSURE_PROGRAM, exifInterface.getAttribute(TAG_EXPOSURE_PROGRAM)))
        datas.add(ExifData(TAG_PHOTOGRAPHIC_SENSITIVITY, exifInterface.getAttribute(TAG_PHOTOGRAPHIC_SENSITIVITY)))
        datas.add(ExifData(TAG_OECF, exifInterface.getAttribute(TAG_OECF)))
        datas.add(ExifData(TAG_SENSITIVITY_TYPE, exifInterface.getAttribute(TAG_SENSITIVITY_TYPE)))
        datas.add(ExifData(TAG_STANDARD_OUTPUT_SENSITIVITY, exifInterface.getAttribute(TAG_STANDARD_OUTPUT_SENSITIVITY)))
        datas.add(ExifData(TAG_RECOMMENDED_EXPOSURE_INDEX, exifInterface.getAttribute(TAG_RECOMMENDED_EXPOSURE_INDEX)))
        datas.add(ExifData(TAG_ISO_SPEED, exifInterface.getAttribute(TAG_ISO_SPEED)))
        datas.add(ExifData(TAG_ISO_SPEED_LATITUDE_YYY, exifInterface.getAttribute(TAG_ISO_SPEED_LATITUDE_YYY)))
        datas.add(ExifData(TAG_ISO_SPEED_LATITUDE_ZZZ, exifInterface.getAttribute(TAG_ISO_SPEED_LATITUDE_ZZZ)))
        datas.add(ExifData(TAG_SHUTTER_SPEED_VALUE, exifInterface.getAttribute(TAG_SHUTTER_SPEED_VALUE)))
        datas.add(ExifData(TAG_APERTURE_VALUE, exifInterface.getAttribute(TAG_APERTURE_VALUE)))
        datas.add(ExifData(TAG_BRIGHTNESS_VALUE, exifInterface.getAttribute(TAG_BRIGHTNESS_VALUE)))
        datas.add(ExifData(TAG_EXPOSURE_BIAS_VALUE, exifInterface.getAttribute(TAG_EXPOSURE_BIAS_VALUE)))
        datas.add(ExifData(TAG_MAX_APERTURE_VALUE, exifInterface.getAttribute(TAG_MAX_APERTURE_VALUE)))
        datas.add(ExifData(TAG_SUBJECT_DISTANCE, exifInterface.getAttribute(TAG_SUBJECT_DISTANCE)))
        datas.add(ExifData(TAG_METERING_MODE, exifInterface.getAttribute(TAG_METERING_MODE)))
        datas.add(ExifData(TAG_LIGHT_SOURCE, exifInterface.getAttribute(TAG_LIGHT_SOURCE)))
        datas.add(ExifData(TAG_FLASH, exifInterface.getAttribute(TAG_FLASH)))
        datas.add(ExifData(TAG_SUBJECT_AREA, exifInterface.getAttribute(TAG_SUBJECT_AREA)))
        datas.add(ExifData(TAG_FOCAL_LENGTH, exifInterface.getAttribute(TAG_FOCAL_LENGTH)))
        datas.add(ExifData(TAG_FLASH_ENERGY, exifInterface.getAttribute(TAG_FLASH_ENERGY)))
        datas.add(ExifData(TAG_SPATIAL_FREQUENCY_RESPONSE, exifInterface.getAttribute(TAG_SPATIAL_FREQUENCY_RESPONSE)))
        datas.add(ExifData(TAG_FOCAL_PLANE_X_RESOLUTION, exifInterface.getAttribute(TAG_FOCAL_PLANE_X_RESOLUTION)))
        datas.add(ExifData(TAG_FOCAL_PLANE_Y_RESOLUTION, exifInterface.getAttribute(TAG_FOCAL_PLANE_Y_RESOLUTION)))
        datas.add(ExifData(TAG_FOCAL_PLANE_RESOLUTION_UNIT, exifInterface.getAttribute(TAG_FOCAL_PLANE_RESOLUTION_UNIT)))
        datas.add(ExifData(TAG_SUBJECT_LOCATION, exifInterface.getAttribute(TAG_SUBJECT_LOCATION)))
        datas.add(ExifData(TAG_EXPOSURE_INDEX, exifInterface.getAttribute(TAG_EXPOSURE_INDEX)))
        datas.add(ExifData(TAG_SENSING_METHOD, exifInterface.getAttribute(TAG_SENSING_METHOD)))
        datas.add(ExifData(TAG_FILE_SOURCE, exifInterface.getAttribute(TAG_FILE_SOURCE)))
        datas.add(ExifData(TAG_SCENE_TYPE, exifInterface.getAttribute(TAG_SCENE_TYPE)))
        datas.add(ExifData(TAG_CFA_PATTERN, exifInterface.getAttribute(TAG_CFA_PATTERN)))
        datas.add(ExifData(TAG_CUSTOM_RENDERED, exifInterface.getAttribute(TAG_CUSTOM_RENDERED)))
        datas.add(ExifData(TAG_EXPOSURE_MODE, exifInterface.getAttribute(TAG_EXPOSURE_MODE)))
        datas.add(ExifData(TAG_WHITE_BALANCE, exifInterface.getAttribute(TAG_WHITE_BALANCE)))
        datas.add(ExifData(TAG_DIGITAL_ZOOM_RATIO, exifInterface.getAttribute(TAG_DIGITAL_ZOOM_RATIO)))
        datas.add(ExifData(TAG_FOCAL_LENGTH_IN_35MM_FILM, exifInterface.getAttribute(TAG_FOCAL_LENGTH_IN_35MM_FILM)))
        datas.add(ExifData(TAG_SCENE_CAPTURE_TYPE, exifInterface.getAttribute(TAG_SCENE_CAPTURE_TYPE)))
        datas.add(ExifData(TAG_GAIN_CONTROL, exifInterface.getAttribute(TAG_GAIN_CONTROL)))
        datas.add(ExifData(TAG_CONTRAST, exifInterface.getAttribute(TAG_CONTRAST)))
        datas.add(ExifData(TAG_SATURATION, exifInterface.getAttribute(TAG_SATURATION)))
        datas.add(ExifData(TAG_SHARPNESS, exifInterface.getAttribute(TAG_SHARPNESS)))
        datas.add(ExifData(TAG_DEVICE_SETTING_DESCRIPTION, exifInterface.getAttribute(TAG_DEVICE_SETTING_DESCRIPTION)))
        datas.add(ExifData(TAG_SUBJECT_DISTANCE_RANGE, exifInterface.getAttribute(TAG_SUBJECT_DISTANCE_RANGE)))
        datas.add(ExifData(TAG_IMAGE_UNIQUE_ID, exifInterface.getAttribute(TAG_IMAGE_UNIQUE_ID)))
        datas.add(ExifData(TAG_CAMERA_OWNER_NAME, exifInterface.getAttribute(TAG_CAMERA_OWNER_NAME)))
        datas.add(ExifData(TAG_BODY_SERIAL_NUMBER, exifInterface.getAttribute(TAG_BODY_SERIAL_NUMBER)))
        datas.add(ExifData(TAG_LENS_SPECIFICATION, exifInterface.getAttribute(TAG_LENS_SPECIFICATION)))
        datas.add(ExifData(TAG_LENS_MAKE, exifInterface.getAttribute(TAG_LENS_MAKE)))
        datas.add(ExifData(TAG_LENS_MODEL, exifInterface.getAttribute(TAG_LENS_MODEL)))
        datas.add(ExifData(TAG_LENS_SERIAL_NUMBER, exifInterface.getAttribute(TAG_LENS_SERIAL_NUMBER)))
        datas.add(ExifData(TAG_GPS_VERSION_ID, exifInterface.getAttribute(TAG_GPS_VERSION_ID)))
        datas.add(ExifData(TAG_GPS_LATITUDE_REF, exifInterface.getAttribute(TAG_GPS_LATITUDE_REF)))
        datas.add(ExifData(TAG_GPS_LATITUDE, exifInterface.getAttribute(TAG_GPS_LATITUDE)))
        datas.add(ExifData(TAG_GPS_LONGITUDE_REF, exifInterface.getAttribute(TAG_GPS_LONGITUDE_REF)))
        datas.add(ExifData(TAG_GPS_LONGITUDE, exifInterface.getAttribute(TAG_GPS_LONGITUDE)))
        datas.add(ExifData(TAG_GPS_ALTITUDE_REF, exifInterface.getAttribute(TAG_GPS_ALTITUDE_REF)))
        datas.add(ExifData(TAG_GPS_ALTITUDE, exifInterface.getAttribute(TAG_GPS_ALTITUDE)))
        datas.add(ExifData(TAG_GPS_TIMESTAMP, exifInterface.getAttribute(TAG_GPS_TIMESTAMP)))
        datas.add(ExifData(TAG_GPS_SATELLITES, exifInterface.getAttribute(TAG_GPS_SATELLITES)))
        datas.add(ExifData(TAG_GPS_STATUS, exifInterface.getAttribute(TAG_GPS_STATUS)))
        datas.add(ExifData(TAG_GPS_MEASURE_MODE, exifInterface.getAttribute(TAG_GPS_MEASURE_MODE)))
        datas.add(ExifData(TAG_GPS_DOP, exifInterface.getAttribute(TAG_GPS_DOP)))
        datas.add(ExifData(TAG_GPS_SPEED_REF, exifInterface.getAttribute(TAG_GPS_SPEED_REF)))
        datas.add(ExifData(TAG_GPS_SPEED, exifInterface.getAttribute(TAG_GPS_SPEED)))
        datas.add(ExifData(TAG_GPS_TRACK_REF, exifInterface.getAttribute(TAG_GPS_TRACK_REF)))
        datas.add(ExifData(TAG_GPS_TRACK, exifInterface.getAttribute(TAG_GPS_TRACK)))
        datas.add(ExifData(TAG_GPS_IMG_DIRECTION_REF, exifInterface.getAttribute(TAG_GPS_IMG_DIRECTION_REF)))
        datas.add(ExifData(TAG_GPS_IMG_DIRECTION, exifInterface.getAttribute(TAG_GPS_IMG_DIRECTION)))
        datas.add(ExifData(TAG_GPS_MAP_DATUM, exifInterface.getAttribute(TAG_GPS_MAP_DATUM)))
        datas.add(ExifData(TAG_GPS_DEST_LATITUDE_REF, exifInterface.getAttribute(TAG_GPS_DEST_LATITUDE_REF)))
        datas.add(ExifData(TAG_GPS_DEST_LATITUDE, exifInterface.getAttribute(TAG_GPS_DEST_LATITUDE)))
        datas.add(ExifData(TAG_GPS_DEST_LONGITUDE_REF, exifInterface.getAttribute(TAG_GPS_DEST_LONGITUDE_REF)))
        datas.add(ExifData(TAG_GPS_DEST_LONGITUDE, exifInterface.getAttribute(TAG_GPS_DEST_LONGITUDE)))
        datas.add(ExifData(TAG_GPS_DEST_BEARING_REF, exifInterface.getAttribute(TAG_GPS_DEST_BEARING_REF)))
        datas.add(ExifData(TAG_GPS_DEST_BEARING, exifInterface.getAttribute(TAG_GPS_DEST_BEARING)))
        datas.add(ExifData(TAG_GPS_DEST_DISTANCE_REF, exifInterface.getAttribute(TAG_GPS_DEST_DISTANCE_REF)))
        datas.add(ExifData(TAG_GPS_DEST_DISTANCE, exifInterface.getAttribute(TAG_GPS_DEST_DISTANCE)))
        datas.add(ExifData(TAG_GPS_PROCESSING_METHOD, exifInterface.getAttribute(TAG_GPS_PROCESSING_METHOD)))
        datas.add(ExifData(TAG_GPS_AREA_INFORMATION, exifInterface.getAttribute(TAG_GPS_AREA_INFORMATION)))
        datas.add(ExifData(TAG_GPS_DATESTAMP, exifInterface.getAttribute(TAG_GPS_DATESTAMP)))
        datas.add(ExifData(TAG_GPS_DIFFERENTIAL, exifInterface.getAttribute(TAG_GPS_DIFFERENTIAL)))
        datas.add(ExifData(TAG_GPS_H_POSITIONING_ERROR, exifInterface.getAttribute(TAG_GPS_H_POSITIONING_ERROR)))
        datas.add(ExifData(TAG_INTEROPERABILITY_INDEX, exifInterface.getAttribute(TAG_INTEROPERABILITY_INDEX)))
        datas.add(ExifData(TAG_THUMBNAIL_IMAGE_LENGTH, exifInterface.getAttribute(TAG_THUMBNAIL_IMAGE_LENGTH)))
        datas.add(ExifData(TAG_THUMBNAIL_IMAGE_WIDTH, exifInterface.getAttribute(TAG_THUMBNAIL_IMAGE_WIDTH)))
        datas.add(ExifData(TAG_DNG_VERSION, exifInterface.getAttribute(TAG_DNG_VERSION)))
        datas.add(ExifData(TAG_DEFAULT_CROP_SIZE, exifInterface.getAttribute(TAG_DEFAULT_CROP_SIZE)))
        datas.add(ExifData(TAG_ORF_THUMBNAIL_IMAGE, exifInterface.getAttribute(TAG_ORF_THUMBNAIL_IMAGE)))
        datas.add(ExifData(TAG_ORF_PREVIEW_IMAGE_START, exifInterface.getAttribute(TAG_ORF_PREVIEW_IMAGE_START)))
        datas.add(ExifData(TAG_ORF_PREVIEW_IMAGE_LENGTH, exifInterface.getAttribute(TAG_ORF_PREVIEW_IMAGE_LENGTH)))
        datas.add(ExifData(TAG_ORF_ASPECT_FRAME, exifInterface.getAttribute(TAG_ORF_ASPECT_FRAME)))
        datas.add(ExifData(TAG_RW2_SENSOR_BOTTOM_BORDER, exifInterface.getAttribute(TAG_RW2_SENSOR_BOTTOM_BORDER)))
        datas.add(ExifData(TAG_RW2_SENSOR_LEFT_BORDER, exifInterface.getAttribute(TAG_RW2_SENSOR_LEFT_BORDER)))
        datas.add(ExifData(TAG_RW2_SENSOR_RIGHT_BORDER, exifInterface.getAttribute(TAG_RW2_SENSOR_RIGHT_BORDER)))
        datas.add(ExifData(TAG_RW2_SENSOR_TOP_BORDER, exifInterface.getAttribute(TAG_RW2_SENSOR_TOP_BORDER)))
        datas.add(ExifData(TAG_RW2_ISO, exifInterface.getAttribute(TAG_RW2_ISO)))
        datas.add(ExifData(TAG_RW2_JPG_FROM_RAW, exifInterface.getAttribute(TAG_RW2_JPG_FROM_RAW)))
        datas.add(ExifData(TAG_XMP, exifInterface.getAttribute(TAG_XMP)))
        datas.add(ExifData(TAG_NEW_SUBFILE_TYPE, exifInterface.getAttribute(TAG_NEW_SUBFILE_TYPE)))
        datas.add(ExifData(TAG_SUBFILE_TYPE, exifInterface.getAttribute(TAG_SUBFILE_TYPE)))
        return datas
    }

    private open class PickOnlyCallback(private val resultFunc: (Uri) -> Unit) : ActivityResultCallback<Uri?> {
        override fun onActivityResult(result: Uri?) {
            result ?: run {
                Log.d(TAG, "PickOnlyCallback.onActivityResult: ")
                return
            }
            Log.d(TAG, "onActivityResult: $result")
            resultFunc(result)
        }
    }

    private class PickTypeResultContract : ActivityResultContract<String, Uri?>() {
        override fun createIntent(context: Context, input: String): Intent {
            return Intent(Intent.ACTION_PICK).setType(input)
        }

        override fun parseResult(resultCode: Int, intent: Intent?): Uri? {
            return if (resultCode != Activity.RESULT_OK) null else intent?.data
        }
    }

    class ExifVH(parent: ViewGroup) : BaseVH<ExifData>(parent, R.layout.item_exif) {
        private val tvKey: TextView = findViewById(R.id.tv_item_key)
        private val tvValue: TextView = findViewById(R.id.tv_item_value)
        private val changeExif: ImageView = findViewById(R.id.changeExif)

        override fun bind(extraData: Map<String, Any>, data: ExifData, position: Int) {
            tvKey.text = data.key
            tvValue.text = data.value
            changeExif.setOnClickListener {
                val activity = extraData[KEY_ACTIVITY] as? ExifActivity ?: return@setOnClickListener
                showDialog(activity, data)
            }
        }

        private fun showDialog(activity: ExifActivity, data: ExifData) {
            Log.d(TAG, "showDialog: $data")
            val editText = EditText(activity)
            editText.setText(data.value)
            AlertDialog.Builder(activity)
                .setTitle(data.key)
                .setView(editText)
                .setNegativeButton("取消", null)
                .setPositiveButton("确定") { _, _ ->
                    activity.lifecycleScope.launch(Dispatchers.Default) {
                        activity.contentResolver.openFileDescriptor(activity.contentUri!!, "rw")?.use {
                            val exifInterface = ExifInterface(it.fileDescriptor)
                            exifInterface.setAttribute(data.key!!, editText.text.toString())
                            exifInterface.saveAttributes()
                        }
                        activity.refreshData()
                        Log.d(TAG, "showDialog: save ${editText.text.toString()}")
                    }
                }.show()
        }
    }

    data class ExifData(val key: String?, val value: String?) : BaseData(TYPE_DEFAULT)


    class ExifAdapter(val activity: ExifActivity, recyclerView: RecyclerView) : DefaultAdapter<BaseData, BaseVH<BaseData>>(recyclerView) {
        @Suppress("UNCHECKED_CAST")
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH<BaseData> {
            return ExifVH(parent) as BaseVH<BaseData>
        }

        override fun onBindViewHolder(holder: BaseVH<BaseData>, position: Int) {
            holder.bind(mapOf(KEY_ACTIVITY to activity), dataList[position], position)
        }
    }

    companion object {
        private const val TAG = "ExifActivity"
        private const val MIME_TYPE = "*/*"
        private const val KEY_ACTIVITY = "activity"
    }

}