package com.hu.demo.main.works.recyclerview.drag

import android.graphics.Canvas
import android.graphics.PointF
import android.util.Log
import android.view.MotionEvent
import android.view.View
import android.view.ViewConfiguration
import android.view.ViewGroup
import androidx.core.view.ViewCompat
import com.hu.demo.main.R
import com.hu.demo.main.works.recyclerview.longclick.LongClickCallback
import com.hu.demo.main.works.recyclerview.view.AutoScroll
import com.hu.demo.main.works.recyclerview.view.IAttachView
import com.hu.demo.main.works.recyclerview.view.IItemTouchProcessor
import com.hu.demo.main.works.recyclerview.view.ViewTouchAdapter
import kotlin.math.min

class ItemDragHelper<V : ViewGroup>(
    private val dragCallback: IDragCallback?,
) : IItemTouchProcessor<V>, IAttachView<V>, LongClickCallback {
    private val downP = PointF(0f, 0f)
    private val lastP = PointF(0f, 0f)
    private val dragViewStartP = PointF(0f, 0f)
    private val autoScroll: AutoScroll = AutoScroll {
        doMove()
    }
    private var defaultAutoScrollH: Int = 0
    private var scaledTouchSlop: Int = 0
    private var autoScrollHeight = 0
    private var touchAdapter: ViewTouchAdapter<V>? = null
    private var lastPos = INVALID_POS
    private var dragView: View? = null
    private var isLongClicked = false
    private var selfDisallowIntercept = false

    override fun onLongClick(e: MotionEvent, pos: Int): Boolean {
        val touchAdapter = touchAdapter ?: return false
        dragCallback ?: return false
        return (if (pos != INVALID_POS && lastPos != INVALID_POS && pos == lastPos) {
            isLongClicked = true
            Log.d(TAG, "onInterceptTouchEvent: drag start. ")
            setElevation(touchAdapter.view, dragView!!)
            dragCallback.onDragStart(dragView!!, lastPos)
            true
        } else false).apply {
            Log.d(TAG, "onLongClick: is called $this")
        }
    }

    override fun onInterceptTouchEvent(e: MotionEvent): Boolean {
        val touchAdapter = touchAdapter ?: return false
        dragCallback ?: return false
        if (e.actionMasked == MotionEvent.ACTION_DOWN) {
            downP.set(e.x, e.y)
            lastP.set(e.x, e.y)
            val findView = touchAdapter.findChildView(e.x, e.y, false)
            findView ?: return false
            val pos = touchAdapter.findChildPosition(findView)
            if ((pos != INVALID_POS) && dragCallback.canMove(pos)) {
                lastPos = pos
                dragView = findView
                dragViewStartP.set(findView.left.toFloat(), findView.top.toFloat())
                autoScrollHeight = defaultAutoScrollH.coerceAtMost(min(downP.y, (touchAdapter.view.height - downP.y)).toInt())
            }
        } else if ((e.actionMasked == MotionEvent.ACTION_UP) || (e.actionMasked == MotionEvent.ACTION_CANCEL)) {
            reset()
        }
        return ((dragView != null) && isLongClicked).apply {
            if (this) {
                selfDisallowIntercept = true
                touchAdapter.view.requestDisallowInterceptTouchEvent(true)
                selfDisallowIntercept = false
            }
        }
    }

    override fun onTouchEvent(e: MotionEvent) {
        val touchAdapter = touchAdapter ?: return
        dragCallback ?: return
        if (e.actionMasked == MotionEvent.ACTION_MOVE) {
            lastP.set(e.x, e.y)
            doMove()

            dealAutoScroll()

            touchAdapter.view.parent?.requestDisallowInterceptTouchEvent(true)
        } else if ((e.actionMasked == MotionEvent.ACTION_UP) || (e.actionMasked == MotionEvent.ACTION_CANCEL)) {
            reset()
        }
    }

    override fun onDraw(c: Canvas, view: V) {
        dragView?.apply {
            translationX = lastP.x - downP.x - (left - dragViewStartP.x)
            translationY = lastP.y - downP.y - (top - dragViewStartP.y)
        }
    }

    private fun doMove() {
        val touchAdapter = touchAdapter ?: return
        dragCallback ?: return
        Log.d(TAG, "doMove: ${dragView?.left}, ${dragView?.top}")

        touchAdapter.view.invalidate()

        val pos = touchAdapter.findChildView(lastP.x, lastP.y, false)?.let {
            touchAdapter.findChildPosition(it)
        } ?: INVALID_POS
        if ((lastPos != INVALID_POS) && (pos != INVALID_POS) && (pos != lastPos) && dragCallback.canMove(pos)) {
            dragCallback.onDragMove(lastPos, pos)
            lastPos = pos
        }
    }

    /**
     * 处理自动滚动
     */
    private fun dealAutoScroll() {
        val view = touchAdapter?.view ?: return
        if (((downP.y - lastP.y) > scaledTouchSlop) && (lastP.y < autoScrollHeight)) {
            // 处理上滑自动滚动
            autoScroll.setVelocityScale((lastP.y - autoScrollHeight) / autoScrollHeight.toFloat())
            autoScroll.start(view)
        } else if (((lastP.y - downP.y) > scaledTouchSlop) && (lastP.y > (view.height - autoScrollHeight))) {
            // 处理下滑自动滚动
            autoScroll.setVelocityScale((autoScrollHeight - (view.height - lastP.y)) / autoScrollHeight.toFloat())
            autoScroll.start(view)
        } else {
            // 处理停止
            autoScroll.cancel()
        }
    }

    override fun onRequestDisallowInterceptTouchEvent(disallowIntercept: Boolean) {
        if (!selfDisallowIntercept && disallowIntercept) {
            reset()
        }
    }

    private fun reset() {
        Log.d(TAG, "reset: is called")
        if (dragView != null) {
            revertElevation()
            dragCallback?.onDragEnd(dragView!!, lastPos)
        }
        lastPos = INVALID_POS
        dragView = null
        isLongClicked = false
    }

    private fun setElevation(parent: ViewGroup, view: View) {
        var originalElevation = view.getTag(R.id.item_touch_helper_previous_elevation)
        if (originalElevation == null) {
            originalElevation = ViewCompat.getElevation(view)
            ViewCompat.setElevation(view, 1f + findMaxElevation(parent, view))
            view.setTag(R.id.item_touch_helper_previous_elevation, originalElevation)
        }
    }

    private fun revertElevation() {
        dragView?.apply {
            val tag = getTag(R.id.item_touch_helper_previous_elevation)
            if (tag is Float) {
                ViewCompat.setElevation(this, tag)
            }
            dragView!!.setTag(R.id.item_touch_helper_previous_elevation, null)
        }
    }

    override fun attachToView(viewTouchAdapter: ViewTouchAdapter<V>?) {
        touchAdapter?.apply {
            reset()
            removeCallback(this@ItemDragHelper)
        }
        touchAdapter = viewTouchAdapter?.apply {
            addCallback(this@ItemDragHelper)
            scaledTouchSlop = ViewConfiguration.get(view.context).scaledTouchSlop
            defaultAutoScrollH = view.resources.getDimensionPixelSize(R.dimen.toolbar_min_height)
            autoScrollHeight = defaultAutoScrollH
        }
    }

    companion object {
        private const val TAG = "ItemDragHelper"
        private const val INVALID_POS = -1

        private fun findMaxElevation(viewGroup: ViewGroup, itemView: View): Float {
            val childCount = viewGroup.childCount
            var max = 0f
            for (i in 0 until childCount) {
                val child = viewGroup.getChildAt(i)
                if (child === itemView) {
                    continue
                }
                val elevation = ViewCompat.getElevation(child)
                if (elevation > max) {
                    max = elevation
                }
            }
            return max
        }
    }
}

interface IDragCallback {
    fun canMove(pos: Int): Boolean
    fun onDragStart(dragView: View, from: Int)
    fun onDragMove(from: Int, to: Int)
    fun onDragEnd(dragView: View, to: Int)
}
