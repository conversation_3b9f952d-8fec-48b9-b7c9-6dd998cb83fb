/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - Renderer.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/08/26
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery		2024/08/26		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.renderer.normal

import android.util.ArrayMap
import android.util.Log
import com.hu.demo.main.works.gl.IRecyclable
import com.hu.demo.main.works.gl.IReusable
import com.hu.demo.main.works.gl.renderer.IName
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.toNodePath
import com.hu.demo.main.works.gl.renderer.param.RenderArgs
import com.hu.demo.main.works.gl.shader.ProgramShader
import com.hu.demo.main.works.gl.utils.GlProgram
import com.hu.demo.main.works.gl.utils.ShaderParam
import kotlin.properties.Delegates

/**
 * 渲染器，对到当前步骤的纹理等执行操作
 */
abstract class Renderer(final override val name: String) : IReusable, IRecyclable, IName {
    /**
     * 节点的全路径
     */
    var nodePath = arrayOf(name).toNodePath()
        private set

    /**
     * 当前节点的父节点
     */
    var parent: RendererGroup? by Delegates.observable(null) { _, _, new ->
        nodePath = new?.nodePath?.plus(name) ?: arrayOf(name).toNodePath()
    }
        internal set

    internal abstract val programShader: ProgramShader

    private var hasCreated = false

    private var hasDestroyed = false

    private var hasInstalled = false

    protected abstract val handleParams: Array<ShaderParam>

    /**
     * 编辑构建program，当传入的shader为null时，则编译当前[Renderer]内部的[programShader]
     *
     * @param shader shader脚本
     *
     * @return 编译后的OpenGL程序
     */
    open fun install(shader: ProgramShader? = null): GlProgram? {
        val lShader = shader ?: programShader
        if (lShader.isEmpty()) return null

        val glProgram = GlProgram.obtain(lShader)

        if (hasInstalled) {
            return glProgram
        }

        glProgram.install()
        glProgram.loadHandle(handleParams)
        hasInstalled = true
        return glProgram
    }

    /**
     * 执行render方法，
     * - 当是[Renderer]时，则会按照只执行一次执行[render]；
     * - 当是[RendererGroup]时，则不会执行[render]
     */
    fun performRender(renderArgs: RenderArgs, glProgram: GlProgram? = null) {
        val program = install(glProgram?.programShader)
        render(renderArgs, program)
    }

    /**
     * 执行渲染的方法
     *
     * @param glProgram 渲染器程序
     * @param renderArgs 渲染参数
     */
    protected abstract fun render(renderArgs: RenderArgs, glProgram: GlProgram?)

    override fun reuse() = Unit

    override fun recycle() = Unit

    override fun toString(): String {
        return name
    }

    companion object {
        private const val TAG = "Renderer"

        /**
         * [Renderer]的缓存，由于Renderer的特殊性，不会存在并行使用情况，且需要复用，因此需要使用[ArrayMap]
         */
        private val cache = object : ThreadLocal<ArrayMap<Class<out Renderer>, Renderer>>() {
            override fun initialValue(): ArrayMap<Class<out Renderer>, Renderer> {
                return ArrayMap<Class<out Renderer>, Renderer>()
            }
        }

        /**
         * 执行对象创建的调用函数
         */
        private val createFunc = { clazz: Class<out Renderer> ->
            clazz.getDeclaredConstructor().run {
                isAccessible = true
                newInstance()
            }
        }

        /**
         * 获取渲染器
         *
         * @param clazz [Renderer]的类
         */
        @Suppress("UNCHECKED_CAST")
        fun <T : Renderer> get(clazz: Class<T>): T {
            return cache.get()!!.getOrPut(clazz) { new(clazz) } as T
        }

        /**
         * 渲染器
         *
         * @param clazz [Renderer]的类
         */
        @Suppress("UNCHECKED_CAST")
        fun <T : Renderer> new(clazz: Class<T>): T {
            return createFunc(clazz) as T
        }

        /**
         * 对渲染器缓存执行清除
         */
        fun clean() {
            Log.d(TAG, "clean: is called.")
            cache.get()!!.values.forEach {
                it.recycle()
            }
            cache.remove()
        }
    }
}