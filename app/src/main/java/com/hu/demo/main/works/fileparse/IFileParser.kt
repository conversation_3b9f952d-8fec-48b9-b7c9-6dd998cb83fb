package com.hu.demo.main.works.fileparse

import android.system.Os
import android.system.OsConstants
import kotlinx.coroutines.flow.Flow
import java.io.FileDescriptor
import java.io.FileInputStream
import java.io.InputStream

interface IFileParser {
    fun canParse(iStream: IStreamSource): Boolean
    suspend fun parse(iStream: IStreamSource): Flow<CharSequence>

    suspend fun parse(fd: FileDescriptor): Flow<CharSequence> {
        Os.lseek(fd, 0, OsConstants.SEEK_SET)
        return parse(FdSource(fd))
    }
}

interface IStreamSource {
    fun newStream(): InputStream
}

class FdSource(val fd: FileDescriptor) : IStreamSource {
    override fun newStream(): InputStream {
        Os.lseek(fd, 0, OsConstants.SEEK_SET)
        return FileInputStream(fd)
    }

}