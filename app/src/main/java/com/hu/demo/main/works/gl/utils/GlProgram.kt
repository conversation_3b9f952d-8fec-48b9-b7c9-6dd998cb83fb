/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - GlProgram.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2025/01/08
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery		2025/01/08		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.utils

import android.opengl.GLES30
import android.util.ArrayMap
import android.util.Log
import com.hu.demo.utils.getOrLog
import com.hu.demo.main.works.gl.IRecyclable
import com.hu.demo.main.works.gl.shader.ProgramShader
import java.nio.Buffer
import java.nio.FloatBuffer

class GlProgram private constructor(val programShader: ProgramShader) : IRecyclable {
    private var programId: Int = GLES30.GL_NONE

    private var isInstalled = false
    private var isRecycled: Boolean = false

    private val shaderParamKeys = mutableMapOf<ShaderParamKey, Int>()

    fun install() {
        if (isInstalled) return
        isInstalled = true
        programId = GLES30.glCreateProgram()
        // Add the vertex and fragment shaders.
        addShader(programId, GLES30.GL_VERTEX_SHADER, programShader.vertexGlShader.build())
        addShader(programId, GLES30.GL_FRAGMENT_SHADER, programShader.fragmentGlShader.build())

        // Link and use the program, and enumerate attributes/uniforms.
        GLES30.glLinkProgram(programId)
        val linkStatus = singleInt { GLES30.glGetProgramiv(programId, GLES30.GL_LINK_STATUS, it, 0) }
        GlUtil.checkGlException(
            linkStatus == GLES30.GL_TRUE,
            "Unable to link shader program: ${GLES30.glGetProgramInfoLog(programId)}"
        )

        val attributeCount = singleInt { GLES30.glGetProgramiv(programId, GLES30.GL_ACTIVE_ATTRIBUTES, it, 0) }
        for (i in 0 until attributeCount) {
            val shaderParamKey = ShaderParamKey.createAttr(programId, i)
            shaderParamKeys[shaderParamKey] = GLES30.glGetAttribLocation(programId, shaderParamKey.name)
        }
        val uniformCount = singleInt { GLES30.glGetProgramiv(programId, GLES30.GL_ACTIVE_UNIFORMS, it, 0) }
        for (i in 0 until uniformCount) {
            val shaderParamKey = ShaderParamKey.createUniform(programId, i)
            shaderParamKeys[shaderParamKey] = GLES30.glGetUniformLocation(programId, shaderParamKey.name)
        }
    }

    fun loadHandle(shaderParams: Array<ShaderParam>) {
        use()
        shaderParams.forEach {
            it.handle = shaderParamKeys[it.key].getOrLog(TAG, "loadHandle ${it.key}") ?: GLES30.GL_INVALID_INDEX
        }
        unUse()
    }

    fun use() {
        GLES30.glUseProgram(programId)
        GlUtil.checkGlError()
    }

    fun unUse() {
        GLES30.glUseProgram(GLES30.GL_NONE)
        GlUtil.checkGlError()
    }

    override fun recycle() {
        if (isRecycled) return
        isRecycled = true
        GLES30.glDeleteProgram(programId)
        GlUtil.checkGlError()
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as GlProgram

        return programId == other.programId
    }

    override fun hashCode(): Int {
        return programId
    }

    companion object {
        private const val TAG = "GlProgram"

        private val cachePool = object : ThreadLocal<ArrayMap<ProgramShader, GlProgram>>() {
            override fun initialValue(): ArrayMap<ProgramShader, GlProgram> {
                return ArrayMap()
            }
        }

        /**
         * 执行对象创建的调用函数
         */
        private val createFunc = { programShader: ProgramShader ->
            GlProgram(programShader)
        }

        /**
         * 使用唯一键[programShader]，从缓存中获取一个[GlProgram]
         *
         * @param key 生成[GlCanvas]的唯一键
         */
        fun obtain(programShader: ProgramShader): GlProgram {
            return cachePool.get()!!.getOrPut(programShader) { createFunc(programShader) }
        }

        /**
         * 清除缓存池
         */
        fun clean() {
            Log.d(TAG, "clean: is called.")
            cachePool.get()!!.forEach { (_, glProgram) ->
                glProgram.recycle()
            }
            cachePool.remove()
        }

        /**
         * 编译shader
         *
         * @param programId shader程序id
         * @param type shader类型
         * @param glsl shader程序
         */
        private fun addShader(programId: Int, type: Int, glsl: String) {
            val shader = GLES30.glCreateShader(type)
            GLES30.glShaderSource(shader, glsl)
            GLES30.glCompileShader(shader)

            val result = singleInt { GLES30.glGetShaderiv(shader, GLES30.GL_COMPILE_STATUS, it, 0) }
            GlUtil.checkGlException(result == GLES30.GL_TRUE, GLES30.glGetShaderInfoLog(shader) + ", source: " + glsl)

            GLES30.glAttachShader(programId, shader)
            GLES30.glDeleteShader(shader)
            GlUtil.checkGlError()
        }
    }
}

data class ShaderParamKey(
    val type: Int,
    val name: String,
) {
    companion object {
        const val TYPE_ATTR = 0
        const val TYPE_UNIFORM = 1

        /**
         *  Returns the attribute at the given index in the program.
         */
        fun createAttr(programId: Int, index: Int): ShaderParamKey {
            val length = singleInt { GLES30.glGetProgramiv(programId, GLES30.GL_ACTIVE_ATTRIBUTE_MAX_LENGTH, it, 0) }

            val nameBytes = ByteArray(length)
            GLES30.glGetActiveAttrib(
                programId, index, length, IntArray(1), 0, IntArray(1), 0, IntArray(1), 0, nameBytes, 0
            )
            val name = String(nameBytes, 0, getCStringLength(nameBytes))

            return ShaderParamKey(TYPE_ATTR, name)
        }


        /**
         * Returns the uniform at the given index in the program.
         */
        fun createUniform(programId: Int, index: Int): ShaderParamKey {
            val length = singleInt { GLES30.glGetProgramiv(programId, GLES30.GL_ACTIVE_UNIFORM_MAX_LENGTH, it, 0) }

            val nameBytes = ByteArray(length)
            GLES30.glGetActiveUniform(programId, index, length, IntArray(1), 0, IntArray(1), 0, IntArray(1), 0, nameBytes, 0)

            val name = String(nameBytes, 0, getCStringLength(nameBytes))

            return ShaderParamKey(TYPE_UNIFORM, name)
        }

        private fun getCStringLength(cString: ByteArray): Int {
            for (i in cString.indices) {
                if (cString[i] == '\u0000'.code.toByte()) {
                    return i
                }
            }
            return cString.size
        }
    }
}

abstract class ShaderParam(protected val name: String) {
    internal abstract val key: ShaderParamKey
    internal var handle: Int = GLES30.GL_INVALID_INDEX

    /**
     * 加载OpenGL对应的句柄[handle]
     *
     * @param programId OpenGL程序Id
     */
    internal abstract fun loadHandle(programId: Int)
}

open class AttributeShaderParam(name: String, private val size: Int, private val location: Int? = null) : ShaderParam(name) {
    override val key: ShaderParamKey = ShaderParamKey(ShaderParamKey.TYPE_ATTR, name)
    override fun loadHandle(programId: Int) {
        handle = GLES30.glGetAttribLocation(programId, name)
        if (handle == GLES30.GL_INVALID_INDEX) {
            Log.e(TAG, "loadHandle: name: $name, glGetAttribLocation return -1.")
            if (location != null) {
                handle = location
            }
        }
    }

    fun setValue(values: FloatArray, stride: Int, offset: Int) {
        GLES30.glBindBuffer(GLES30.GL_ARRAY_BUFFER, 0)
        val floatBuffer = values.toBuffer().position(offset)
        GLES30.glEnableVertexAttribArray(handle)
        GLES30.glVertexAttribPointer(handle, size, GLES30.GL_FLOAT, false, stride * Float.SIZE_BYTES, floatBuffer)
        GlUtil.checkGlError()
    }

    fun setValue(stride: Int, offset: Int) {
        GLES30.glEnableVertexAttribArray(handle)
        GLES30.glVertexAttribPointer(handle, size, GLES30.GL_FLOAT, false, stride * Float.SIZE_BYTES, offset * Float.SIZE_BYTES)
        GlUtil.checkGlError()
    }

    companion object {
        private const val TAG = "AttributeShaderParam"
    }
}

open class UniformShaderParam(name: String) : ShaderParam(name) {
    override val key: ShaderParamKey = ShaderParamKey(ShaderParamKey.TYPE_UNIFORM, name)
    override fun loadHandle(programId: Int) {
        handle = GLES30.glGetUniformLocation(programId, name)
    }

}

class Uniform1iShaderParam(name: String) : UniformShaderParam(name) {
    fun setValue(value: Int) {
        GLES30.glUniform1i(handle, value)
        GlUtil.checkGlError()
    }
}

class Uniform1fShaderParam(name: String) : UniformShaderParam(name) {
    fun setValue(value: Float) {
        GLES30.glUniform1f(handle, value)
        GlUtil.checkGlError()
    }
}

class Uniform1fvShaderParam(name: String) : UniformShaderParam(name) {
    fun setValue(values: FloatArray, count: Int = values.size, offset: Int = 0) {
        GLES30.glUniform1fv(handle, count, values, offset)
        GlUtil.checkGlError()
    }
}

class Uniform3fShaderParam(name: String) : UniformShaderParam(name) {
    fun setValue(x: Float, y: Float, z: Float) {
        GLES30.glUniform3f(handle, x, y, z)
        GlUtil.checkGlError()
    }
}

class Uniform3fvShaderParam(name: String) : UniformShaderParam(name) {
    fun setValue(values: FloatArray, count: Int = values.size / NUM_3, offset: Int = 0) {
        GLES30.glUniform3fv(handle, count, values, offset)
        GlUtil.checkGlError()
    }

    companion object {
        private const val NUM_3 = 3
    }
}

class Uniform4fvShaderParam(name: String) : UniformShaderParam(name) {
    fun setValue(values: FloatArray, count: Int = values.size / NUM_4, offset: Int = 0) {
        GLES30.glUniform4fv(handle, count, values, offset)
        GlUtil.checkGlError()
    }

    companion object {
        private const val NUM_4 = 4
    }
}

class UniformMat3fvShaderParam(name: String) : UniformShaderParam(name) {
    fun setValue(value: FloatArray, count: Int = value.size / NUM_9, transpose: Boolean = false, offset: Int = 0) {
        GLES30.glUniformMatrix3fv(handle, count, transpose, value, offset)
        GlUtil.checkGlError()
    }

    companion object {
        private const val NUM_9 = 9
    }
}


class UniformMat4fvShaderParam(name: String) : UniformShaderParam(name) {
    fun setValue(value: FloatArray, count: Int = value.size / NUM_16, transpose: Boolean = false, offset: Int = 0) {
        GLES30.glUniformMatrix4fv(handle, count, transpose, value, offset)
        GlUtil.checkGlError()
    }

    companion object {
        private const val NUM_16 = 16
    }
}

/**
 * 数据缓冲绑定器
 */
abstract class BufferBinder : IRecyclable {
    /**
     * 执行缓冲器的安装
     */
    abstract fun install()

    /**
     * 执行缓冲器的绑定，在使用此缓冲中的数据
     */
    abstract fun bind()

    /**
     * 执行缓冲器的解绑
     */
    abstract fun unbind()

    /**
     * 使用缓冲器，执行后可执行attribute的设置
     */
    abstract fun use()

    /**
     * 取消使用缓冲器
     */
    abstract fun unUse()
}

class BufferDataBinder(private val vboData: Buffer) : BufferBinder() {
    var vaoId: Int = GLES30.GL_NONE
        private set
    var vboId: Int = GLES30.GL_NONE
        private set

    private var isInstalled = false

    override fun install() {
        if (isInstalled) return
        isInstalled = true
        vaoId = singleInt { GLES30.glGenVertexArrays(1, it, 0) }
        vboId = singleInt { GLES30.glGenBuffers(1, it, 0) }
        GlUtil.checkGlError()

        // VAO
        GLES30.glBindVertexArray(vaoId)
        GlUtil.checkGlError()

        // VBO
        GLES30.glBindBuffer(GLES30.GL_ARRAY_BUFFER, vboId)
        vboData.position(0)
        GLES30.glBufferData(GLES30.GL_ARRAY_BUFFER, vboData.capacity() * Float.SIZE_BYTES, vboData, GLES30.GL_STATIC_DRAW)
        GlUtil.checkGlError()

        unUse()
    }

    override fun use() {
        GLES30.glBindVertexArray(vaoId)
        GlUtil.checkGlError()
        GLES30.glBindBuffer(GLES30.GL_ARRAY_BUFFER, vboId)
        GlUtil.checkGlError()
    }

    override fun unUse() {
        //解绑 VBO
        GLES30.glBindBuffer(GLES30.GL_ARRAY_BUFFER, GLES30.GL_NONE)
        //解绑 VAO
        GLES30.glBindVertexArray(GLES30.GL_NONE)

        GlUtil.checkGlError()
    }

    override fun bind() {
        GLES30.glBindVertexArray(vaoId)
        GlUtil.checkGlError()
    }

    override fun unbind() {
        GLES30.glBindVertexArray(GLES30.GL_NONE)
        GlUtil.checkGlError()
    }

    override fun recycle() {
        GLES30.glDeleteVertexArrays(1, intArrayOf(vaoId), 0)
        GLES30.glDeleteBuffers(1, intArrayOf(vboId), 0)
        isInstalled = false
    }

    companion object {
        private const val TAG = "BufferDataBinder"
        private val cachePool = object : ThreadLocal<ArrayMap<FloatBuffer, BufferDataBinder>>() {
            override fun initialValue(): ArrayMap<FloatBuffer, BufferDataBinder> {
                return ArrayMap()
            }
        }

        /**
         * 执行对象创建的调用函数
         */
        private val createFunc = { buffer: FloatBuffer ->
            BufferDataBinder(buffer)
        }

        /**
         * 使用唯一键[FloatBuffer]，从缓存中获取一个[BufferDataBinder]
         *
         * @param key 生成[GlCanvas]的唯一键
         */
        fun obtain(buffer: FloatBuffer): BufferDataBinder {
            return cachePool.get()!!.getOrPut(buffer) { createFunc(buffer) }
        }

        /**
         * 清除缓存池
         */
        fun clean() {
            Log.d(TAG, "clean: is called.")
            cachePool.get()!!.forEach { (_, glProgram) ->
                glProgram.recycle()
            }
            cachePool.remove()
        }
    }
}

inline fun singleInt(func: (IntArray) -> Unit): Int {
    return IntArray(1).also(func)[0]
}

inline fun doubleInt(func: (IntArray) -> Unit): Pair<Int, Int> {
    return IntArray(2).also(func).let { Pair(it[0], it[1]) }
}
