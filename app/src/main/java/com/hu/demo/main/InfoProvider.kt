package com.hu.demo.main

import android.content.ContentProvider
import android.content.ContentValues
import android.database.Cursor
import android.net.Uri
import android.os.Bundle
import android.util.Log
import androidx.work.Data
import androidx.work.ExistingWorkPolicy
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.OutOfQuotaPolicy
import androidx.work.WorkManager
import com.hu.demo.main.works.uhdr.BaseWorker.Companion.COLOR_SPACE
import com.hu.demo.main.works.uhdr.BaseWorker.Companion.INPUT_DIR
import com.hu.demo.main.works.uhdr.BaseWorker.Companion.INPUT_FILE
import com.hu.demo.main.works.uhdr.BaseWorker.Companion.MAX_SLICE_COUNT
import com.hu.demo.main.works.uhdr.BaseWorker.Companion.OUTPUT_COUNT
import com.hu.demo.main.works.uhdr.BaseWorker.Companion.OUTPUT_DIR
import com.hu.demo.main.works.uhdr.BaseWorker.Companion.OUTPUT_FILE
import com.hu.demo.main.works.uhdr.UhdrCombineWorker
import com.hu.demo.main.works.uhdr.UhdrSpiltWorker
import com.hu.demo.main.works.uhdr.UhdrToHdrWorker
import com.hu.demo.main.works.uhdr.VideoCombineWorker
import com.hu.demo.main.works.uhdr.VideoSplitWorker

class InfoProvider : ContentProvider() {

    override fun delete(uri: Uri, selection: String?, selectionArgs: Array<String>?): Int {
        return 0
    }

    override fun getType(uri: Uri): String? {
        return null
    }

    override fun insert(uri: Uri, values: ContentValues?): Uri? {
        return null
    }

    override fun onCreate(): Boolean {
        return true
    }

    override fun query(uri: Uri, projection: Array<String>?, selection: String?, selectionArgs: Array<String>?, sortOrder: String?): Cursor? {
        return null
    }

    override fun update(uri: Uri, values: ContentValues?, selection: String?, selectionArgs: Array<String>?): Int {
        return 0
    }

    override fun call(method: String, arg: String?, extras: Bundle?): Bundle {
        val rtn = Bundle()
        if (method == METHOD_SPLIT_UHDR) {
            callSplitUhdr(extras, rtn)
        } else if (method == METHOD_COMBINE_UHDR) {
            callCombineUhdr(extras, rtn)
        } else if (method == METHOD_COMBINE_TO_HDR) {
            callCombineToHdr(extras, rtn)
        } else if (method == METHOD_SPLIT_VIDEO) {
            callSplitVideo(extras, rtn)
        } else if (method == METHOD_COMBINE_VIDEO) {
            callCombineVideo(extras, rtn)
        }
        return rtn
    }

    private fun callCombineVideo(extras: Bundle?, rtn: Bundle) {
        val context = context ?: return
        if (extras == null) {
            rtn.putBoolean(KEY_RESULT, false)
            rtn.putString(KEY_MSG, "参数为null")
            return
        }
        val inputData = Data.Builder()
            .putString(INPUT_DIR, extras.getString(INPUT_DIR))
            .putString(OUTPUT_FILE, extras.getString(OUTPUT_FILE))
            .build()
        val workRequest = OneTimeWorkRequestBuilder<VideoCombineWorker>()
            .setExpedited(OutOfQuotaPolicy.RUN_AS_NON_EXPEDITED_WORK_REQUEST)
            .setInputData(inputData)
            .build()
        WorkManager.getInstance(context).enqueueUniqueWork("VideoCombineWorker", ExistingWorkPolicy.REPLACE, workRequest).apply {
            result.get()
        }

        while (WorkManager.getInstance(context).getWorkInfoById(workRequest.id).get()?.state?.isFinished != true) {
            Log.d(TAG, "call: sleep 300")
            Thread.sleep(300)
        }

        val count = WorkManager.getInstance(context).getWorkInfoById(workRequest.id).get()?.outputData?.getInt(OUTPUT_COUNT, 0) ?: 0

        rtn.putBoolean(KEY_RESULT, true)
        rtn.putInt(KEY_COUNT, count)
        return
    }

    private fun callSplitVideo(extras: Bundle?, rtn: Bundle) {
        val context = context ?: return
        if (extras == null) {
            rtn.putBoolean(KEY_RESULT, false)
            rtn.putString(KEY_MSG, "参数为null")
            return
        }
        val inputData = Data.Builder()
            .putString(INPUT_FILE, extras.getString(INPUT_FILE))
            .putString(OUTPUT_DIR, extras.getString(OUTPUT_DIR))
            .putInt(MAX_SLICE_COUNT, extras.getInt(MAX_SLICE_COUNT, 3))
            .build()
        val workRequest = OneTimeWorkRequestBuilder<VideoSplitWorker>()
            .setExpedited(OutOfQuotaPolicy.RUN_AS_NON_EXPEDITED_WORK_REQUEST)
            .setInputData(inputData)
            .build()
        WorkManager.getInstance(context).enqueueUniqueWork("SplitVideoWorker", ExistingWorkPolicy.REPLACE, workRequest).apply {
            result.get()
        }

        while (WorkManager.getInstance(context).getWorkInfoById(workRequest.id).get()?.state?.isFinished != true) {
            Log.d(TAG, "call: sleep 300")
            Thread.sleep(300)
        }

        val count = WorkManager.getInstance(context).getWorkInfoById(workRequest.id).get()?.outputData?.getInt(OUTPUT_COUNT, 0) ?: 0

        rtn.putBoolean(KEY_RESULT, true)
        rtn.putInt(KEY_COUNT, count)
        return
    }

    private fun callCombineToHdr(extras: Bundle?, rtn: Bundle) {
        val context = context ?: return
        if (extras == null) {
            rtn.putBoolean(KEY_RESULT, false)
            rtn.putString(KEY_MSG, "参数为null")
            return
        }
        val inputData = Data.Builder()
            .putString(INPUT_DIR, extras.getString(INPUT_DIR))
            .putString(OUTPUT_DIR, extras.getString(OUTPUT_DIR))
            .putString(COLOR_SPACE, extras.getString(COLOR_SPACE))
            .build()
        val workRequest = OneTimeWorkRequestBuilder<UhdrToHdrWorker>()
            .setExpedited(OutOfQuotaPolicy.RUN_AS_NON_EXPEDITED_WORK_REQUEST)
            .setInputData(inputData)
            .build()
        WorkManager.getInstance(context).enqueueUniqueWork("UhdrToHdrWorker", ExistingWorkPolicy.REPLACE, workRequest).apply {
            result.get()
        }

        while (WorkManager.getInstance(context).getWorkInfoById(workRequest.id).get()?.state?.isFinished != true) {
            Log.d(TAG, "call: sleep 300")
            Thread.sleep(300)
        }

        val count = WorkManager.getInstance(context).getWorkInfoById(workRequest.id).get()?.outputData?.getInt(OUTPUT_COUNT, 0) ?: 0

        rtn.putBoolean(KEY_RESULT, true)
        rtn.putInt(KEY_COUNT, count)
        return
    }

    private fun callCombineUhdr(extras: Bundle?, rtn: Bundle) {
        val context = context ?: return
        if (extras == null) {
            rtn.putBoolean(KEY_RESULT, false)
            rtn.putString(KEY_MSG, "参数为null")
            return
        }
        val inputData = Data.Builder()
            .putString(INPUT_DIR, extras.getString(INPUT_DIR))
            .putString(OUTPUT_DIR, extras.getString(OUTPUT_DIR))
            .build()
        val workRequest = OneTimeWorkRequestBuilder<UhdrCombineWorker>()
            .setExpedited(OutOfQuotaPolicy.RUN_AS_NON_EXPEDITED_WORK_REQUEST)
            .setInputData(inputData)
            .build()
        WorkManager.getInstance(context).enqueueUniqueWork("UhdrCombineWorker", ExistingWorkPolicy.REPLACE, workRequest).apply {
            result.get()
        }

        while (WorkManager.getInstance(context).getWorkInfoById(workRequest.id).get()?.state?.isFinished != true) {
            Log.d(TAG, "call: sleep 300")
            Thread.sleep(300)
        }

        val count = WorkManager.getInstance(context).getWorkInfoById(workRequest.id).get()?.outputData?.getInt(OUTPUT_COUNT, 0) ?: 0

        rtn.putBoolean(KEY_RESULT, true)
        rtn.putInt(KEY_COUNT, count)
        return
    }

    private fun callSplitUhdr(extras: Bundle?, rtn: Bundle) {
        val context = context ?: return
        if (extras == null) {
            rtn.putBoolean(KEY_RESULT, false)
            rtn.putString(KEY_MSG, "参数为null")
            return
        }
        val inputData = Data.Builder()
            .putString(INPUT_DIR, extras.getString(INPUT_DIR))
            .putString(OUTPUT_DIR, extras.getString(OUTPUT_DIR))
            .build()
        val workRequest = OneTimeWorkRequestBuilder<UhdrSpiltWorker>()
            .setExpedited(OutOfQuotaPolicy.RUN_AS_NON_EXPEDITED_WORK_REQUEST)
            .setInputData(inputData)
            .build()
        WorkManager.getInstance(context).enqueueUniqueWork("UhdrSpiltWorker", ExistingWorkPolicy.REPLACE, workRequest).apply {
            result.get()
        }

        while (WorkManager.getInstance(context).getWorkInfoById(workRequest.id).get()?.state?.isFinished != true) {
            Log.d(TAG, "call: sleep 300")
            Thread.sleep(300)
        }

        val count = WorkManager.getInstance(context).getWorkInfoById(workRequest.id).get()?.outputData?.getInt(OUTPUT_COUNT, 0) ?: 0

        rtn.putBoolean(KEY_RESULT, true)
        rtn.putInt(KEY_COUNT, count)
        return
    }

    companion object {
        private const val TAG = "InfoProvider"
        private const val METHOD_SPLIT_UHDR = "splitUhdr"
        private const val METHOD_COMBINE_UHDR = "combineUhdr"
        private const val METHOD_COMBINE_TO_HDR = "combineToHdr"
        private const val METHOD_SPLIT_VIDEO = "splitVideo"
        private const val METHOD_COMBINE_VIDEO = "combineVideo"
        private const val KEY_RESULT = "result"
        private const val KEY_COUNT = "count"
        private const val KEY_MSG = "msg"
    }
}