package com.hu.demo.main.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.IdRes
import androidx.annotation.LayoutRes
import androidx.recyclerview.widget.RecyclerView
import com.hu.demo.main.works.recyclerview.view.IEditable

abstract class BaseVH<DATA : BaseData>(
    parent: ViewGroup,
    @LayoutRes layoutId: Int,
) : RecyclerView.ViewHolder(LayoutInflater.from(parent.context).inflate(layoutId, parent, false)), BindCallback<DATA>, IEditable {
    override var isEditMode: Boolean = false
    fun <T : View?> findViewById(@IdRes id: Int): T {
        return itemView.findViewById(id)
    }
}