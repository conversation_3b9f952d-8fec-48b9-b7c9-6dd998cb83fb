/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - BitmapTexture.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/08/24
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * huca<PERSON><PERSON>@Apps.Gallery		2024/08/24		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.texture

import android.graphics.Bitmap
import android.opengl.GLES30
import com.hu.demo.main.works.gl.texture.ITexture.Companion.getFormat
import com.hu.demo.main.works.gl.texture.ITexture.Companion.getInternalFormat
import com.hu.demo.main.works.gl.texture.ITexture.Companion.getType
import com.hu.demo.main.works.gl.utils.GlUtil
import com.hu.demo.utils.requireColorSpace
import com.hu.demo.utils.toBuffer

/**
 * [Bitmap]类型的[Texture]，通过传入的[bitmap]实现与GPU纹理的绑定。
 *
 * @param bitmap bitmap
 * @param canRecycle 是否可以在[load]后回收[bitmap]，默认是false
 */
class BitmapTexture(
    bitmap: Bitmap,
    private val canRecycle: Boolean = false,
) : Texture(TexKey(bitmap.width, bitmap.height, texConfig = bitmap.config!!.toTexConfig()), bitmap.requireColorSpace) {
    override val tag: String = TAG

    var bitmap: Bitmap? = bitmap
        private set

    override fun load() {
        if (hasUpload) return
        val bitmap = bitmap ?: return

        realTexture.value.load()
        GLES30.glTexSubImage2D(texTarget, 0, 0, 0, width, height, getFormat(texConfig.getInternalFormat()), texConfig.getType(), bitmap.toBuffer())
        GlUtil.checkGlError()
        GLES30.glFlush()

        hasUpload = true

        if (canRecycle) {
            bitmap.recycle()
            this.bitmap = null
        }
    }

    override fun buildString(): MutableMap<String, Any?> {
        return super.buildString().apply {
            this["canRecycle"] = canRecycle
        }
    }

    private companion object {
        private const val TAG = "BitmapTexture"
    }
}