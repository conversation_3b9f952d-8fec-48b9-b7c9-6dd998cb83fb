package com.hu.demo.main.works.fileparse.box.mp4

import com.hu.demo.main.works.fileparse.box.Box
import com.hu.demo.main.works.fileparse.box.FullBox
import com.hu.demo.main.works.fileparse.box.Tree
import com.hu.demo.utils.ByteOrderedDataInputStream
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter

internal class MovieHeaderBox(parent: Tree?) : FullBox("mvhd", 2, parent) {
    var creationTime: Long? = null
    var modificationTime: Long? = null
    var timeScale: Int? = null
    var duration: Long? = null
    var rate: Int? = null
    var volume: Short? = null
    var matrix: IntArray? = null
    var preDefined: IntArray? = null
    var nextTrackId: Int? = null

    override fun readInner(bis: ByteOrderedDataInputStream) {
        super.readInner(bis)
        val version = version?.toInt()!!
        if (version == 1) {
            creationTime = bis.nReadLong()
            modificationTime = bis.nReadLong()
            timeScale = bis.nReadInt()
            duration = bis.nReadLong()
        } else {
            creationTime = bis.nReadUInt().toLong()
            modificationTime = bis.nReadUInt().toLong()
            timeScale = bis.nReadInt()
            duration = bis.nReadUInt().toLong()
        }
        bis.nSkipFully(2)
        bis.nSkipFully(4 * 2)
        rate = bis.nReadInt()
        volume = bis.nReadShort()
        matrix = bis.nReadInts(9)
        preDefined = bis.nReadInts(6)
        nextTrackId = bis.nReadInt()
    }

    private fun formatUtc(seconds: Long): String {
        // 1904 年 1 月 1 日午夜的 UTC 时间
        val zoneId = ZoneId.of("UTC")
        val startInstant = LocalDateTime.of(1904, 1, 1, 0, 0, 0).atZone(zoneId).toInstant()

        // 将 Instant 转换为 LocalDateTime
        val targetDateTime = LocalDateTime.ofInstant(startInstant.plusSeconds(seconds), zoneId)

        // 定义日期时间格式
        val formatter = DateTimeFormatter.ofPattern("yyyyMMdd-HH:mm:ss")

        // 格式化日期时间
        return targetDateTime.format(formatter)
    }

    override fun fork(parent: Tree): Box {
        return MovieHeaderBox(parent)
    }

    override fun toString(): String {
        return "${super.toString()}, creationTime:${formatUtc(creationTime!!)}, modificationTime=${formatUtc(modificationTime!!)}, " +
                "timeScale=$timeScale, duration=$duration, rate=$rate, volume=$volume, matrix=${matrix?.contentToString()}, " +
                "preDefined=${preDefined?.contentToString()}, \n nextTrackId=$nextTrackId)"
    }

}