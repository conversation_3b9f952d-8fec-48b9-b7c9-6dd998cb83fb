package com.hu.demo.main.works.drawable

import android.graphics.Canvas
import android.graphics.drawable.Drawable
import kotlin.math.PI
import kotlin.math.abs
import kotlin.math.atan2
import kotlin.math.cos
import kotlin.math.hypot
import kotlin.math.max
import kotlin.math.min
import kotlin.math.roundToInt
import kotlin.math.sin

const val INVALID = -1
const val PI_1_2 = 0.5 * PI

enum class DrawType {
    /**
     * 裁剪中间区域
     */
    CENTER_CROP,

    /**
     * 图片不裁剪，将图片缩放到Canvas中间
     */
    CENTER_INSIDE,

    /**
     * 图片在中间
     */
    CENTER
}

/**
 * 将drawable画在[Canvas]上，画的位置为：left:0,top:0,right:[drawW],bottom:[drawH]，图片等比例缩放。
 * 根据[DrawType]设置图片裁剪模式
 *
 * @param drawable 需要画的[Drawable]
 * @param itemWidth 指定画在Canvas的宽度
 * @param itemHeight 指定画在Canvas的高度
 */
fun Canvas.drawDrawable(drawable: Drawable?, drawW: Float, drawH: Float, rotate: Float = 0f, drawType: DrawType = DrawType.CENTER_CROP) {
    drawable ?: return
    save()
    clipRect(0f, 0f, drawW, drawH)
    rotate(rotate, drawW / 2f, drawH / 2f)
    if (drawable.intrinsicWidth != INVALID || drawable.intrinsicHeight != INVALID) {
        var scale = 1.0
        val radian = rotate.toDouble().angleToRadian()
        val drawableW = drawable.intrinsicWidth.toDouble()
        val drawableH = drawable.intrinsicHeight.toDouble()
        val containerW = drawW.toDouble()
        val containerH = drawH.toDouble()
        if (drawType == DrawType.CENTER_CROP) {
            val diagonal = hypot(containerW, containerH)
            val rectAngle = atan2(containerH, containerW)
            val wScale = abs(
                if (radian % PI < PI_1_2) {
                    diagonal * sin(PI_1_2 - rectAngle + radian % PI_1_2) / drawableW
                } else {
                    diagonal * sin(rectAngle + radian % PI_1_2) / drawableW
                }
            )
            val hScale = abs(
                if (radian % PI < PI_1_2) {
                    diagonal * sin(rectAngle + radian % PI_1_2) / drawableH
                } else {
                    diagonal * sin(PI_1_2 - rectAngle + radian % PI_1_2) / drawableH
                }
            )
            scale = max(wScale, hScale)
        } else if (drawType == DrawType.CENTER_INSIDE) {
            val rectAngle = atan2(containerH, containerW)
            val wScale = abs(
                if (radian % PI < PI_1_2) {
                    containerH / sin(rectAngle + radian % PI_1_2) * cos(rectAngle) / drawableW
                } else {
                    containerH / sin(PI_1_2 - rectAngle + radian % PI_1_2) * sin(PI_1_2 - rectAngle) / drawableW
                }
            )
            val hScale = abs(
                if (radian % PI < PI_1_2) {
                    containerW / sin(PI_1_2 - rectAngle + radian % PI_1_2) * cos(PI_1_2 - rectAngle) / drawableH
                } else {
                    containerW / sin(rectAngle + radian % PI_1_2) * sin(rectAngle) / drawableH
                }
            )
            scale = min(wScale, hScale)
        } else if (drawType == DrawType.CENTER) {
            scale = 1.0
        }
        val changeWidth = (drawableW * scale).toFloat()
        val changeHeight = (drawableH * scale).toFloat()
        translate((drawW - changeWidth) / 2, (drawH - changeHeight) / 2)
        drawable.setBounds(0, 0, changeWidth.roundToInt(), changeHeight.roundToInt())
    } else {
        drawable.setBounds(0, 0, drawW.toInt(), drawH.toInt())
    }
    drawable.draw(this)
    restore()
}

fun Double.angleToRadian(): Double {
    return this * PI / 180.0
}