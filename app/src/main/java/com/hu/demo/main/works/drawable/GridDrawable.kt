package com.hu.demo.main.works.drawable

import android.graphics.*
import android.graphics.drawable.Drawable

class GridDrawable(
    private val gridItems: Array<Array<Drawable?>>? = null,
    private val background: Drawable? = null,
    private val edge: Float = 0f,
    private val gap: Float = 0f,
    private val radius: Float = 0f,
    lineWidth: Float = 0f,
    lineColor: Int = Color.TRANSPARENT
) : Drawable() {
    private val linePaint = Paint().apply {
        isAntiAlias = true
        style = Paint.Style.STROKE
        strokeWidth = lineWidth
        color = lineColor
    }
    private var alphaValue: Int = ALPHA_OPAQUE

    override fun draw(canvas: Canvas) {
        canvas.save()
        val path = Path().apply {
            addRoundRect(0f, 0f, bounds.width().toFloat(), bounds.height().toFloat(), radius, radius, Path.Direction.CW)
        }
        canvas.clipPath(path)
        canvas.drawDrawable(background, bounds.width().toFloat(), bounds.height().toFloat())
        drawGrid(canvas)
        canvas.drawPath(path, linePaint)
        canvas.restore()
    }

    private fun drawGrid(canvas: Canvas) {
        gridItems?.also {
            canvas.save()
            val itemHeight = (bounds.height() - edge * 2 - (it.size - 1) * gap) / it.size
            canvas.translate(edge, edge)
            for (row in it) {
                canvas.save()
                val itemWidth = (bounds.width() - edge * 2 - (row.size - 1) * gap) / row.size
                for (col in row) {
                    canvas.drawDrawable(col, itemWidth, itemHeight)
                    canvas.translate(gap + itemWidth, 0f)
                }
                canvas.restore()
                canvas.translate(0f, gap + itemHeight)
            }
            canvas.restore()
        }
    }

    override fun setAlpha(alpha: Int) {
        if (alphaValue != alpha) {
            alphaValue = alpha
            invalidateSelf()
        }
    }

    override fun getAlpha(): Int = alphaValue

    override fun setColorFilter(colorFilter: ColorFilter?) {
        // do nothing
    }

    @Deprecated("Deprecated in Java")
    override fun getOpacity(): Int {
        return when (alphaValue) {
            in (ALPHA_TRANSPARENT + 1) until ALPHA_OPAQUE -> PixelFormat.TRANSLUCENT
            ALPHA_TRANSPARENT -> PixelFormat.TRANSPARENT
            else -> PixelFormat.OPAQUE
        }
    }

    companion object {
        private const val ALPHA_OPAQUE = 0xFF
        private const val ALPHA_TRANSPARENT = 0x00
    }
}