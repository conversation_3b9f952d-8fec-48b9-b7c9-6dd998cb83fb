package com.hu.demo.main.works.sensor

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.OrientationEventListener
import android.view.ViewGroup
import android.widget.CompoundButton
import android.widget.TextView
import androidx.appcompat.widget.SwitchCompat
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.hu.demo.main.R
import com.hu.demo.main.adapter.BaseData
import com.hu.demo.main.adapter.BaseVH
import com.hu.demo.main.adapter.DefaultAdapter
import com.hu.demo.base.ui.BaseActivity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class SensorActivity : BaseActivity(), CompoundButton.OnCheckedChangeListener {
    private var switchLog: SwitchCompat? = null
    private var switchGravity: SwitchCompat? = null
    private var rvRecycler: RecyclerView? = null
    private var logAdapter: LogAdapter? = null
    private var orientationEventListener: OrientationEventListener? = null
    override fun getLayoutId(): Int {
        return R.layout.activity_sensor
    }

    override fun initToolbar() {
        super.initToolbar()
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowTitleEnabled(true)
            setDisplayShowHomeEnabled(false)
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        orientationEventListener = object : OrientationEventListener(this) {
            override fun onOrientationChanged(orientation: Int) {
                lifecycleScope.launch {
                    addLog(LogData("方向： $orientation"))
                }
            }
        }
    }

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        switchLog = findViewById(R.id.switchLog)
        switchGravity = findViewById(R.id.switchGravity)
        rvRecycler = findViewById(R.id.rv_list)
    }

    override fun initEvent(savedInstanceState: Bundle?) {
        super.initEvent(savedInstanceState)
        switchGravity?.setOnCheckedChangeListener(this)
        switchLog?.setOnCheckedChangeListener(this)
        rvRecycler?.apply {
            addItemDecoration(DividerItemDecoration(this@SensorActivity, RecyclerView.VERTICAL))
            adapter = LogAdapter(this).apply {
                resetData(emptyList())
                logAdapter = this
            }
            layoutManager = LinearLayoutManager(this@SensorActivity, RecyclerView.VERTICAL, false)
        }
    }

    override fun onCheckedChanged(buttonView: CompoundButton, isChecked: Boolean) {
        when (buttonView.id) {
            R.id.switchGravity -> {
                if (isChecked) {
                    orientationEventListener?.enable()
                    lifecycleScope.launch {
                        addLog(LogData("开始监听重力传感器"))
                    }
                } else {
                    orientationEventListener?.disable()
                    lifecycleScope.launch {
                        addLog(LogData("结束监听重力传感器"))
                    }
                }
            }

            R.id.switchLog -> {
                if (!isChecked) {
                    lifecycleScope.launch {
                        closeLog()
                    }
                }
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private suspend fun closeLog() = withContext(Dispatchers.Main) {
        logAdapter?.resetData(emptyList())
        logAdapter?.notifyDataSetChanged()
    }

    private suspend fun addLog(logData: LogData) = withContext(Dispatchers.Main) {
        if (switchLog?.isChecked == true) {
            logAdapter?.apply {
                dataList.add(logData)
                notifyItemInserted(dataList.lastIndex)
                rvRecycler?.scrollToPosition(dataList.lastIndex)
            }
        }
    }

    class LogVH(parent: ViewGroup) : BaseVH<LogData>(parent, R.layout.item_sensor_log) {
        private val tvItem: TextView = findViewById(R.id.tv_item)

        override fun bind(extraData: Map<String, Any>, data: LogData, position: Int) {
            tvItem.text = data.name
        }
    }

    data class LogData(val name: String?) : BaseData(TYPE_DEFAULT)

    class LogAdapter(recyclerView: RecyclerView) : DefaultAdapter<BaseData, BaseVH<BaseData>>(recyclerView) {
        @Suppress("UNCHECKED_CAST")
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH<BaseData> {
            return LogVH(parent) as BaseVH<BaseData>
        }

        override fun onBindViewHolder(holder: BaseVH<BaseData>, position: Int) {
            holder.bind(emptyMap(), dataList[position], position)
        }
    }

    companion object {
        private const val TYPE_DEFAULT = 0
    }
}