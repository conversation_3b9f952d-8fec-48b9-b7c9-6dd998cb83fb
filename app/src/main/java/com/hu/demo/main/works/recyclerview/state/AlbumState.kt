package com.hu.demo.main.works.recyclerview.state

import android.view.ViewGroup
import com.hu.demo.main.works.recyclerview.longclick.ItemLongClickAddition
import com.hu.demo.main.works.recyclerview.slidingselect.SlidingSelectProcessor
import com.hu.demo.main.works.recyclerview.view.ViewTouchAdapter

class AlbumState<V : ViewGroup>(touchAdapter: ViewTouchAdapter<V>) : State<V>(touchAdapter) {
    private val longClickHelper = ItemLongClickAddition<V>()
    private val slidingSelectHelper = SlidingSelectProcessor<V>(null)

    override fun toNormalModel() {
        longClickHelper.attachToView(touchAdapter)
        slidingSelectHelper.attachToView(touchAdapter)
    }

    override fun toEditMode() {
        slidingSelectHelper.attachToView(touchAdapter)
        longClickHelper.attachToView(touchAdapter)
    }
}