package com.hu.demo.main.works.fileparse.box.jpg

import com.hu.demo.main.works.fileparse.box.Tree
import com.hu.demo.utils.ByteOrderedDataInputStream

internal class XmpSection(parent: Tree) : IdentifierSection(parent) {
    private val sb = StringBuilder()

    override fun read(bis: ByteOrderedDataInputStream) {
        super.read(bis)

        // 因为size没有包含marker，需要加上
        val contentSize = allSize - alreadyReadSize.toInt()

        val text = bis.nReadBytes(contentSize).decodeToString()
        sb.append(text)
    }

    override fun toString(): String {
        return "${super.toString()} XMP\n$sb"
    }
}
