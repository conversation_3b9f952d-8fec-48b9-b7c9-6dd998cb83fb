/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - IItemTouchCallback.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2022/01/14
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery		2022/01/14		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.recyclerview.view

import android.graphics.Canvas
import android.view.MotionEvent
import android.view.ViewGroup

interface IItemTouchProcessor<V : ViewGroup> {
    fun onInterceptTouchEvent(e: MotionEvent): Boolean
    fun onTouchEvent(e: MotionEvent)
    fun onRequestDisallowInterceptTouchEvent(disallowIntercept: Boolean)
    fun onDraw(c: Canvas, view: V)
}