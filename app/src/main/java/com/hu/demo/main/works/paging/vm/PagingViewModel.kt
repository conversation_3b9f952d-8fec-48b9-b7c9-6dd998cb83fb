package com.hu.demo.main.works.paging.vm

import android.database.ContentObserver
import android.net.Uri
import android.os.Handler
import android.os.Looper
import android.provider.MediaStore
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import androidx.paging.cachedIn
import com.hu.demo.base.app.App
import com.hu.demo.main.works.paging.MediaPagingSource
import com.hu.demo.main.works.paging.data.MediaData
import kotlinx.coroutines.flow.Flow

class PagingViewModel : ViewModel() {
    private val contentObserver = object : ContentObserver(Handler(Looper.getMainLooper())) {
        override fun onChange(selfChange: Boolean, uri: Uri?) {
            invalidateCallback?.invoke()
        }
    }
    var invalidateCallback: (() -> Unit)? = null

    init {
        URIS.forEach {
            App.app.contentResolver.registerContentObserver(it, true, contentObserver)
        }
    }

    val items: Flow<PagingData<MediaData>> = Pager(
        config = PagingConfig(pageSize = ITEMS_PER_PAGE, enablePlaceholders = false),
        pagingSourceFactory = { MediaPagingSource(App.app.contentResolver) }
    ).flow.cachedIn(viewModelScope)

    override fun onCleared() {
        super.onCleared()
        App.app.contentResolver.unregisterContentObserver(contentObserver)
    }

    companion object {
        private const val ITEMS_PER_PAGE = 50

        private val URIS = arrayOf(
            MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
            MediaStore.Video.Media.EXTERNAL_CONTENT_URI,
        )
    }
}