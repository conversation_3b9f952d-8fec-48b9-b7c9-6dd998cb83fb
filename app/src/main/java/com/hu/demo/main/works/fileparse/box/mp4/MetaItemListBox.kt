package com.hu.demo.main.works.fileparse.box.mp4

import com.hu.demo.main.works.fileparse.IRead
import com.hu.demo.main.works.fileparse.box.Box
import com.hu.demo.main.works.fileparse.box.Tree
import com.hu.demo.utils.ByteOrderedDataInputStream
import com.hu.demo.utils.beautyToString
import com.hu.demo.utils.contentHexString

internal class MetaItemListBox(parent: Tree) : Box("ilst", 3, parent) {
    var entries: Array<Item>? = null

    override fun readInner(bis: ByteOrderedDataInputStream) {
        super.readInner(bis)
        val entryCount = (parent!!.children.find {
            (it as Box).type == "keys"
        } as MetaItemKeysBox).entryCount!!
        entries = Array(entryCount) {
            Item().apply { read(bis) }
        }
    }

    override fun fork(parent: Tree): Box {
        return MetaItemListBox(parent)
    }

    override fun toString(): String {
        return "${super.toString()}, entries=${entries?.beautyToString()})"
    }

    inner class Item : IRead {
        var length: Int? = null
        var num: Int? = null
        var dataBox: DataBox? = null
        override fun read(bis: ByteOrderedDataInputStream) {
            length = bis.nReadInt()
            num = bis.nReadInt()
            dataBox = DataBox(this@MetaItemListBox).apply { read(bis) }
        }

        override fun toString(): String {
            return "Item(length:$length, num:$num, dataBox:$dataBox)"
        }

        inner class DataBox(parent: Tree) : Box("data", 4, parent) {
            var ltype: Int? = null
            var locale: Int? = null
            var value: String? = null

            override fun readInner(bis: ByteOrderedDataInputStream) {
                super.readInner(bis)
                ltype = bis.nReadInt()
                locale = bis.nReadInt()
                value = bis.nReadBytes(remainSize.toInt()).run {
                    if (ltype == 1) {
                        decodeToString()
                    } else {
                        contentHexString()
                    }
                }
            }

            override fun toString(): String {
                return "(ltype:$ltype, locale:$locale, value:$value)"
            }
        }
    }


}