package com.hu.demo.main.works.fileparse.box.mp4

import com.hu.demo.main.works.fileparse.box.Box
import com.hu.demo.main.works.fileparse.box.Tree
import com.hu.demo.utils.ByteOrderedDataInputStream

internal class DataBox(parent: Tree) : Box("data", 3, parent) {
    var ltype :Int?=null
    var locale:Int?=null
    var value: String? = null

    override fun readInner(bis: ByteOrderedDataInputStream) {
        super.readInner(bis)
        ltype = bis.nReadInt()
        locale = bis.nReadInt()
        value = bis.nReadBytes(remainSize.toInt()).decodeToString()
    }
}