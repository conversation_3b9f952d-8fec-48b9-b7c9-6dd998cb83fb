/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : RuntimeShaderView.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2025/3/18 9:50
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/3/18  1.0        build this module
 ***********************************************************************/
package com.hu.demo.main.works.uhdr

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Bitmap.Config
import android.graphics.BitmapShader
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.ColorFilter
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.PixelFormat
import android.graphics.RuntimeShader
import android.graphics.Shader
import android.graphics.drawable.Drawable
import android.os.Build
import androidx.annotation.RequiresApi
import com.hu.demo.base.app.App
import kotlin.math.ln


@RequiresApi(Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
class DrawShaderDrawable(private val context: Context, private val bitmap: Bitmap) : Drawable() {
    private var shader: RuntimeShader? = null
    private val bitmapShader = BitmapShader(bitmap, Shader.TileMode.REPEAT, Shader.TileMode.REPEAT)
    private val matrix = Matrix()

    val gainmap = bitmap.gainmap

    private val paint = Paint().apply {
        style = Paint.Style.FILL
        color = Color.WHITE
    }

    override fun draw(canvas: Canvas) {
        shader = RuntimeShader(App.app.assets.open("agsl/fs_uhdr.agsl").readBytes().decodeToString())

        bitmap.gainmap = null
        gainmap ?: return
        shader?.setInputBuffer("uTex", bitmapShader)
        shader?.setFloatUniform("uTexSize", floatArrayOf(bitmap.width.toFloat(), bitmap.height.toFloat()))
        val gainmapShader = BitmapShader(gainmap.gainmapContents, Shader.TileMode.REPEAT, Shader.TileMode.REPEAT)
        shader?.setInputBuffer("uGainmap", gainmapShader)
        shader?.setFloatUniform("uGainmapSize", floatArrayOf(gainmap.gainmapContents.width.toFloat(), gainmap.gainmapContents.height.toFloat()))

        shader?.setFloatUniform("uResolution", floatArrayOf(bounds.width().toFloat(), bounds.height().toFloat()))

        shader?.setFloatUniform("uLogRatioMin", ln(gainmap.ratioMin[0]), ln(gainmap.ratioMin[1]), ln(gainmap.ratioMin[2]))
        shader?.setFloatUniform("uLogRatioMax", ln(gainmap.ratioMax[0]), ln(gainmap.ratioMax[1]), ln(gainmap.ratioMax[2]))
        shader?.setFloatUniform("uGainmapGamma", gainmap.gamma)
        shader?.setFloatUniform("uEpsilonSdr", gainmap.epsilonSdr)
        shader?.setFloatUniform("uEpsilonHdr", gainmap.epsilonHdr)

        shader?.setFloatUniform("uLogDeviceHdrSdrRatio", ln(context.display.hdrSdrRatio))

        shader?.setFloatUniform("uLogDisplaySdrRatio", ln(gainmap.minDisplayRatioForHdrTransition))
        shader?.setFloatUniform("uLogDisplayHdrRatio", ln(gainmap.displayRatioForFullHdr))

        shader?.setIntUniform("uSingleChannel", if (gainmap.gainmapContents.config == Config.ALPHA_8) 1 else 0)
        shader?.setIntUniform("uUseCombineUhdr", 1)

        paint.shader = shader
        canvas.drawRect(bounds, paint)
    }

    override fun setAlpha(alpha: Int) = Unit

    override fun setColorFilter(filter: ColorFilter?) = Unit

    @Deprecated("Deprecated in Java")
    override fun getOpacity(): Int = PixelFormat.TRANSPARENT

}