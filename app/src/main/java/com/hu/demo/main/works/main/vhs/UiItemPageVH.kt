package com.hu.demo.main.works.main.vhs

import android.content.Intent
import android.view.ViewGroup
import android.widget.TextView
import com.hu.demo.base.app.App
import com.hu.demo.main.R
import com.hu.demo.main.adapter.BaseVH
import com.hu.demo.main.works.main.datas.UiItemData

class UiItemPageVH(parent: ViewGroup) : BaseVH<UiItemData>(parent, R.layout.item_ka) {
    private val tvTitle: TextView = findViewById(R.id.tv_item_title)
    override fun bind(extraData: Map<String, Any>, data: UiItemData, position: Int) {
        tvTitle.text = data.title
        itemView.setOnClickListener {
            itemView.context.startActivity(
                Intent(Intent.ACTION_VIEW, data.uri).setPackage(App.app.packageName)
            )
        }
    }
}