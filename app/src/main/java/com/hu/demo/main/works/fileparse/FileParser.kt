package com.hu.demo.main.works.fileparse

import android.system.Os
import android.system.OsConstants
import java.io.FileDescriptor

object FileParser {
    private val realParsers = listOf(JpgFileParser(), HeifFileParser(), Mp4FileParser())
    private val MAX_CHECK_SIZE = 100
    fun getParser(fd: FileDescriptor): IFileParser? {
        Os.lseek(fd, 0, OsConstants.SEEK_SET)
        val fis = FdSource(fd)
        return realParsers.firstOrNull {
            it.canParse(fis)
        }
    }
}