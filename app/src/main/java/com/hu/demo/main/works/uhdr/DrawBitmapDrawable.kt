package com.hu.demo.main.works.uhdr

import android.graphics.*
import android.graphics.drawable.Drawable
import androidx.core.graphics.toRectF

class DrawBitmapDrawable(private val bitmap: Bitmap) : Drawable() {
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val matrix = Matrix()

    override fun draw(canvas: Canvas) {
        matrix.setRectToRect(
            RectF(0f, 0f, bitmap.width.toFloat(), bitmap.height.toFloat()),
            bounds.toRectF(),
            Matrix.ScaleToFit.CENTER
        )
        canvas.drawBitmap(bitmap, matrix, paint)
    }

    override fun setAlpha(alpha: Int) = Unit

    override fun setColorFilter(filter: ColorFilter?) = Unit

    @Deprecated("Deprecated in Java")
    override fun getOpacity(): Int = PixelFormat.TRANSPARENT
}