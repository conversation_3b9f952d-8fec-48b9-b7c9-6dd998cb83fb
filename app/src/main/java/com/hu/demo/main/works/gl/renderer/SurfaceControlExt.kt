/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - SurfaceControlExt.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/10/16
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery		2024/10/16		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.renderer

import android.view.SurfaceControl
import android.view.SurfaceControl.Transaction
import androidx.graphics.surface.SurfaceControlCompat

fun SurfaceControlCompat.Transaction.sysTransaction(): Transaction {
    val mImplField = javaClass.getDeclaredField("mImpl")
    mImplField.isAccessible = true
    val mImpl = mImplField.get(this)

    val transactionField = mImpl.javaClass.getDeclaredField("mTransaction")
    transactionField.isAccessible = true
    return transactionField.get(mImpl) as Transaction
}

fun SurfaceControlCompat.sysSurfaceControl(): SurfaceControl {
    val scImplField = javaClass.getDeclaredField("scImpl")
    scImplField.isAccessible = true
    val scImpl = scImplField.get(this)

    val surfaceControlField = scImpl.javaClass.getDeclaredField("surfaceControl")
    surfaceControlField.isAccessible = true
    return surfaceControlField.get(scImpl) as SurfaceControl
}