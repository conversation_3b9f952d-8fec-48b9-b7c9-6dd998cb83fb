package com.hu.demo.main.works.paging

import android.os.Bundle
import android.util.Log
import android.view.ViewGroup
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearSnapHelper
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.hu.demo.main.R
import com.hu.demo.base.ui.BaseActivity
import com.hu.demo.main.works.paging.ui.GridItemDecoration
import com.hu.demo.main.works.paging.ui.MediaAdapter
import com.hu.demo.main.works.paging.vh.PagingMediaViewHolder
import com.hu.demo.main.works.paging.vm.PagingViewModel
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

class PagingActivity : BaseActivity() {
    private var navView: BottomNavigationView? = null
    private var rvList: RecyclerView? = null
    override fun getLayoutId(): Int = R.layout.activity_paging

    private val viewModel by viewModels<PagingViewModel>()

    override fun initToolbar() {
        super.initToolbar()
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowTitleEnabled(true)
            setDisplayShowHomeEnabled(false)
        }
    }

    override fun initView(savedInstanceState: Bundle?) {
        rvList = findViewById(R.id.rv_list)
    }

    override fun initEvent(savedInstanceState: Bundle?) {
        super.initEvent(savedInstanceState)
        rvList?.also {
            it.layoutManager = GridLayoutManager(it.context, 4)
            LinearSnapHelper().attachToRecyclerView(it)
            it.addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    Log.d(TAG, "onScrolled: $dy")
                }
            })
            it.adapter = object : MediaAdapter() {
                override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
                    return PagingMediaViewHolder.create(parent)
                }
            }.apply {
                lifecycleScope.launch {
                    viewModel.items.collectLatest(this@apply::submitData)
                }
                viewModel.invalidateCallback = {
                    refresh()
                }
            }
            it.addItemDecoration(GridItemDecoration())

            it.addRecyclerListener { vh ->
                Log.d(TAG, "onCreateView: ${vh.layoutPosition}")
            }
        }
    }

    companion object {
        private const val TAG = "PagingActivity"
    }
}