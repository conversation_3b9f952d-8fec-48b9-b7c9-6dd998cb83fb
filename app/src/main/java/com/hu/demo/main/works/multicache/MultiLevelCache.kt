package com.hu.demo.main.works.multicache

import androidx.annotation.WorkerThread
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

class MultiLevelCache<K, V>(
    private val caches: List<ICache<K, V>>,
    private val scope: CoroutineScope,
    private val defOnChange: ((K, V) -> Unit)? = null
) : List<ICache<K, V>> by caches {

    fun getValue(k: K, maxLevel: Int = caches.size - 1, @WorkerThread onChange: ((K, V) -> Unit)? = null): V? {
        for ((index, cache) in caches.withIndex()) {
            if (index <= maxLevel) {
                cache[k]?.apply {
                    return this
                } ?: continue
            } else break
        }
        getValueAndUpdateAsync(k, maxLevel, onChange)
        return null
    }

    private fun getValueAndUpdateAsync(k: K, maxLevel: Int, onChange: ((K, V) -> Unit)?) {
        if (maxLevel < caches.size - 1) {
            scope.launch {
                for (index in (maxLevel + 1) until caches.size) {
                    caches[index][k]?.apply {
                        defOnChange?.invoke(k, this)
                        onChange?.invoke(k, this)
                        setValue(k, this, caches.size - 1)
                        return@launch
                    }
                }
            }
        }
    }

    fun setValue(k: K, v: V, maxLevel: Int = caches.size - 1) {
        for ((index, cache) in caches.withIndex()) {
            if (index <= maxLevel) {
                cache[k] = v
            } else break
        }
        setValueAsync(k, v, maxLevel)
    }

    private fun setValueAsync(k: K, v: V, maxLevel: Int) {
        if (maxLevel < caches.size - 1) {
            scope.launch {
                for (index in (maxLevel + 1) until caches.size) {
                    caches[index][k] = v
                }
            }
        }
    }
}







