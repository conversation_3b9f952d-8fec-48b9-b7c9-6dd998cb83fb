package com.hu.demo.main.works.fileparse.box.jpg

import com.hu.demo.main.works.fileparse.box.Tree
import com.hu.demo.utils.toHexString

/**
 * JPG专用的Tree基类，提供JPG特有的功能和适配
 */
internal abstract class JpgTree(nodeName: String, level: Int = 0, parent: Tree? = null) : Tree(nodeName, level, parent) {

    override fun toString(): String {
        return "offset: ${(rootTree.alreadyReadSize - alreadyReadSize).toHexString()}"
    }
}
