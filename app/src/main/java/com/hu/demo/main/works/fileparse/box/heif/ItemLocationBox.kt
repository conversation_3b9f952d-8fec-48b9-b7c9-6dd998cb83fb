package com.hu.demo.main.works.fileparse.box.heif

import com.hu.demo.main.works.fileparse.IRead
import com.hu.demo.main.works.fileparse.box.Box
import com.hu.demo.main.works.fileparse.box.FullBox
import com.hu.demo.main.works.fileparse.box.Tree
import com.hu.demo.utils.ByteOrderedDataInputStream
import com.hu.demo.utils.beautyToString
import com.hu.demo.utils.getLow
import com.hu.demo.utils.getSenior
import com.hu.demo.utils.toHexString
import com.hu.demo.utils.toInt

internal class ItemLocationBox(parent: Tree) : FullBox("iloc", 2, parent) {
    var offsetSize: Byte? = null
    var lengthSize: Byte? = null
    var baseOffsetSize: Byte? = null

    // version = 1 or version = 2
    var indexSize: Byte? = null

    var itemCount: Int? = null

    var items: Array<Item>? = null

    override fun readInner(bis: ByteOrderedDataInputStream) {
        super.readInner(bis)
        var cacheByte = bis.nReadByte()
        offsetSize = cacheByte.getSenior()
        lengthSize = cacheByte.getLow()

        cacheByte = bis.nReadByte()
        baseOffsetSize = cacheByte.getSenior()
        val version = version?.toInt()!!
        indexSize = (if (version == 1 || version == 2) cacheByte.getLow() else 0)
        itemCount = (if (version < 2) bis.nReadShort().toInt() else bis.nReadInt())

        items = Array(itemCount!!) {
            Item().apply { read(bis) }
        }
    }

    override fun fork(parent: Tree): Box {
        return ItemLocationBox(parent)
    }

    override fun toString(): String {
        return "${super.toString()}, offsetSize:${offsetSize?.toHexString()}, lengthSize:${lengthSize?.toHexString()}," +
                " baseOffsetSize:${baseOffsetSize?.toHexString()}, indexSize:${indexSize?.toHexString()}, " +
                "itemCount:${itemCount?.toHexString()}, items:\n${items?.beautyToString()}"
    }


    inner class Item : IRead {
        var itemId: Int? = null
        var constructionMethod: Byte? = null
        var dataReferenceIndex: Short? = null
        var baseOffset: ByteArray? = null
        var extentCount: Short? = null
        var extentItem: Array<ExtentItem>? = null

        override fun read(bis: ByteOrderedDataInputStream) {
            val version = version?.toInt()!!
            if (version < 2) {
                itemId = bis.nReadShort().toInt()
            } else if (version == 2) {
                itemId = bis.nReadInt()
            }
            if (version == 1 || version == 2) {
                // 高12位保留，取低4位
                constructionMethod = bis.nReadShort().toByte()
            }
            dataReferenceIndex = bis.nReadShort()
            baseOffset = bis.nReadBytes(baseOffsetSize!!.toInt())
            extentCount = bis.nReadShort()
            extentItem = Array(extentCount!!.toInt()) {
                ExtentItem().apply { read(bis) }
            }
        }

        override fun toString(): String {
            return "Item(itemId:${itemId?.toHexString()}, constructionMethod:${constructionMethod?.toHexString()}, " +
                    "dataReferenceIndex:${dataReferenceIndex?.toHexString()}, baseOffset:${baseOffset?.contentToString()}, " +
                    "extentCount:${extentCount?.toHexString()}, extentItem:\n${extentItem?.beautyToString()})"
        }

        inner class ExtentItem : IRead {
            var extentIndex: Int? = null
            var extentOffset: Int? = null
            var extentLength: Int? = null

            override fun read(bis: ByteOrderedDataInputStream) {
                val version = version?.toInt()!!
                if ((version == 1 || version == 2) && indexSize!! > 0) {
                    extentIndex = bis.nReadBytes(indexSize!!.toInt()).toInt()
                }
                extentOffset = bis.nReadBytes(offsetSize!!.toInt()).toInt()
                extentLength = bis.nReadBytes(lengthSize!!.toInt()).toInt()
            }

            override fun toString(): String {
                return "ExtentItem(extentIndex=${extentIndex?.toHexString()}, extentOffset=${extentOffset?.toHexString()}, " +
                        "extentLength=${extentLength?.toHexString()})"
            }
        }
    }
}