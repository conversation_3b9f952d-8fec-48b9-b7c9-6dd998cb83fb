package com.hu.demo.main.works.gl.renderer.image

import com.hu.demo.main.works.gl.renderer.normal.DitherRenderer
import com.hu.demo.main.works.gl.renderer.normal.FrameRenderer
import com.hu.demo.main.works.gl.renderer.normal.LutRenderer
import com.hu.demo.main.works.gl.renderer.normal.RendererGroup
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_3D_LUT_SIZE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_3D_LUT_TEXTURE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_DITHER_RATIO
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_DITHER_TABLE_TEXTURE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_IMAGE_TEXTURE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_MIX_RATIO
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_VIG_RATIO
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_VIG_TABLE_TEXTURE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.getKey
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.NEXT_NP
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.PROCEDURAL_NP
import com.hu.demo.main.works.gl.renderer.param.RenderArgs
import com.hu.demo.main.works.gl.texture.ITexture
import com.hu.demo.main.works.gl.utils.GlProgram

class LutRendererGroup : RendererGroup(TAG) {
    init {
        addChild(new(DitherRenderer::class.java))
//        addChild(new(LutRenderer::class.java))
        addChild(new(FrameRenderer::class.java))
    }

    override fun render(renderArgs: RenderArgs, glProgram: GlProgram?) {
        var tex: ITexture? = renderArgs.get<ITexture>(PROCEDURAL_NP.getKey(KEY_IMAGE_TEXTURE))
        children.forEach {
            when (it.name) {
                DitherRenderer.TAG -> {
                    renderArgs[NEXT_NP.getKey(KEY_VIG_TABLE_TEXTURE)] = renderArgs[PROCEDURAL_NP.getKey(KEY_VIG_TABLE_TEXTURE)]
                    renderArgs[NEXT_NP.getKey(KEY_DITHER_TABLE_TEXTURE)] = renderArgs[PROCEDURAL_NP.getKey(KEY_DITHER_TABLE_TEXTURE)]
                    renderArgs[NEXT_NP.getKey(KEY_VIG_RATIO)] = renderArgs[PROCEDURAL_NP.getKey(KEY_VIG_RATIO)]
                    renderArgs[NEXT_NP.getKey(KEY_DITHER_RATIO)] = renderArgs[PROCEDURAL_NP.getKey(KEY_DITHER_RATIO)]
                    renderArgs[NEXT_NP.getKey(KEY_IMAGE_TEXTURE)] = tex
                }

                LutRenderer.TAG -> {
                    renderArgs[NEXT_NP.getKey(KEY_3D_LUT_TEXTURE)] = renderArgs[PROCEDURAL_NP.getKey(KEY_3D_LUT_TEXTURE)]
                    renderArgs[NEXT_NP.getKey(KEY_3D_LUT_SIZE)] = renderArgs[PROCEDURAL_NP.getKey(KEY_3D_LUT_SIZE)]
                    renderArgs[NEXT_NP.getKey(KEY_IMAGE_TEXTURE)] = tex
                }

                else -> {
                    renderArgs.remove(NEXT_NP.getKey(KEY_MIX_RATIO))
                }
            }
            it.performRender(renderArgs, glProgram)
            tex = renderArgs[NEXT_NP.getKey(KEY_IMAGE_TEXTURE)]
        }
    }

    companion object {
        private const val TAG = "MixRendererGroup"
    }
}