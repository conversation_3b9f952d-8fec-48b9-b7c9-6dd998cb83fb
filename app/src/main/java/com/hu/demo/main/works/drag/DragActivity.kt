package com.hu.demo.main.works.drag

import android.app.Activity
import android.content.ClipData
import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.Point
import android.graphics.PointF
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.DragEvent
import android.view.MotionEvent
import android.view.View
import android.view.View.DragShadowBuilder
import android.view.View.OnClickListener
import android.widget.Button
import android.widget.ImageView
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.contract.ActivityResultContract
import androidx.core.content.FileProvider
import androidx.core.graphics.drawable.toBitmap
import androidx.lifecycle.lifecycleScope
import com.hu.demo.base.provider.DFileProvider
import com.hu.demo.main.R
import com.hu.demo.utils.ApiLevelUtil
import com.hu.demo.utils.decodeImage
import com.hu.demo.utils.ensureFile
import com.hu.demo.utils.getName
import com.hu.demo.base.ui.BaseActivity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File
import java.util.concurrent.locks.ReentrantReadWriteLock
import kotlin.math.hypot
import kotlin.math.ln
import kotlin.math.min

class DragActivity : BaseActivity(), OnClickListener {
    private var selectUri: Uri? = null
    private var ivImage: ImageView? = null
    private var btnSelectImg: Button? = null
    private val pickTypeResultContract = PickTypeResultContract()
    private val pickImageLauncher = registerForActivityResult(
        pickTypeResultContract,
        PickOnlyCallback { changeData(it) }
    )
    private var startPoint = PointF(0f, 0f)
    private var startScale = 0f
    private var currentPoint = Point(0, 0)
    private var fingerRatio = PointF(0f, 0f)
    private var shadowBuilder: LocalDragShadowBuilder? = null

    override fun getLayoutId(): Int {
        return R.layout.activity_drag
    }

    override fun initToolbar() {
        super.initToolbar()
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowTitleEnabled(true)
            setDisplayShowHomeEnabled(false)
        }
    }

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        ivImage = findViewById(R.id.ivImage)
        btnSelectImg = findViewById(R.id.btnSelectImg)
    }

    override fun initEvent(savedInstanceState: Bundle?) {
        super.initEvent(savedInstanceState)
        btnSelectImg?.setOnClickListener(this)
        ivImage?.setOnLongClickListener(::onLongClick)
        ivImage?.setOnDragListener(::onDragEvent)
        ivImage?.setOnTouchListener(::onTouchEvent)
    }

    private fun onTouchEvent(v: View, event: MotionEvent): Boolean {
        currentPoint.set(event.x.toInt(), event.y.toInt())
        Log.d(TAG, "onTouchEvent: $currentPoint")
        return false
    }

    private fun onLongClick(it: View): Boolean {
        val selectUri = selectUri ?: return false
        val file = File(DFileProvider.cacheFile, contentResolver.getName(selectUri)!!).ensureFile()
        val shareUri = FileProvider.getUriForFile(this, DFileProvider.AUTHORITY, file)
        val lock = DFileProvider.fileLocks.getOrPut(shareUri) { ReentrantReadWriteLock() }.writeLock()
        lifecycleScope.launch(Dispatchers.IO) {
            lock.lock()
            val lastTime = System.currentTimeMillis()
            contentResolver.openInputStream(selectUri)?.use { iStream ->
                file.outputStream().use { oStream ->
                    iStream.copyTo(oStream)
                    //                    var length = 0
                    //                    val bytes = ByteArray(2560)
                    //                    while ((iStream.read(bytes).apply { length = this }) > 0) {
                    //                        oStream.write(bytes, 0, length)
                    //                    }
                }
            }
            Log.d(TAG, "onLongClick: copy file cost: ${System.currentTimeMillis() - lastTime}")
            lock.unlock()
        }
        val clipData = ClipData.newUri(contentResolver, "DragImage", shareUri)
        val bitmap = (it as ImageView).drawable.toBitmap()
        startScale = min(it.width.toFloat() / bitmap.width, it.height.toFloat() / bitmap.height)
        fingerRatio = PointF(
            currentPoint.x.toFloat() / (bitmap.width * startScale),
            (currentPoint.y - (it.height - bitmap.height * startScale) / 2) / (bitmap.height * startScale)
        )
        shadowBuilder = LocalDragShadowBuilder(bitmap, startScale, fingerRatio)
        it.startDragAndDrop(
            clipData,
            shadowBuilder,
            null,
            View.DRAG_FLAG_GLOBAL or View.DRAG_FLAG_GLOBAL_URI_READ or View.DRAG_FLAG_GLOBAL_URI_WRITE or View.DRAG_FLAG_OPAQUE
        )
        return true
    }

    private fun onDragEvent(v: View, event: DragEvent): Boolean {
        Log.d(TAG, "onDragEvent: $event")
        if (event.action == DragEvent.ACTION_DRAG_STARTED) {
            Log.d(TAG, "onDragEvent: set start point: $startPoint")
            startPoint.set(event.x, event.y)
        } else if ((event.action == DragEvent.ACTION_DRAG_LOCATION) || (event.action == DragEvent.ACTION_DRAG_EXITED)) {
            val hypotv = hypot(event.x - startPoint.x, event.y - startPoint.y)
            val logV = ln(1 + hypotv) / 10 + 1
            val scale = 1 / logV
            Log.d(TAG, "onDragEvent: drag:$hypotv, $logV, $scale, $startScale")
            shadowBuilder!!.scale = startScale * scale
            v.updateDragShadow(shadowBuilder)
        }
        return true
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.btnSelectImg -> {
                pickImageLauncher.launch(MIME_TYPE)
            }
        }
    }

    private fun changeData(obj: Any?) {
        ivImage?.also {
            this.selectUri = obj as Uri
            val bitmap = obj.decodeImage(this, 4000)
            if (ApiLevelUtil.isAtLeastAndroidU() && bitmap.hasGainmap()) {
                window.colorMode = ActivityInfo.COLOR_MODE_HDR
            } else {
                window.colorMode = ActivityInfo.COLOR_MODE_DEFAULT
            }
            it.setImageBitmap(bitmap)
        }
    }

    private open class PickOnlyCallback(private val resultFunc: (Uri) -> Unit) : ActivityResultCallback<Uri?> {
        override fun onActivityResult(result: Uri?) {
            result ?: run {
                Log.d(TAG, "PickOnlyCallback.onActivityResult: ")
                return
            }
            Log.d(TAG, "onActivityResult: $result")
            resultFunc(result)
        }
    }

    private class PickTypeResultContract : ActivityResultContract<String, Uri?>() {
        override fun createIntent(context: Context, input: String): Intent {
            return Intent(Intent.ACTION_PICK).setType(input)
        }

        override fun parseResult(resultCode: Int, intent: Intent?): Uri? {
            return if (resultCode != Activity.RESULT_OK) null else intent?.data
        }
    }

    private class LocalDragShadowBuilder(
        private val bitmap: Bitmap,
        var scale: Float = 1f,
        val point: PointF
    ) : DragShadowBuilder() {
        private val paint = Paint(Paint.ANTI_ALIAS_FLAG)

        override fun onDrawShadow(canvas: Canvas) {
            canvas.save()
            val matrix = Matrix()
            matrix.postScale(scale, scale)
            matrix.postTranslate(canvas.width * point.x - bitmap.width * scale * point.x, canvas.height * point.y - bitmap.height * scale * point.y)
            canvas.drawBitmap(bitmap, matrix, paint)
            canvas.restore()
        }

        override fun onProvideShadowMetrics(outShadowSize: Point, outShadowTouchPoint: Point) {
            outShadowSize.set((bitmap.width * scale).toInt(), (bitmap.height * scale).toInt())
            outShadowTouchPoint.set((bitmap.width * scale * point.x).toInt(), (bitmap.height * scale * point.y).toInt())
        }
    }

    companion object {
        private const val TAG = "DragActivity"
        private const val MIME_TYPE = "image/*"
    }
}