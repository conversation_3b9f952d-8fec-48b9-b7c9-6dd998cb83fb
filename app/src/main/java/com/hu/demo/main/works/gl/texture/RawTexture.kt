/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - RawTexture.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/08/24
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * hucanh<PERSON>@Apps.Gallery		2024/08/24		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.texture

import android.graphics.ColorSpace
import android.opengl.GLES30
import com.hu.demo.utils.ColorSpaceExt

/**
 * 开辟了纹理空间，无内容
 *
 * @param width 纹理宽
 * @param height 纹理高
 * @param colorSpace 纹理的色域，默认[COLOR_SPACE_SRGB]
 * @param texConfig 纹理类型，默认[TexConfig.ARGB_8888]
 * @param clamp 纹理范围放大倍率
 * @param texTarget 纹理绘制目标，默认[GLES30.GL_TEXTURE_2D]
 */
open class RawTexture(
    width: Int,
    height: Int,
    colorSpace: ColorSpace = ColorSpaceExt.SRGB,
    texConfig: TexConfig = TexConfig.ARGB_8888,
    clamp: Float = 1.0f,
    texTarget: Int = GLES30.GL_TEXTURE_2D,
) : Texture(TexKey(width, height, texConfig = texConfig, texTarget = texTarget), colorSpace, clamp) {
    override val tag: String = TAG

    override fun load() {
        if (hasUpload) return

        realTexture.value.load()

        hasUpload = true
    }

    private companion object {
        private const val TAG = "RawTexture"
    }
}