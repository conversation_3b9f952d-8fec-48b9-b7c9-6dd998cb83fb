package com.hu.demo.main.works.exifimage

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Bundle
import android.system.Os
import android.system.OsConstants
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.contract.ActivityResultContract
import androidx.exifinterface.media.ExifInterface
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import com.hu.demo.main.R
import com.hu.demo.main.adapter.BaseData
import com.hu.demo.main.adapter.BaseVH
import com.hu.demo.main.adapter.DefaultAdapter
import com.hu.demo.base.ui.BaseActivity
import com.hu.demo.main.works.main.TYPE_DEFAULT
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class ExifImageActivity : BaseActivity(), View.OnClickListener {
    private var tvHasThumbValue: TextView? = null
    private var tvHasThumbWidthValue: TextView? = null
    private var tvHasThumbHeightValue: TextView? = null
    private var tvThumbColorSpaceValue: TextView? = null
    private var tvImageColorSpaceValue: TextView? = null
    private var ivImage: ImageView? = null
    private var btnSelectImg: Button? = null
    private var exifAdapter: ExifAdapter? = null
    private val pickTypeResultContract = PickTypeResultContract()
    private val pickImageLauncher = registerForActivityResult(
        pickTypeResultContract,
        PickOnlyCallback { changeData(it) }
    )

    override fun getLayoutId(): Int {
        return R.layout.activity_exif_image
    }

    override fun initToolbar() {
        super.initToolbar()
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowTitleEnabled(true)
            setDisplayShowHomeEnabled(false)
        }
    }

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        tvHasThumbValue = findViewById(R.id.tvHasThumbValue)
        tvHasThumbWidthValue = findViewById(R.id.tvHasThumbWidthValue)
        tvHasThumbHeightValue = findViewById(R.id.tvHasThumbHeightValue)
        tvThumbColorSpaceValue = findViewById(R.id.tvThumbColorSpaceValue)
        tvImageColorSpaceValue = findViewById(R.id.tvImageColorSpaceValue)
        ivImage = findViewById(R.id.ivImage)
        btnSelectImg = findViewById(R.id.btnSelectImg)
    }

    override fun initEvent(savedInstanceState: Bundle?) {
        super.initEvent(savedInstanceState)
        btnSelectImg?.setOnClickListener(this)
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.btnSelectImg -> {
                pickImageLauncher.launch(MIME_TYPE)
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun changeData(obj: Any?) {
        lifecycleScope.launch(Dispatchers.Default) {
            contentResolver.openFileDescriptor(obj as Uri, "r")?.use {
                val exifInterface = ExifInterface(it.fileDescriptor)
                val hasThumbnail = exifInterface.hasThumbnail()
                val bitmap = exifInterface.thumbnailBitmap
                val thumbWidth = bitmap?.width ?: -1
                val thumbHeight = bitmap?.height ?: -1
                Os.lseek(it.fileDescriptor, 0, OsConstants.SEEK_SET)
                withContext(Dispatchers.Main) {
                    tvHasThumbValue?.text = hasThumbnail.toString()
                    tvHasThumbWidthValue?.text = thumbWidth.toString()
                    tvHasThumbHeightValue?.text = thumbHeight.toString()
                    tvThumbColorSpaceValue?.text = bitmap?.colorSpace?.name
                    tvImageColorSpaceValue?.text = BitmapFactory.decodeFileDescriptor(it.fileDescriptor)?.colorSpace?.name
                    ivImage?.setImageBitmap(bitmap)
                }
            }
        }
    }

    private open class PickOnlyCallback(private val resultFunc: (Uri) -> Unit) : ActivityResultCallback<Uri?> {
        override fun onActivityResult(result: Uri?) {
            result ?: run {
                Log.d(TAG, "PickOnlyCallback.onActivityResult: ")
                return
            }
            Log.d(TAG, "onActivityResult: $result")
            resultFunc(result)
        }
    }

    private class PickTypeResultContract : ActivityResultContract<String, Uri?>() {
        override fun createIntent(context: Context, input: String): Intent {
            return Intent(Intent.ACTION_PICK).setType(input)
        }

        override fun parseResult(resultCode: Int, intent: Intent?): Uri? {
            return if (resultCode != Activity.RESULT_OK) null else intent?.data
        }
    }

    class ExifVH(parent: ViewGroup) : BaseVH<ExifData>(parent, R.layout.item_exif) {
        private val tvKey: TextView = findViewById(R.id.tv_item_key)
        private val tvValue: TextView = findViewById(R.id.tv_item_value)

        override fun bind(extraData: Map<String, Any>, data: ExifData, position: Int) {
            tvKey.text = data.key
            tvValue.text = data.value
        }
    }

    data class ExifData(val key: String?, val value: String?) : BaseData(TYPE_DEFAULT)


    class ExifAdapter(recyclerView: RecyclerView) : DefaultAdapter<BaseData, BaseVH<BaseData>>(recyclerView) {
        @Suppress("UNCHECKED_CAST")
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH<BaseData> {
            return ExifVH(parent) as BaseVH<BaseData>
        }

        override fun onBindViewHolder(holder: BaseVH<BaseData>, position: Int) {
            holder.bind(emptyMap(), dataList[position], position)
        }
    }

    companion object {
        private const val TAG = "ExifActivity"
        private const val MIME_TYPE = "image/*"
    }

}