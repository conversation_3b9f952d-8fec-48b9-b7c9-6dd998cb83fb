/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - RealTexture.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/09/20
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * huca<PERSON><PERSON>@Apps.Gallery		2024/09/20		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.texture

import android.graphics.Bitmap
import android.graphics.ColorSpace
import android.hardware.HardwareBuffer
import android.opengl.EGL14
import android.opengl.GLES30
import android.util.Log
import androidx.collection.ArraySet
import androidx.opengl.EGLExt
import androidx.opengl.EGLImageKHR
import com.hu.demo.main.works.gl.texture.ITexture.Companion.getFormat
import com.hu.demo.main.works.gl.texture.ITexture.Companion.getHwbFormat
import com.hu.demo.main.works.gl.texture.ITexture.Companion.getInternalFormat
import com.hu.demo.main.works.gl.texture.ITexture.Companion.getType
import com.hu.demo.main.works.gl.utils.GLThread
import com.hu.demo.main.works.gl.utils.GlUtil
import com.hu.demo.main.works.gl.utils.HwbKey
import com.hu.demo.main.works.gl.utils.HwbPool
import com.hu.demo.main.works.gl.utils.KeyCachePool
import com.hu.demo.main.works.gl.utils.singleInt

/**
 * 真实存储纹理的类
 *
 * @param texKey 构造[RealTexture]的唯一键
 */
class RealTexture internal constructor(val texKey: TexKey) : ITexture {
    private val threadName = Thread.currentThread().name
    override var canUse: Boolean = true
    override var textureId: Int = texKey.textureId
        private set
    override val width: Int = texKey.width
    override val height: Int = texKey.height
    override val depth: Int = texKey.depth
    override val colorSpace: ColorSpace
        get() = throw IllegalArgumentException("RealTexture cannot call is field!")
    override var displayColorSpace: ColorSpace
        set(value) = throw IllegalArgumentException("RealTexture cannot call is field!")
        get() = throw IllegalArgumentException("RealTexture cannot call is field!")
    override val texConfig: TexConfig = texKey.texConfig
    override val texTarget: Int = texKey.texTarget
    override val clamp: Float
        get() = throw IllegalArgumentException("RealTexture cannot call is field!")
    override val byteSize: Int = texConfig.byteSize

    private var imageKHR: EGLImageKHR? = null
    internal var buffer: HardwareBuffer? = null

    private val display by lazy { EGL14.eglGetDisplay(EGL14.EGL_DEFAULT_DISPLAY) }

    override fun increaseMark() {
        throw IllegalArgumentException("RealTexture cannot call is method!")
    }

    override fun reduceMark() {
        throw IllegalArgumentException("RealTexture cannot call is method!")
    }

    override fun load() {
        checkCanUse()
        if (textureId != GLES30.GL_NONE) {
            setTexParameter()
        } else {
            textureId = singleInt { GLES30.glGenTextures(1, it, 0) }
            setTexParameter()
            GlUtil.checkGlError()
            if (texTarget == GLES30.GL_TEXTURE_2D) {
                val internalFormat = texConfig.getInternalFormat()
                GLES30.glBindTexture(texTarget, textureId)
                HwbPool.DEFAULT.obtain(HwbKey(width, height, texConfig.getHwbFormat()))?.let { hardwareBuffer ->
                    EGLExt.eglCreateImageFromHardwareBuffer(display, hardwareBuffer)?.let { imageKHR ->
                        EGLExt.glEGLImageTargetTexture2DOES(GLES30.GL_TEXTURE_2D, imageKHR)
                        this.imageKHR = imageKHR
                        this.buffer = hardwareBuffer
                    } ?: run {
                        Log.w(TAG, "load: imageKHR is null!")
                        HwbPool.DEFAULT.reuse(hardwareBuffer)
                    }
                }
                if (imageKHR == null) {
                    GLES30.glTexImage2D(texTarget, 0, internalFormat, width, height, 0, getFormat(internalFormat), texConfig.getType(), null)
                }
                GlUtil.checkGlError()
            }
        }
        GLES30.glFlush()
    }

    private fun setTexParameter() {
        GLES30.glBindTexture(texTarget, textureId)
        GlUtil.checkGlError()
        GLES30.glPixelStorei(GLES30.GL_UNPACK_ALIGNMENT, texConfig.byteSize)
        GLES30.glTexParameteri(texTarget, GLES30.GL_TEXTURE_MIN_FILTER, GLES30.GL_LINEAR)
        GLES30.glTexParameteri(texTarget, GLES30.GL_TEXTURE_MAG_FILTER, GLES30.GL_LINEAR)
        GLES30.glTexParameteri(texTarget, GLES30.GL_TEXTURE_WRAP_S, GLES30.GL_CLAMP_TO_EDGE)
        GLES30.glTexParameteri(texTarget, GLES30.GL_TEXTURE_WRAP_T, GLES30.GL_CLAMP_TO_EDGE)
    }

    @GLThread
    override fun copy(outTexture: ITexture?): ITexture {
        throw IllegalArgumentException("RealTexture cannot call is method!")
    }

    override fun toBitmap(scale: Float): Bitmap {
        throw IllegalArgumentException("RealTexture cannot call is method!")
    }

    override fun active(texUnitIndex: Int) {
        checkCanUse()
        GLES30.glActiveTexture(GLES30.GL_TEXTURE0 + texUnitIndex)
        GLES30.glBindTexture(texTarget, textureId)
        GlUtil.checkGlError()
    }

    override fun force() {
        checkCanUse()
        GLES30.glFinish()
    }

    /**
     * 检查当前纹理是否可用，如果已经执行过recycle后，纹理的方法将失效，当不可用时，抛出异常
     *
     * @throws IllegalArgumentException 当不可用时，抛出异常
     */
    @Throws(IllegalArgumentException::class)
    protected fun checkCanUse() {
        if (canUse) return
        throw IllegalStateException("This texture has called recycle or reuse. Can no longer be used! ${toString()}")
    }

    override fun reuse() {
        // 可能存在业务已经将纹理recycle，但是纹理还在渲染链路的情况，这里需要拦截一下
        if (!canUse) {
            Log.d(TAG, "recycle: this texture has been recycled.")
            return
        }
        reuse(this)
    }

    override fun recycle() {
        if (!canUse) {
            Log.d(TAG, "recycle: this texture has been recycled.")
            return
        }
        imageKHR?.also { EGLExt.eglDestroyImageKHR(display, it) }
        imageKHR = null
        buffer?.close()
        buffer = null
        GLES30.glDeleteTextures(1, intArrayOf(textureId), 0)
        canUse = false
    }

    override fun getTexture(): Texture {
        throw IllegalArgumentException("RealTexture cannot call is method!")
    }

    override fun toVirtual(): ITexture {
        throw IllegalArgumentException("RealTexture cannot call is method!")
    }

    override fun toString(): String {
        return "RealTexture(texKey=$texKey, threadName=$threadName, canUse=$canUse, textureId=$textureId)"
    }

    companion object {
        private const val TAG = "RealTexture"

        private const val CACHE_POOL_SIZE = 10

        /**
         * 参数集缓存池
         */
        private val cachePool = object : ThreadLocal<KeyCachePool<TexKey, RealTexture>>() {
            override fun initialValue(): KeyCachePool<TexKey, RealTexture> {
                Log.d(TAG, "initialValue: cachePool")
                return KeyCachePool(CACHE_POOL_SIZE, { RealTexture(it) })
            }
        }

        /**
         * 所有纹理的引用
         */
        private val texRefs = object : ThreadLocal<ArraySet<RealTexture>>() {
            override fun initialValue(): ArraySet<RealTexture> {
                Log.d(TAG, "initialValue: texRefs")
                return ArraySet()
            }
        }

        /**
         * 获取一个参数集
         *
         * @param texKey 创建纹理的唯一键
         *
         * @return 返回从缓存或重新创建的纹理
         * @param isSelf 是否是本模块的纹理，以是否是本模块创建作为基准
         */
        fun obtain(texKey: TexKey, isSelf: Boolean): RealTexture {
            val realTexture = cachePool.get()!!.obtain(texKey)
            if (isSelf) {
                addRef(realTexture)
            }
            return realTexture
        }

        /**
         * 回收纹理，以备后续复用
         *
         * @param realTex 需要被复用的纹理
         */
        private fun reuse(realTex: RealTexture) {
            cachePool.get()!!.reuse(realTex.texKey, realTex)
            deRef(realTex)
        }

        /**
         * 添加对纹理的引用
         */
        private fun addRef(realTex: RealTexture) {
            texRefs.get()!!.add(realTex)
        }

        /**
         * 对纹理解引用
         *
         * @param realTex 纹理
         */
        private fun deRef(realTex: RealTexture) {
            texRefs.get()!!.remove(realTex)
        }

        /**
         * 清除纹理缓存
         */
        fun clean() {
            Log.d(TAG, "clean: cachePool. size: ${cachePool.get()!!.size}, thread: ${Thread.currentThread()}")
            cachePool.get()!!.clean()
            cachePool.remove()

            Log.d(TAG, "clean: texRef. size: ${texRefs.get()!!.size}, thread: ${Thread.currentThread()}")
            texRefs.get()!!.iterator().forEach {
                it.recycle()
            }
            texRefs.remove()
        }
    }
}