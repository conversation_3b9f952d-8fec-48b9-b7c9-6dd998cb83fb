/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - GlCanvas.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/08/29
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery		2024/08/29		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.canvas

import android.opengl.GLES20
import android.opengl.GLES30
import android.util.Log
import com.hu.demo.main.works.gl.IRecyclable
import com.hu.demo.main.works.gl.IReusable
import com.hu.demo.main.works.gl.texture.ITexture
import com.hu.demo.main.works.gl.texture.Texture
import com.hu.demo.main.works.gl.utils.GlUtil
import com.hu.demo.main.works.gl.utils.KeyCachePool
import com.hu.demo.main.works.gl.utils.singleInt

/**
 * OpenGL的渲染Canvas的key类
 *
 * @param size 需要画的顶点数量
 * @param width 纹理的宽
 * @param height 纹理的高
 * @param className 类的名称simpleName
 * @param drawMode 绘制模式，见[GLES30.GL_TRIANGLES]、[GLES30.GL_TRIANGLE_STRIP]、[GLES30.GL_TRIANGLE_FAN]
 */
open class CanvasKey(
    val size: Int,
    val width: Int,
    val height: Int,
    val className: String?,
    val drawMode: Int,
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as CanvasKey

        if (className != other.className) return false
        if (size != other.size) return false
        if (width != other.width) return false
        if (height != other.height) return false
        if (drawMode != other.drawMode) return false

        return true
    }

    override fun hashCode(): Int {
        var result = className?.hashCode() ?: 0
        result = 31 * result + size
        result = 31 * result + width
        result = 31 * result + height
        result = 31 * result + drawMode
        return result
    }
}

/**
 * fbo类型的Canvas key
 *
 * @param size 需要画的顶点数量
 * @param width 纹理的宽
 * @param height 纹理的高
 * @param className 类的名称simpleName，默认[ArrayFboGlCanvas]
 * @param drawMode 绘制模式，见[GLES30.GL_TRIANGLES]、[GLES30.GL_TRIANGLE_STRIP]、[GLES30.GL_TRIANGLE_FAN]，默认[GLES30.GL_TRIANGLE_STRIP]
 */
class FboKey(
    size: Int,
    width: Int,
    height: Int,
    className: String? = ArrayFboGlCanvas::class.simpleName,
    drawMode: Int = GLES30.GL_TRIANGLE_STRIP,
) : CanvasKey(size, width, height, className, drawMode)

/**
 * 普通的渲染Canvas的key类
 *
 * @param size 需要画的顶点数量
 * @param width 绘制窗口的宽
 * @param height 绘制窗口的高
 * @param className 类的名称simpleName
 * @param drawMode 绘制模式，见[GLES30.GL_TRIANGLES]、[GLES30.GL_TRIANGLE_STRIP]、[GLES30.GL_TRIANGLE_FAN]
 */
class SKey(
    size: Int,
    width: Int,
    height: Int,
    className: String? = ArrayScreenGlCanvas::class.simpleName,
    drawMode: Int = GLES30.GL_TRIANGLE_STRIP,
) : CanvasKey(size, width, height, className, drawMode)

abstract class GlCanvas<T : CanvasKey> : IReusable, IRecyclable {
    abstract val key: T

    override fun reuse() {
        reuse(this)
    }

    override fun recycle() = Unit

    /**
     * GlCanvas的异常类
     */
    class GlCanvasException(message: String?) : IllegalArgumentException(message)

    companion object {
        private const val TAG = "GlCanvas"

        private val cachePool = object : ThreadLocal<KeyCachePool<CanvasKey, GlCanvas<*>>>() {
            override fun initialValue(): KeyCachePool<CanvasKey, GlCanvas<*>> {
                return KeyCachePool(
                    4,
                    {
                        when (it.className) {
                            ArrayFboGlCanvas::class.simpleName -> ArrayFboGlCanvas(it as FboKey)
                            ArrayScreenGlCanvas::class.simpleName -> ArrayScreenGlCanvas(it as SKey)
                            else -> throw GlCanvasException("No corresponding type: $it")
                        }
                    }
                )
            }
        }

        /**
         * 使用唯一键[key]，从缓存中获取一个[GlCanvas]
         *
         * @param key 生成[GlCanvas]的唯一键
         */
        fun obtain(key: CanvasKey): GlCanvas<*> {
            return cachePool.get()!!.obtain(key)
        }

        /**
         * 对[canvas]执行复用，将[canvas]放入到缓存池中
         */
        private fun reuse(canvas: GlCanvas<*>) {
            cachePool.get()!!.reuse(canvas.key, canvas)
        }

        /**
         * 清除缓存池
         */
        fun clean() {
            Log.d(TAG, "clean: is called.")
            cachePool.get()!!.clean()
            cachePool.remove()
        }
    }
}

/**
 * fbo类型的虚拟画布，使用FrameBuffer绑定[Texture]，将内容绘制到目标[Texture]上
 *
 * @param key 构造[GlCanvas]的唯一键
 */
abstract class FboGlCanvas(final override val key: FboKey) : GlCanvas<FboKey>() {
    private val fboId by lazy { singleInt { GLES30.glGenFramebuffers(1, it, 0) } }

    /**
     * 执行绘制到目标[Texture]
     *
     * @param texture 要绘制到的目标[Texture]
     */
    fun draw(texture: ITexture) {
        GLES30.glBindFramebuffer(GLES30.GL_FRAMEBUFFER, fboId)
        GLES30.glFramebufferTexture2D(GLES30.GL_FRAMEBUFFER, GLES30.GL_COLOR_ATTACHMENT0, texture.texTarget, texture.textureId, 0)
        val status = GLES30.glCheckFramebufferStatus(GLES30.GL_FRAMEBUFFER)
        GlUtil.checkGlException(status == GLES30.GL_FRAMEBUFFER_COMPLETE, "bindFBO: Buffer binding failed! " + GLES20.glGetError())
        GlUtil.checkGlError()
        GLES30.glViewport(0, 0, key.width, key.height)
        GlUtil.checkGlError()
        GLES30.glClearColor(0f, 0f, 0f, 0f)
        GLES30.glClear(GLES30.GL_COLOR_BUFFER_BIT)
        GlUtil.checkGlError()
        drawInternal()
        GLES30.glBindFramebuffer(GLES30.GL_FRAMEBUFFER, GLES30.GL_NONE)
        GlUtil.checkGlError()
    }

    /**
     * 执行渲染的具体动作
     */
    protected abstract fun drawInternal()

    override fun recycle() {
        GLES30.glDeleteFramebuffers(1, intArrayOf(fboId), 0)
    }

    companion object {
        private const val TAG = "FboGlCanvas"
    }
}

/**
 * 使用[GLES30.glDrawArrays]方法执行绘制的[FboGlCanvas]
 *
 * @param key 构造[GlCanvas]的唯一键
 */
class ArrayFboGlCanvas internal constructor(key: FboKey) : FboGlCanvas(key) {
    override fun drawInternal() {
        GLES30.glDrawArrays(key.drawMode, 0, key.size)
        GlUtil.checkGlError()
    }

    companion object {
        private const val TAG = "ArrayFboGlCanvas"
    }
}

/**
 * 默认的渲染画布。
 * 针对Androidx的graphic-core库的渲染方法，默认需要渲染到指定的fbo中，因此默认画布需要渲染接受一个[fboId]的参数
 *
 * @param key 构造[GlCanvas]的唯一键
 */
abstract class ScreenGlCanvas internal constructor(final override val key: SKey) : GlCanvas<SKey>() {

    /**
     * 针对Androidx的graphic-core库的渲染方法，默认需要渲染到指定的fbo中，因此默认画布需要渲染接受一个[fboId]的参数
     *
     * @param fboId 指定渲染到的fboId
     */
    fun draw(fboId: Int) {
        GLES30.glBindFramebuffer(GLES30.GL_FRAMEBUFFER, fboId)
        GLES30.glViewport(0, 0, key.width, key.height)
        val status = GLES30.glCheckFramebufferStatus(GLES30.GL_FRAMEBUFFER)
        GlUtil.checkGlException(status == GLES30.GL_FRAMEBUFFER_COMPLETE, "bindFBO: Buffer binding failed! " + GLES20.glGetError())
        GlUtil.checkGlError()
        drawInternal()
        GLES30.glBindFramebuffer(GLES30.GL_FRAMEBUFFER, GLES30.GL_NONE)
        GlUtil.checkGlError()
    }

    /**
     * 执行渲染的具体动作
     */
    abstract fun drawInternal()

    companion object {
        private const val TAG = "ScreenGlCanvas"
    }
}

/**
 * 使用[GLES30.glDrawArrays]方法执行绘制的[FboGlCanvas]
 *
 * @param fboKey 构造[GlCanvas]的唯一键
 */
class ArrayScreenGlCanvas internal constructor(key: SKey) : ScreenGlCanvas(key) {
    override fun drawInternal() {
        GLES30.glDrawArrays(key.drawMode, 0, key.size)
        GlUtil.checkGlError()
    }

    companion object {
        private const val TAG = "ArrayScreenGlCanvas"
    }
}