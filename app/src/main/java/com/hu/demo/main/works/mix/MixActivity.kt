package com.hu.demo.main.works.mix

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.Display
import android.view.View
import android.view.View.OnClickListener
import android.widget.Button
import android.widget.SeekBar
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.contract.ActivityResultContract
import androidx.annotation.RequiresApi
import androidx.graphics.opengl.egl.EGLConfigAttributes
import androidx.lifecycle.lifecycleScope
import com.hu.demo.main.R
import com.hu.demo.utils.decodeImage
import com.hu.demo.base.ui.BaseActivity
import com.hu.demo.main.works.gl.EditingSurfaceView
import com.hu.demo.main.works.gl.OnSeekBarChangeListener2
import com.hu.demo.main.works.gl.renderer.brighten.CombineImageOnlyHdrMode
import com.hu.demo.main.works.gl.renderer.brighten.HdrModeManager
import com.hu.demo.main.works.gl.renderer.image.MixRendererGroup
import com.hu.demo.main.works.gl.renderer.normal.Renderer
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_COLOR_SPACE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_DEVICE_HDR_SDR_RATIO
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_GAINMAP_TEXTURE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_GAINMAP_TEXTURE_2
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_IMAGE_TEXTURE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_IMAGE_TEXTURE_2
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_MIX_RATIO
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.getKey
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.STABLE_NP
import com.hu.demo.main.works.gl.texture.BitmapTexture
import com.hu.demo.main.works.gl.texture.GainmapTexture
import com.hu.demo.main.works.gl.utils.EditingEglSpec
import com.hu.demo.utils.ColorSpaceExt
import kotlinx.coroutines.launch
import java.util.function.Consumer

@RequiresApi(Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
class MixActivity : BaseActivity(), OnClickListener, OnSeekBarChangeListener2 {
    private var glView: EditingSurfaceView? = null
    private var sbSeek: SeekBar? = null
    private var btnSelectImg1: Button? = null
    private var btnSelectImg2: Button? = null
    private val pickTypeResultContract = PickTypeResultContract()
    private val pickImageLauncher1 = registerForActivityResult(
        pickTypeResultContract,
        PickOnlyCallback { changeImageData1(it) }
    )
    private val pickImageLauncher2 = registerForActivityResult(
        pickTypeResultContract,
        PickOnlyCallback { changeImageData2(it) }
    )
    private var percent: Float = 0f
    private var hdrSdrRatio: Float = 1.0f
    private var desiredRatio: Float = 1.0f
    private val hdrSdrListener = Consumer<Display> { display ->
        hdrSdrRatio = display?.hdrSdrRatio ?: 1.0f
        glView?.setDataAndRender(STABLE_NP.getKey(KEY_DEVICE_HDR_SDR_RATIO), hdrSdrRatio)
    }

    override fun getLayoutId(): Int {
        return R.layout.activity_gl_mix
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        if (display?.isHdrSdrRatioAvailable == true) {
            display?.registerHdrSdrRatioChangedListener({ it.run() }, hdrSdrListener)
        }
    }

    override fun initToolbar() {
        super.initToolbar()
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowTitleEnabled(true)
            setDisplayShowHomeEnabled(false)
        }
    }

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        window.colorMode = ActivityInfo.COLOR_MODE_WIDE_COLOR_GAMUT
        glView = findViewById(R.id.glView)
        sbSeek = findViewById(R.id.sbSeek)
        btnSelectImg1 = findViewById(R.id.btnSelectImg1)
        btnSelectImg2 = findViewById(R.id.btnSelectImg2)
    }

    override fun initEvent(savedInstanceState: Bundle?) {
        super.initEvent(savedInstanceState)
        sbSeek?.setOnSeekBarChangeListener(this)
        btnSelectImg1?.setOnClickListener(this)
        btnSelectImg2?.setOnClickListener(this)
        initRenderer()
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun initRenderer() {
        glView?.apply {
            initGlRenderer(
                EditingEglSpec(),
                {
                    loadConfig(EGLConfigAttributes.RGBA_1010102)!!
                }
            )

            initBufferRenderer(HdrModeManager(this, arrayOf(CombineImageOnlyHdrMode())))

            glView?.setDataAndRender(STABLE_NP.getKey(KEY_MIX_RATIO), sbSeek!!.progress.toFloat() / sbSeek!!.max.toFloat())

            // 设置屏幕色域
            setData(STABLE_NP.getKey(KEY_COLOR_SPACE), if (window.isWideColorGamut) ColorSpaceExt.DISPLAY_P3 else ColorSpaceExt.SRGB)

            // 添加渲染节点
            changeNode {
                it.removeChildren()
                it.addChild(Renderer.new(MixRendererGroup::class.java))
            }
        }
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.btnSelectImg1 -> {
                pickImageLauncher1.launch(MIME_TYPE_IMAGE)
            }

            R.id.btnSelectImg2 -> {
                pickImageLauncher2.launch(MIME_TYPE_IMAGE)
            }
        }
    }

    override fun onProgressChanged(seekBar: SeekBar, progress: Int, fromUser: Boolean) {
        glView?.setDataAndRender(STABLE_NP.getKey(KEY_MIX_RATIO), progress.toFloat() / seekBar.max.toFloat())
    }

    private suspend fun calcDisiredRatio() {
        val gainmapTexture = glView?.getData<GainmapTexture>(STABLE_NP.getKey(KEY_GAINMAP_TEXTURE))
        desiredRatio = maxOf(1.0f, (gainmapTexture?.gainmap?.displayRatioForFullHdr?.let { it * percent }) ?: 1.0f)
    }

    @SuppressLint("HalfFloat")
    private fun changeImageData1(obj: Any?) {
        lifecycleScope.launch {
            val bitmap = (obj as Uri).decodeImage(this@MixActivity, 4000)
            glView?.setDatasAndRender(mapOf(
                STABLE_NP.getKey(KEY_IMAGE_TEXTURE) to BitmapTexture(bitmap),
                STABLE_NP.getKey(KEY_GAINMAP_TEXTURE) to bitmap.gainmap?.let { GainmapTexture(it) }
            ))
            calcDisiredRatio()
        }
    }

    @SuppressLint("HalfFloat")
    private fun changeImageData2(obj: Any?) {
        lifecycleScope.launch {
            val bitmap = (obj as Uri).decodeImage(this@MixActivity, 4000)
            glView?.setDatasAndRender(mapOf(
                STABLE_NP.getKey(KEY_IMAGE_TEXTURE_2) to BitmapTexture(bitmap),
                STABLE_NP.getKey(KEY_GAINMAP_TEXTURE_2) to bitmap.gainmap?.let { GainmapTexture(it) }
            ))
            calcDisiredRatio()
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        display?.unregisterHdrSdrRatioChangedListener(hdrSdrListener)
    }

    override fun onDestroy() {
        super.onDestroy()
        glView?.apply {
            bufferRenderer.release(false)
            glRenderer.stop(false)
        }
    }

    private open class PickOnlyCallback(private val resultFunc: (Uri) -> Unit) : ActivityResultCallback<Uri?> {
        override fun onActivityResult(result: Uri?) {
            result ?: run {
                Log.d(TAG, "PickOnlyCallback.onActivityResult: ")
                return
            }
            Log.d(TAG, "onActivityResult: $result")
            resultFunc(result)
        }
    }

    private class PickTypeResultContract : ActivityResultContract<String, Uri?>() {
        override fun createIntent(context: Context, input: String): Intent {
            return Intent(Intent.ACTION_PICK).setType(input)
        }

        override fun parseResult(resultCode: Int, intent: Intent?): Uri? {
            return if (resultCode != Activity.RESULT_OK) null else intent?.data
        }
    }

    companion object {
        private const val TAG = "GLShowActivity"
        private const val MIME_TYPE_IMAGE = "image/*"
    }

}