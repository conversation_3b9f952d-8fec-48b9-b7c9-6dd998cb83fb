package com.hu.demo.main.works.color

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.ColorMatrixColorFilter
import android.graphics.Paint
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.os.Bundle
import android.view.View
import android.widget.SeekBar
import android.widget.TextView
import androidx.core.graphics.createBitmap
import com.hu.demo.base.ui.BaseActivity
import com.hu.demo.main.R
import kotlin.math.roundToInt

class ColorActivity : BaseActivity(), SeekBar.OnSeekBarChangeListener {
    private var viewR: View? = null
    private var viewG: View? = null
    private var viewB: View? = null
    private var viewMix: View? = null
    private var tvR: TextView? = null
    private var tvG: TextView? = null
    private var tvB: TextView? = null
    private var tvLight: TextView? = null
    private var sbR: SeekBar? = null
    private var sbG: SeekBar? = null
    private var sbB: SeekBar? = null
    private var sbLight: SeekBar? = null

    override fun getLayoutId(): Int {
        return R.layout.activity_color
    }

    override fun initToolbar() {
        super.initToolbar()
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowTitleEnabled(true)
            setDisplayShowHomeEnabled(false)
        }
    }

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        viewR = findViewById(R.id.viewR)
        viewG = findViewById(R.id.viewG)
        viewB = findViewById(R.id.viewB)
        viewMix = findViewById(R.id.viewMix)
        tvR = findViewById(R.id.tvR)
        tvG = findViewById(R.id.tvG)
        tvB = findViewById(R.id.tvB)
        tvLight = findViewById(R.id.tvLight)
        sbR = findViewById(R.id.sbR)
        sbG = findViewById(R.id.sbG)
        sbB = findViewById(R.id.sbB)
        sbLight = findViewById(R.id.sbLight)
    }

    override fun initEvent(savedInstanceState: Bundle?) {
        super.initEvent(savedInstanceState)
        sbR?.setOnSeekBarChangeListener(this)
        sbG?.setOnSeekBarChangeListener(this)
        sbB?.setOnSeekBarChangeListener(this)
        sbLight?.setOnSeekBarChangeListener(this)
    }


    override fun onProgressChanged(seekBar: SeekBar, progress: Int, fromUser: Boolean) {
        when (seekBar.id) {
            R.id.sbR -> {
                tvR?.text = "R:$progress"
                viewR?.setBackgroundColor(Color.rgb(progress, 0, 0))
                viewMix?.setBackgroundColor(Color.rgb(progress, sbG!!.progress, sbB!!.progress))
                if (fromUser) {
                    changeLight(progress, sbG!!.progress, sbB!!.progress)
                }
            }

            R.id.sbG -> {
                tvG?.text = "G:$progress"
                viewG?.setBackgroundColor(Color.rgb(0, progress, 0))
                viewMix?.setBackgroundColor(Color.rgb(sbG!!.progress, progress, sbB!!.progress))
                if (fromUser) {
                    changeLight(sbG!!.progress, progress, sbB!!.progress)
                }
            }

            R.id.sbB -> {
                tvB?.text = "B:$progress"
                viewB?.setBackgroundColor(Color.rgb(0, 0, progress))
                viewMix?.setBackgroundColor(Color.rgb(sbR!!.progress, sbG!!.progress, progress))
                if (fromUser) {
                    changeLight(sbR!!.progress, sbG!!.progress, progress)
                }
            }

            R.id.sbLight -> {
                tvLight?.text = "L:$progress"
                val r = sbR!!.progress
                val g = sbG!!.progress
                val b = sbB!!.progress
                val scale = 1 - (calcLight(r, g, b) - progress).toFloat() / 256
                if (fromUser) {
                    sbR!!.progress = (r * scale).roundToInt()
                    sbG!!.progress = (g * scale).roundToInt()
                    sbB!!.progress = (b * scale).roundToInt()
                }
            }
        }
    }

    override fun onStartTrackingTouch(seekBar: SeekBar?) = Unit

    override fun onStopTrackingTouch(seekBar: SeekBar?) = Unit

    private fun changeLight(r: Int, g: Int, b: Int) {
        // bt709
        val light = calcLight(r, g, b)
        tvLight?.text = "L:$light"
        sbLight?.progress = light
    }

    private fun calcLight(r: Int, g: Int, b: Int): Int {
        return (0.2126 * r + 0.7152 * g + 0.0722 * b).roundToInt()
    }

    private fun changeColor(sourceBmp: Bitmap): Bitmap {
        val bmp = createBitmap(sourceBmp.width, sourceBmp.height, sourceBmp.config!!)
        val canvas = Canvas(bmp)
        canvas.drawColor(Color.RED)

        val colorMatrix = floatArrayOf(
            0f, 0f, 0f, 0f, 0f,
            0f, 0f, 0f, 0f, 0f,
            0f, 0f, 0f, 0f, 0f,
            0f, 0f, 0f, 1f, 0f,
        )
        val paint = Paint()
        paint.colorFilter = ColorMatrixColorFilter(colorMatrix)
        paint.xfermode = PorterDuffXfermode(PorterDuff.Mode.DST_ATOP)

        canvas.drawBitmap(sourceBmp, 0f, 0f, paint)

        return bmp
    }

    companion object {
        private const val TAG = "CanvasActivity"
        private const val MIME_TYPE = "image/*"
    }
}