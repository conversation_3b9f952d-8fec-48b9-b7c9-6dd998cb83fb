package com.hu.demo.main.works.uhdr

import android.content.Context
import android.graphics.Gainmap
import android.graphics.ImageDecoder
import android.util.Log
import androidx.core.graphics.decodeBitmap
import androidx.work.Data
import androidx.work.WorkerParameters
import com.hu.demo.utils.ApiLevelUtil
import com.oplus.camera.jni.IccProfile
import java.io.File
import java.io.FileFilter

class UhdrCombineWorker(context: Context, workerParams: WorkerParameters) : BaseWorker(context, workerParams) {
    override fun doWork(): Result {
        return combineUhdr()
    }

    private fun combineUhdr(): Result {
        Log.d(TAG, "splitUhdr: start")
        val inputImagesDir = inputData.getString(INPUT_DIR)?.let { File(it) } ?: run {
            Log.d(TAG, "splitUhdr: end 1")
            return Result.failure()
        }
        val outputImagesDir = inputData.getString(OUTPUT_DIR)?.let { File(it) } ?: inputImagesDir
        outputImagesDir.mkdirs()
        val sdr_suffix = "_sdr.jpg"
        val gain_suffix = "_gain.jpg"
        val sdrImageFiles = inputImagesDir.listFiles(FileFilter {
            it.path.endsWith(sdr_suffix) && !it.isHidden
        })?.sortedArray() ?: return Result.failure()
        val gainImageFiles = inputImagesDir.listFiles(FileFilter {
            it.path.endsWith(gain_suffix) && !it.isHidden
        })?.sortedArray() ?: return Result.failure()

        val pairs = sdrImageFiles.zip(gainImageFiles)

        var count: Int = 0
        for ((index, pair) in pairs.withIndex()) {
            val (sdrFile, gainFile) = pair
            val fileName = sdrFile.name.substringBefore(sdr_suffix)
            val sdrBmp = ImageDecoder.createSource(sdrFile).decodeBitmap { _, _ ->
                allocator = ImageDecoder.ALLOCATOR_SOFTWARE
            }
            val gainBmp = ImageDecoder.createSource(gainFile).decodeBitmap { _, _ ->
                allocator = ImageDecoder.ALLOCATOR_SOFTWARE
            }
            if (ApiLevelUtil.isAtLeastAndroidU()) {
                val ratio = 1000f / 203f
                val gainmap = Gainmap(gainBmp).apply {
                    setRatioMin(1.0f, 1.0f, 1.0f)
                    setRatioMax(ratio, ratio, ratio)
                    setGamma(1.0f, 1.0f, 1.0f)
                    setEpsilonSdr(0.0f, 0.0f, 0.0f)
                    setEpsilonHdr(0.0f, 0.0f, 0.0f)
                    minDisplayRatioForHdrTransition = 1.0f
                    displayRatioForFullHdr = ratio
                }

                sdrBmp.gainmap = gainmap
                val outFile = File(outputImagesDir, "${fileName}_uhdr.jpg")
                IccProfile.compressBitmapToFileForJpegr(sdrBmp, 95, -1f, outFile.absolutePath)
                Log.d(TAG, "combineUhdr: sdr:${sdrFile.name},gain:${gainFile.name}, $outFile, $index")
                count++
            }
        }
        Log.d(TAG, "combineUhdr: end 2")
        return Result.success(Data.Builder().putAll(mapOf(OUTPUT_COUNT to count)).build())
    }

    companion object {
        private const val TAG = "UhdrCombineWorker"
    }
}