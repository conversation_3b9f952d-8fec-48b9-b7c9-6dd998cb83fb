package com.hu.demo.main.works.recyclerview.vhs

import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.ImageView
import android.widget.TextView
import com.hu.demo.main.R
import com.hu.demo.main.adapter.BaseVH
import com.hu.demo.main.works.recyclerview.datas.RecyclerData
import com.hu.demo.main.works.recyclerview.slidingselect.ISelectable
import com.hu.demo.main.works.recyclerview.view.IEditable

class RecyclerVH(val parent: ViewGroup) : BaseVH<RecyclerData>(parent, R.layout.item_recycler), IDraggable, ISelectable {
    private val ivImage = findViewById<ImageView>(R.id.ivImage)
    private val tvName = findViewById<TextView>(R.id.tvName)
    private val tvSize = findViewById<TextView>(R.id.tvSize)
    private val cbCheck = findViewById<CheckBox>(R.id.cbCheck)

    override var isEditMode: Boolean = false
        set(value) {
            field = value
            cbCheck.visibility = if (value) View.VISIBLE else View.GONE
        }

    override var isSelect: Boolean = false
        set(value) {
            field = value
            if (isEditMode) {
                cbCheck.isChecked = value
            }
        }

    override fun bind(extraData: Map<String, Any>, data: RecyclerData, position: Int) {
        ivImage.setImageResource(data.imgId)
        tvName.text = data.name
        tvSize.text = data.size.toString()
        isEditMode = extraData[IEditable.KEY_IS_EDIT] as? Boolean ?: false
        isSelect = extraData[ISelectable.KEY_IS_SELECT] as? Boolean ?: false
    }
}