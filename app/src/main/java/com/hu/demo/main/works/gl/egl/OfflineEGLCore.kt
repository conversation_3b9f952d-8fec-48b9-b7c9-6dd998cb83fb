/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - OfflineEGLCore.kt
 ** Description: 离屏EGL环境的创建，使用此类创建的GL环境，结果绘制在离屏的Surface上
 ** Version: 1.0
 ** Date : 2023/6/5
 ** Author: xiewujie@Apps.Gallery3D
 ** TAG: OfflineEGLCore
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  xie<PERSON><PERSON>e@Apps.Gallery3D      2023/07/03    1.0         first created
 ********************************************************************************/
package com.hu.demo.main.works.gl.egl

import android.opengl.EGL14.EGL_ALPHA_SIZE
import android.opengl.EGL14.EGL_BLUE_SIZE
import android.opengl.EGL14.EGL_CONTEXT_CLIENT_VERSION
import android.opengl.EGL14.EGL_DEFAULT_DISPLAY
import android.opengl.EGL14.EGL_GREEN_SIZE
import android.opengl.EGL14.EGL_HEIGHT
import android.opengl.EGL14.EGL_NONE
import android.opengl.EGL14.EGL_NO_CONTEXT
import android.opengl.EGL14.EGL_NO_DISPLAY
import android.opengl.EGL14.EGL_NO_SURFACE
import android.opengl.EGL14.EGL_RED_SIZE
import android.opengl.EGL14.EGL_SUCCESS
import android.opengl.EGL14.EGL_WIDTH
import android.opengl.EGL14.eglChooseConfig
import android.opengl.EGL14.eglCreateContext
import android.opengl.EGL14.eglCreatePbufferSurface
import android.opengl.EGL14.eglDestroyContext
import android.opengl.EGL14.eglDestroySurface
import android.opengl.EGL14.eglGetDisplay
import android.opengl.EGL14.eglGetError
import android.opengl.EGL14.eglInitialize
import android.opengl.EGL14.eglMakeCurrent
import android.opengl.EGL14.eglReleaseThread
import android.opengl.EGL14.eglTerminate
import android.opengl.EGLConfig
import android.opengl.EGLContext
import android.opengl.EGLDisplay
import android.opengl.EGLSurface
import android.util.Log

/**
 * 离屏EGL环境的创建，使用此类创建的GL环境，结果绘制在离屏的Surface上
 */
class OfflineEGLCore {

    private var eglDisplay: EGLDisplay? = EGL_NO_DISPLAY
    private var eglContext: EGLContext? = EGL_NO_CONTEXT
    private var eglSurface: EGLSurface? = EGL_NO_SURFACE

    private val attributeList = intArrayOf(
        EGL_RED_SIZE, DEFAULT_COLOR_DEPTH,
        EGL_GREEN_SIZE, DEFAULT_COLOR_DEPTH,
        EGL_BLUE_SIZE, DEFAULT_COLOR_DEPTH,
        EGL_ALPHA_SIZE, DEFAULT_COLOR_DEPTH,
        EGL_NONE
    )

    init {
        initEglContext()
    }

    private fun initEglContext() {
        eglDisplay = eglGetDisplay(EGL_DEFAULT_DISPLAY)
        if (eglDisplay == EGL_NO_DISPLAY) {
            Log.e(TAG, "[initEglContext] error from eglGetDisplay, errorCode:${eglGetError()}")
            return
        }
        val version = IntArray(2)
        var success = eglInitialize(eglDisplay, version, 0, version, 1)
        if (success.not()) {
            Log.e(TAG, "[initEglContext] error because eglInitialize, errorCode:${eglGetError()}")
            return
        }
        val configs = arrayOfNulls<EGLConfig>(1)
        success = eglChooseConfig(
            eglDisplay, attributeList, 0, configs,
            0, configs.size, IntArray(1), 0
        )
        if (success.not()) {
            Log.e(TAG, "[initEglContext] error from eglChooseConfig, errorCode:${eglGetError()}")
            return
        }
        val surfaceAttr = intArrayOf(
            EGL_WIDTH, 1,
            EGL_HEIGHT, 1,
            EGL_NONE
        )
        eglSurface = eglCreatePbufferSurface(eglDisplay, configs[0], surfaceAttr, 0)
        if (eglSurface == EGL_NO_SURFACE) {
            Log.e(TAG, "[initEglContext] error from eglCreatePbufferSurface, errorCode:${eglGetError()}")
            return
        }
        val attributeList = intArrayOf(EGL_CONTEXT_CLIENT_VERSION, EGL_VERSION, EGL_NONE)
        eglContext = eglCreateContext(eglDisplay, configs[0], EGL_NO_CONTEXT, attributeList, 0)
        val errorCode = eglGetError()
        if (errorCode != EGL_SUCCESS) {
            Log.e(TAG, "[initEglContext] error from eglCreateContext, errorCode:$errorCode")
        }
    }

    /**
     * 释放资源
     */
    fun release() {
        if (eglDisplay != EGL_NO_DISPLAY) {
            eglMakeCurrent(
                eglDisplay,
                EGL_NO_SURFACE,
                EGL_NO_SURFACE,
                EGL_NO_CONTEXT
            )
            eglDestroyContext(eglDisplay, eglContext)
            eglDestroySurface(eglDisplay, eglSurface)
            eglReleaseThread()
            eglTerminate(eglDisplay)
            eglDisplay = EGL_NO_DISPLAY
            eglContext = EGL_NO_CONTEXT
            eglSurface = EGL_NO_SURFACE
        }
    }

    /**
     * 将当前线程绑定为GL线程
     * @return 是否成功
     */
    fun makeCurrent(): Boolean {
        val success = eglMakeCurrent(eglDisplay, eglSurface, eglSurface, eglContext)
        if (success.not()) {
            Log.e(TAG, "[makeCurrent] error, errorCode:${eglGetError()}")
        }
        return success
    }

    companion object {
        private const val TAG = "OfflineEGLCore"

        private const val EGL_VERSION = 3
        private const val DEFAULT_COLOR_DEPTH = 8
    }
}