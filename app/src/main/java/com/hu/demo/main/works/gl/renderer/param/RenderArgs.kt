/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - RenderArgs.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/08/27
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery		2024/08/27		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.renderer.param

import android.util.Log
import androidx.collection.ArrayMap
import com.hu.demo.main.works.gl.IRecyclable
import com.hu.demo.main.works.gl.IReusable
import com.hu.demo.main.works.gl.utils.NoKeyCachePool

/**
 * 渲染参数集，在渲染时的参数存储到此对象中
 */
class RenderArgs : IReusable, IRecyclable {
    private val infos: MutableMap<NodePath, MutableMap<KeyName, Any?>> = ArrayMap()

    private val arrayMapFun = { ArrayMap<KeyName, Any?>() }

    /**
     * 根据[path]获取对应类型下的参数集合
     */
    fun getInfoByType(path: NodePath): MutableMap<KeyName, Any?> {
        return infos.getOrPut(path, arrayMapFun)
    }

    /**
     * 根据[keyName]来获取参数，并返回执行类型。由于返回值可能不存在，以及类型涉及到强转，实际的返回值是可空的。
     *
     * @param keyName 参数名称
     *
     * @return 返回指定类型的参数值，当未找到对应参数或参数类型不匹配则返回`null`
     */
    @Suppress("UNCHECKED_CAST")
    operator fun <T> get(keyName: KeyName): T? {
        return getInfoByType(keyName.nodePath)[keyName] as? T
    }

    /**
     * 根据[keyNames]依次获取数据，返回第一个不为null的值。
     *
     * @param keyNames 参数列表
     *
     * @return 返回从[keyNames]获取到的第一个不为null的值，如果根据[keyNames]获取的值都为null，则返回null
     */
    fun <T> getFirst(vararg keyNames: KeyName): T? {
        for (keyName in keyNames) {
            val rtn = get<T>(keyName)
            if (rtn != null) {
                return rtn
            }
        }
        return null
    }

    /**
     * 根据[keyName]来获取参数，并返回执行类型。使用此方法需用户能够在业务流程中保证参数是一定存在并且参数值类型匹配，返回不可空参数值。
     *
     * @param keyName 参数名称
     *
     * @return 返回指定类型的参数值
     */
    @Suppress("UNCHECKED_CAST")
    fun <T> require(keyName: KeyName): T {
        return getInfoByType(keyName.nodePath)[keyName] as T
    }

    /**
     * 设置对应的参数值
     *
     * @param keyName 参数名称
     * @param value 参数值
     */
    operator fun <T : Any> set(keyName: KeyName, value: T?) {
        getInfoByType(keyName.nodePath)[keyName] = value
    }

    /**
     * 对传入的指定参数分类批量设置参数
     *
     * @param values 参数集合
     */
    fun set(values: Map<KeyName, Any?>) {
        values.forEach { (keyName, value) ->
            set(keyName, value)
        }
    }

    /**
     * 将传入的[renderArgs]执行完全拷贝到当前参数集中。
     *
     * @param renderArgs 渲染参数集
     */
    fun set(renderArgs: RenderArgs) {
        infos.putAll(renderArgs.infos)
    }

    /**
     * 移除对应[keyName]的值
     */
    fun remove(keyName: KeyName) {
        getInfoByType(keyName.nodePath).remove(keyName)
    }

    /**
     * 移除对应[path]参数分类下的所有参数
     */
    fun remove(path: NodePath) {
        infos[path]?.clear()
    }

    /**
     * 移除所有参数，自行参数回收
     */
    override fun reuse() {
        infos.clear()
        reuse(this)
    }

    override fun recycle() = Unit

    companion object {
        private const val TAG = "RenderArgs"

        /**
         * 参数集缓存池
         */
        private val cachePool = NoKeyCachePool(8) { RenderArgs() }

        /**
         * 获取一个参数集
         */
        fun obtain(): RenderArgs {
            return cachePool.obtain()
        }

        /**
         * 回收参数集，为后续重用
         */
        private fun reuse(renderArgs: RenderArgs) {
            cachePool.reuse(renderArgs)
        }

        /**
         * 对参数集的缓存池执行清除
         */
        fun clean() {
            Log.d(TAG, "clean: is called.")
            cachePool.clean()
        }
    }
}