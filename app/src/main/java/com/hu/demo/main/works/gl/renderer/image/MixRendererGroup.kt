package com.hu.demo.main.works.gl.renderer.image

import com.hu.demo.main.works.gl.renderer.normal.FrameRenderer
import com.hu.demo.main.works.gl.renderer.normal.RendererGroup
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_IMAGE_TEXTURE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_IMAGE_TEXTURE_2
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_MIX_RATIO
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.getKey
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.NEXT_NP
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.PROCEDURAL_NP
import com.hu.demo.main.works.gl.renderer.param.RenderArgs
import com.hu.demo.main.works.gl.texture.ITexture
import com.hu.demo.main.works.gl.utils.GlProgram

class MixRendererGroup : RendererGroup(TAG) {
    init {
        addChild(new(UhdrRendererGroup::class.java))
        addChild(new(UhdrRenderer2Group::class.java))
        addChild(new(MixRenderer::class.java))
        addChild(new(FrameRenderer::class.java))
    }

    override fun render(renderArgs: RenderArgs, glProgram: GlProgram?) {
        var tex: ITexture? = null
        var tex2: ITexture? = null
        children.forEach {
            when (it.name) {
                UhdrRendererGroup.TAG -> {
                    it.performRender(renderArgs, glProgram)
                    tex = renderArgs[NEXT_NP.getKey(KEY_IMAGE_TEXTURE)]
                }
                UhdrRenderer2Group.TAG -> {
                    it.performRender(renderArgs, glProgram)
                    tex2 = renderArgs[NEXT_NP.getKey(KEY_IMAGE_TEXTURE)]
                }
                MixRenderer.TAG -> {
                    renderArgs[NEXT_NP.getKey(KEY_IMAGE_TEXTURE)] = tex
                    renderArgs[NEXT_NP.getKey(KEY_IMAGE_TEXTURE_2)] = tex2
                    renderArgs[NEXT_NP.getKey(KEY_MIX_RATIO)] = renderArgs.require<Float>(PROCEDURAL_NP.getKey(KEY_MIX_RATIO))
                    it.performRender(renderArgs, glProgram)
                }
                else -> {
                    renderArgs.remove(NEXT_NP.getKey(KEY_MIX_RATIO))
                    it.performRender(renderArgs, glProgram)
                }
            }
        }
    }

    companion object {
        private const val TAG = "MixRendererGroup"
    }
}