package com.hu.demo.main.works.pdf

import android.content.Context
import android.graphics.BitmapFactory
import android.graphics.BitmapFactory.Options
import android.net.Uri
import android.os.ParcelFileDescriptor
import android.system.Os
import android.system.OsConstants
import android.util.Log
import com.hu.demo.main.works.pdf.sdk.PdfDocument
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream

object PdfCreator2 {
    private const val TAG = "PdfCreator"
    const val DEFAULT_A4_WIDTH = 3000
    const val DEFAULT_A4_HEIGHT = 4000
    private const val DEFAULT_SPACING = 20
    private const val PDF_SUFFIX = ".pdf"
    suspend fun savePdfToExternal(context: Context, uriList: List<Uri>, saveDir: File, callback: (String) -> Unit): File? {
        return withContext(Dispatchers.IO) {
            saveDir.mkdirs()
            val pdfFile = File(saveDir, "${System.currentTimeMillis()}$PDF_SUFFIX")
            if (uriList.isEmpty()) {
                Log.e(TAG, "savePdfToExternal: bitmap list is empty.")
                return@withContext null
            }
            val fos = FileOutputStream(pdfFile)
            val document = PdfDocument(fos)
            document.start()
            var page: PdfDocument.Page? = null
            runCatching {
                var pageNumber = 1
                Log.d(TAG, "savePdfToExternal: start write. total ${uriList.size}.")
                for (uri in uriList) {
                    Log.d(TAG, "savePdfToExternal: write page $pageNumber .")
                    context.contentResolver.openFileDescriptor(uri, "r")?.use {
                        val imageWh = getImageWh(it)
                        val pageInfo = PdfDocument.PageInfo.Builder(imageWh.first, imageWh.second).build()
                        page = document.startPage(pageInfo)
                        val size = it.statSize
                        page?.write(size, FileInputStream(it.fileDescriptor))
                    }
                    document.finishPage(page)
                }
                callback("写入文件")
                Log.d(TAG, "savePdfToExternal: end write. total.")
            }.onFailure {
                Log.e(TAG, "savePdfToExternal save error", it)
                document.finishPage(page)
            }
            document.finish()
            document.close()

            pdfFile
        }
    }

    private fun getImageWh(it: ParcelFileDescriptor): Pair<Int, Int> {
        val option = Options().apply { inJustDecodeBounds = true }
        BitmapFactory.decodeFileDescriptor(it.fileDescriptor, null, option)
        Os.lseek(it.fileDescriptor, 0, OsConstants.SEEK_SET)
        return option.outWidth to option.outHeight
    }
}