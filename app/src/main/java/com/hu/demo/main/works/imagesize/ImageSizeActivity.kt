package com.hu.demo.main.works.imagesize

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.EditText
import android.widget.ImageView
import android.widget.SeekBar
import android.widget.SeekBar.OnSeekBarChangeListener
import android.widget.TextView
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.contract.ActivityResultContract
import androidx.core.view.updateLayoutParams
import androidx.core.widget.doAfterTextChanged
import com.hu.demo.main.R
import com.hu.demo.utils.setImageUriAny
import com.hu.demo.base.ui.BaseActivity
import kotlinx.coroutines.Job

class ImageSizeActivity : BaseActivity(), View.OnClickListener {
    private var selectUri: Uri? = null
    private var ivImage: ImageView? = null
    private var etViewWidth: EditText? = null
    private var etViewHeight: EditText? = null
    private var tvBitmapMaxWh: TextView? = null
    private var sbMaxWh: SeekBar? = null
    private var job: Job? = null
    private val pickTypeResultContract = PickTypeResultContract()
    private val pickImageLauncher = registerForActivityResult(
        pickTypeResultContract,
        PickOnlyCallback { changeData(it, sbMaxWh!!.progress) }
    )

    override fun getLayoutId(): Int {
        return R.layout.activity_image_size
    }

    override fun initToolbar() {
        super.initToolbar()
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowTitleEnabled(true)
            setDisplayShowHomeEnabled(false)
        }
    }

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        ivImage = findViewById(R.id.ivImage)
        etViewWidth = findViewById(R.id.etViewWidth)
        etViewHeight = findViewById(R.id.etViewHeight)
        tvBitmapMaxWh = findViewById(R.id.tvBitmapMaxWh)
        sbMaxWh = findViewById(R.id.sbMaxWh)
    }

    override fun initEvent(savedInstanceState: Bundle?) {
        super.initEvent(savedInstanceState)
        ivImage?.setOnClickListener(this)
        etViewWidth?.doAfterTextChanged { text ->
            ivImage?.updateLayoutParams {
                text.toString().toIntOrNull()?.also { width = it }
            }
        }
        etViewHeight?.doAfterTextChanged { text ->
            ivImage?.updateLayoutParams {
                text.toString().toIntOrNull()?.also { height = it }
            }
        }
        sbMaxWh?.setOnSeekBarChangeListener(object : OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar, progress: Int, fromUser: Boolean) {
                changeData(selectUri, progress)
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) = Unit

            override fun onStopTrackingTouch(seekBar: SeekBar?) = Unit

        })
        ivImage?.updateLayoutParams {
            etViewWidth?.text?.toString()?.toIntOrNull()?.also { width = it }
            etViewHeight?.text?.toString()?.toIntOrNull()?.also { height = it }
        }
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.ivImage -> {
                pickImageLauncher.launch(MIME_TYPE)
            }
        }
    }

    private fun changeData(obj: Any?, progress: Int) {
        val ivImage = ivImage ?: return
        this.selectUri = obj as Uri
        tvBitmapMaxWh?.text = progress.toString()
        ivImage.setImageUriAny(obj, progress)
    }

    private open class PickOnlyCallback(private val resultFunc: (Uri) -> Unit) : ActivityResultCallback<Uri?> {
        override fun onActivityResult(result: Uri?) {
            result ?: run {
                Log.d(TAG, "PickOnlyCallback.onActivityResult: ")
                return
            }
            Log.d(TAG, "onActivityResult: $result")
            resultFunc(result)
        }
    }

    private class PickTypeResultContract : ActivityResultContract<String, Uri?>() {
        override fun createIntent(context: Context, input: String): Intent {
            return Intent(Intent.ACTION_PICK).setType(input)
        }

        override fun parseResult(resultCode: Int, intent: Intent?): Uri? {
            return if (resultCode != Activity.RESULT_OK) null else intent?.data
        }
    }


    companion object {
        private const val TAG = "DecodeActivity"
        private const val MIME_TYPE = "image/*"

        private const val TYPE_DEFAULT = 0
    }
}