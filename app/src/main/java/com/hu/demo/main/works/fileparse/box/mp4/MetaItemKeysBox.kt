package com.hu.demo.main.works.fileparse.box.mp4

import com.hu.demo.main.works.fileparse.IRead
import com.hu.demo.main.works.fileparse.box.Box
import com.hu.demo.main.works.fileparse.box.FullBox
import com.hu.demo.main.works.fileparse.box.Tree
import com.hu.demo.utils.ByteOrderedDataInputStream
import com.hu.demo.utils.beautyToString

internal class MetaItemKeysBox(parent: Tree) : FullBox("keys", 3, parent) {
    var entryCount: Int? = null
    var entries: Array<Item>? = null

    override fun readInner(bis: ByteOrderedDataInputStream) {
        super.readInner(bis)
        entryCount = bis.nReadInt()
        entries = Array(entryCount!!) {
            Item().apply { read(bis) }
        }
    }

    override fun fork(parent: Tree): Box {
        return MetaItemKeysBox(parent)
    }
    override fun toString(): String {
        return "${super.toString()}, entryCount=$entryCount, entries=${entries?.beautyToString()})"
    }

    inner class Item : IRead {
        var size: Int? = null
        var namespace: String? = null
        var value: String? = null
        override fun read(bis: ByteOrderedDataInputStream) {
            size = bis.nReadInt()
            namespace = bis.nReadBytes(4).decodeToString()
            value = bis.nReadBytes(size!! - 8).decodeToString()
        }

        override fun toString(): String {
            return "Item(keySize:$size, keyNamespace:$namespace, keyValue:$value)"
        }
    }
}