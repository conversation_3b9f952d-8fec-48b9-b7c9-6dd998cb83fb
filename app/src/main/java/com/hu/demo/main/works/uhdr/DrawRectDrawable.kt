package com.hu.demo.main.works.uhdr

import android.graphics.Bitmap
import android.graphics.BitmapShader
import android.graphics.Canvas
import android.graphics.ColorFilter
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.PixelFormat
import android.graphics.RectF
import android.graphics.Shader
import android.graphics.drawable.Drawable
import android.os.Build
import androidx.annotation.RequiresApi
import androidx.core.graphics.toRectF

@RequiresApi(Build.VERSION_CODES.S)
class DrawRectDrawable(private val bitmap: Bitmap) : Drawable() {
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG).also {
        it.shader = BitmapShader(bitmap, Shader.TileMode.DECAL, Shader.TileMode.DECAL)
    }
    private val matrix = Matrix()

    override fun draw(canvas: Canvas) {
        matrix.setRectToRect(
            RectF(0f, 0f, bitmap.width.toFloat(), bitmap.height.toFloat()),
            bounds.toRectF(),
            Matrix.ScaleToFit.CENTER
        )
        paint.shader?.setLocalMatrix(matrix)
        canvas.drawRect(bounds, paint)
    }

    override fun setAlpha(alpha: Int) = Unit

    override fun setColorFilter(filter: ColorFilter?) = Unit

    @Deprecated("Deprecated in Java")
    override fun getOpacity(): Int = PixelFormat.TRANSPARENT
}