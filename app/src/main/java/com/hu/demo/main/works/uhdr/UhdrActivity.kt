package com.hu.demo.main.works.uhdr

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.BitmapFactory.Options
import android.graphics.BitmapRegionDecoder
import android.graphics.Gainmap
import android.graphics.Rect
import android.graphics.drawable.Drawable
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.Display
import android.view.View
import android.view.View.OnClickListener
import android.widget.AdapterView
import android.widget.AdapterView.OnItemSelectedListener
import android.widget.Button
import android.widget.Spinner
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.contract.ActivityResultContract
import androidx.annotation.RequiresApi
import androidx.lifecycle.lifecycleScope
import com.hu.demo.base.ui.BaseActivity
import com.hu.demo.main.R
import com.hu.demo.utils.ApiLevelUtil
import com.hu.demo.utils.decodeImage
import kotlinx.coroutines.launch
import java.util.function.Consumer

@RequiresApi(Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
class UhdrActivity : BaseActivity(), OnClickListener {
    private var drawMode: DrawMode = DrawMode.DRAW_BITMAP
    private var selectUri: Uri? = null
    private var selectGainUri: Uri? = null
    private var tvSdrHdrRatio: TextView? = null
    private var cgGrid: ColorGridView? = null
    private var ivImage: ImageShowView? = null
    private var btnSelectGain: Button? = null
    private var btnSelect: Button? = null
    private var spMode: Spinner? = null
    private var spDrawMode: Spinner? = null
    private var status: BitmapStatus = BitmapStatus.SDR
        set(value) {
            field = value
            Toast.makeText(this, value.toString(), Toast.LENGTH_SHORT).show()
        }
    private val pickTypeResultContract = PickTypeResultContract()
    private val pickImageLauncher = registerForActivityResult(
        pickTypeResultContract,
        PickOnlyCallback { changeData(it) }
    )
    private val pickGainLauncher = registerForActivityResult(
        pickTypeResultContract,
        PickOnlyCallback { changeGainData(it) }
    )
    private val hdrSdrListener = Consumer<Display> { display ->
        updateModeInfoDisplay()
        ivImage?.postInvalidate()
    }

    override fun getLayoutId(): Int {
        return R.layout.activity_uhdr
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        if (display?.isHdrSdrRatioAvailable == true) {
            display?.registerHdrSdrRatioChangedListener({ it.run() }, hdrSdrListener)
        }
    }

    override fun initToolbar() {
        super.initToolbar()
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowTitleEnabled(true)
            setDisplayShowHomeEnabled(false)
        }
    }

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        tvSdrHdrRatio = findViewById(R.id.tvSdrHdrRatio)
        cgGrid = findViewById(R.id.cgGrid)
        ivImage = findViewById(R.id.ivImage)
        btnSelect = findViewById(R.id.btnSelect)
        spMode = findViewById(R.id.spMode)
        spDrawMode = findViewById(R.id.spDrawMode)
        btnSelectGain = findViewById(R.id.btnSelectGain)
    }

    override fun initEvent(savedInstanceState: Bundle?) {
        super.initEvent(savedInstanceState)
        btnSelect?.setOnClickListener(this)
        btnSelectGain?.setOnClickListener(this)
        spMode?.onItemSelectedListener = object : OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                window.colorMode = ColorMode.entries[position].colorModeHdr
                Log.d(TAG, "onItemSelected: ${window.colorMode}")
            }

            override fun onNothingSelected(parent: AdapterView<*>?) = Unit
        }
        spDrawMode?.onItemSelectedListener = object : OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                drawMode = DrawMode.entries[position]
                ivImage?.setImageDrawable(getDrawable())
            }

            override fun onNothingSelected(parent: AdapterView<*>?) = Unit
        }
        ivImage?.setOnClickListener(this)
        Log.d(TAG, "initEvent: isHdrSdrRatioAvailable:${display!!.isHdrSdrRatioAvailable}, ${display!!.hdrCapabilities}")
    }

    override fun onClick(view: View) {
        when (view.id) {
            R.id.btnSelect -> {
                pickImageLauncher.launch(MIME_TYPE)
            }

            R.id.btnSelectGain -> {
                pickGainLauncher.launch(MIME_TYPE)
            }

            R.id.ivImage -> {
                status = BitmapStatus.values()[(status.ordinal + 1) % BitmapStatus.values().size]
                ivImage?.setImageDrawable(getDrawable())
            }
        }
    }

    private fun updateModeInfoDisplay() {
        lifecycleScope.launch {
            Log.d(TAG, "HDR/SDR changed ${display.hdrSdrRatio}")
            val colormode = window.colorMode
            val mode = "ColorMode:" + when (colormode) {
                ActivityInfo.COLOR_MODE_DEFAULT -> "SDR | Ratio = "

                ActivityInfo.COLOR_MODE_HDR -> "HDR | Ratio = "

                else -> "Unknown | Ratio = "
            } + "${display.hdrSdrRatio}"
            tvSdrHdrRatio?.text = mode
        }
    }

    private fun changeData(obj: Any?) {
        val ivImage = ivImage ?: return
        this.selectUri = obj as Uri
        status = BitmapStatus.HDR
        ivImage.setImageDrawable(getDrawable())

        if (ApiLevelUtil.isAtLeastAndroidU() && !obj.decodeImage(this, 10).hasGainmap()) {
            btnSelectGain?.visibility = View.VISIBLE
        } else {
            btnSelectGain?.visibility = View.GONE
        }
    }

    private fun changeGainData(obj: Any?) {
        this.selectGainUri = obj as Uri
        ivImage?.setImageDrawable(getDrawable())
    }

    @SuppressLint("NewApi")
    private fun getDrawable(): Drawable? {
        val selectUri = selectUri ?: return null
        return contentResolver.openInputStream(selectUri)?.use { inputS ->
            if (drawMode in arrayOf(DrawMode.DRAW_BITMAP, DrawMode.DRAW_SHADER, DrawMode.DRAW_RECT)) {
                BitmapFactory.decodeStream(inputS)?.let { bitmap ->
                    getGain()?.apply {
                        bitmap.gainmap = this
                    }
                    val result = getBitmapByStatus(bitmap) ?: return@let null
                    when (drawMode) {
                        DrawMode.DRAW_BITMAP -> DrawBitmapDrawable(result)
                        DrawMode.DRAW_SHADER -> DrawShaderDrawable(this, result)

                        else -> DrawRectDrawable(result)
                    }
                }
            } else {
                DrawRegionDrawable(
                    BitmapRegionDecoder.newInstance(inputS)?.let { decoder ->
                        val size = 2048
                        val rowCount = (decoder.width + size - 1) / size
                        val colCount = (decoder.height + size - 1) / size
                        Array(colCount) { arrayOfNulls<Pair<Rect, Bitmap>>(rowCount) }.tile { colIndex, rowIndex ->
                            val options = Options().apply {
                                inBitmap = Bitmap.createBitmap(size, size, Bitmap.Config.ARGB_8888)
                            }
                            val rect = Rect(rowIndex * size, colIndex * size, (rowIndex + 1) * size, (colIndex + 1) * size)
                            val result = getBitmapByStatus(decoder.decodeRegion(rect, options))
                            if (result != null) {
                                rect to result
                            } else null
                        }
                    }
                )
            }
        }
    }

    @RequiresApi(Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
    private fun getGain(): Gainmap? {
        val selectGainUri = selectGainUri ?: return null
        val bitmap = contentResolver.openInputStream(selectGainUri)?.use { inputS ->
            BitmapFactory.decodeStream(inputS)
        } ?: return null

        val ratio = 1000f / 203f
        return Gainmap(bitmap).apply {
            setRatioMin(1.0f, 1.0f, 1.0f)
            setRatioMax(ratio, ratio, ratio)
            setGamma(1.0f, 1.0f, 1.0f)
            setEpsilonSdr(0.0f, 0.0f, 0.0f)
            setEpsilonHdr(0.0f, 0.0f, 0.0f)
            minDisplayRatioForHdrTransition = 1.0f
            displayRatioForFullHdr = ratio
        }
    }

    @SuppressLint("NewApi")
    private fun getBitmapByStatus(bitmap: Bitmap): Bitmap? {
        return when (status) {
            BitmapStatus.HDR -> bitmap
            BitmapStatus.SDR -> bitmap.apply { gainmap = null }
            BitmapStatus.GAINMAP -> bitmap.run { gainmap?.gainmapContents }
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        display?.unregisterHdrSdrRatioChangedListener(hdrSdrListener)
    }

    private enum class BitmapStatus {
        HDR,
        SDR,
        GAINMAP,
    }

    private open class PickOnlyCallback(private val resultFunc: (Uri) -> Unit) : ActivityResultCallback<Uri?> {
        override fun onActivityResult(result: Uri?) {
            result ?: run {
                Log.d(TAG, "PickOnlyCallback.onActivityResult: ")
                return
            }
            Log.d(TAG, "onActivityResult: $result")
            resultFunc(result)
        }
    }

    private class PickTypeResultContract : ActivityResultContract<String, Uri?>() {
        override fun createIntent(context: Context, input: String): Intent {
            return Intent(Intent.ACTION_PICK).setType(input)
        }

        override fun parseResult(resultCode: Int, intent: Intent?): Uri? {
            return if (resultCode != Activity.RESULT_OK) null else intent?.data
        }
    }

    private fun <T> Array<Array<T>>.tile(func: (colIndex: Int, rowIndex: Int) -> T): Array<Array<T>> {
        val colCount = size
        for (colIndex in 0 until colCount) {
            val rowCount = this[colIndex].size
            for (rowIndex in 0 until rowCount) {
                this[colIndex][rowIndex] = func(colIndex, rowIndex)
            }
        }
        return this
    }

    enum class DrawMode {
        DRAW_BITMAP,
        DRAW_RECT,
        DRAW_SHADER,
        DRAW_REGION
    }

    enum class ColorMode(val colorModeHdr: Int) {
        SRGB(ActivityInfo.COLOR_MODE_DEFAULT),
        WG(ActivityInfo.COLOR_MODE_WIDE_COLOR_GAMUT),
        HDR(ActivityInfo.COLOR_MODE_HDR),
        HDR10(3),
    }

    companion object {
        private const val TAG = "ColorModeActivity"
        private const val MIME_TYPE = "image/*"
    }
}