/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - HdrMode.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/11/12
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * huca<PERSON><PERSON>@Apps.Gallery		2024/11/12		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.renderer.brighten

import android.util.Log
import androidx.graphics.surface.SurfaceControlCompat
import com.hu.demo.main.works.gl.EditingSurfaceView
import com.hu.demo.main.works.gl.renderer.param.RenderArgs

/**
 * 针对提亮模式的管理类，此类提供防烧瓶逻辑、用户触摸激活提亮逻辑等
 *
 * @param view 渲染的SurfaceView
 */
class HdrModeManager(
    private val view: EditingSurfaceView,
    private val hdrModes: Array<ISubHdrMode> = arrayOf(CombineImageHdrMode(), VideoHdrMode(), SdrMode()),
) : IHdrMode {

    /**
     * 当前的提亮模式
     */
    private var currentMode: ISubHdrMode? = null


    override fun startShow() {
        registerObserver()

        currentMode?.startShow()
    }

    override fun render(
        renderArgs: RenderArgs,
        surfaceControl: SurfaceControlCompat,
        transaction: SurfaceControlCompat.Transaction,
    ) {
        val hdrMode = hdrModes.find { it.checkHandle(renderArgs) }
        if (currentMode != hdrMode) {
            Log.d(TAG, "render: lastMode: $currentMode, current: $hdrMode")
            currentMode?.unrender(renderArgs, surfaceControl, transaction)
            currentMode?.stopShow()
            hdrMode?.startShow()
            currentMode = hdrMode
        }
        currentMode?.render(renderArgs, surfaceControl, transaction)
    }

    override fun stopShow() {
        unregisterObserver()

        currentMode?.stopShow()

        invalidate()
    }

    /**
     * 请求重写刷帧，通过调用[EditingSurfaceView.postRender]方法触发重写刷帧
     */
    private fun invalidate() {
        view.postRender()
    }

    private fun registerObserver() {
        Log.d(TAG, "registerObserver: is called.")
    }

    private fun unregisterObserver() {
        Log.d(TAG, "unregisterObserver: is called.")
    }

    /**
     * 下hdr参数的配置类
     *
     * @param updateDrawable 是否刷新增益图
     * @param updateDrawable 是否刷新edr参数
     * @param needAnim 是否带动画更新
     */
    class InvalidateConfig(
        private var updateDrawable: Boolean = true,
        private var updateEdrParams: Boolean = true,
        private var needAnim: Boolean = true,
    ) {
        /**
         * 获取是否需要强制刷新，代表刷新增益图
         *
         * @param consume 是否消费[updateDrawable]值
         */
        fun getUpdateDrawable(consume: Boolean = false): Boolean {
            return updateDrawable.apply {
                if (consume) {
                    updateDrawable = false
                }
            }
        }

        /**
         * 获取是否刷新edr参
         *
         * @param consume 是否消费[updateEdrParams]值
         */
        fun getUpdateEdrParams(consume: Boolean = false): Boolean {
            return updateEdrParams.apply {
                if (consume) {
                    updateEdrParams = false
                }
            }
        }

        /**
         * 获取是否刷新edr参
         *
         * @param consume 是否消费[needAnim]值
         */
        fun getNeedAnim(consume: Boolean = false): Boolean {
            return needAnim.apply {
                if (consume) {
                    needAnim = false
                }
            }
        }

        /**
         * 刷新配置数据
         *
         * @param updateDrawable 更新updateDrawable的值
         * @param updateEdrParams 更新updateEdrParams的值
         * @param needAnim 更新needAnim的值
         */
        fun update(
            updateDrawable: Boolean = this.updateDrawable,
            updateEdrParams: Boolean = this.updateEdrParams,
            needAnim: Boolean = this.needAnim,
        ) {
            this.updateDrawable = updateDrawable
            this.updateEdrParams = updateEdrParams
            this.needAnim = needAnim
        }
    }

    companion object {
        private const val TAG = "HdrModeManager"

        /**
         * HDR状态下，静置后，等待5分钟时间触发烧屏警告
         */
        private const val HDR_IMAGE_SCREEN_BURN_IN_WARN_DELAY_TIME = 5 * 60 * 1000L
    }
}