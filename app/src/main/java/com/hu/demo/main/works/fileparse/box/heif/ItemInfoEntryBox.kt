package com.hu.demo.main.works.fileparse.box.heif

import com.hu.demo.main.works.fileparse.IRead
import com.hu.demo.main.works.fileparse.box.Box
import com.hu.demo.main.works.fileparse.box.FullBox
import com.hu.demo.main.works.fileparse.box.Tree
import com.hu.demo.utils.ByteOrderedDataInputStream
import com.hu.demo.utils.toByteString
import com.hu.demo.utils.toHexString

internal class ItemInfoEntryBox(parent: Tree?) : FullBox("infe", 3, parent) {
    // version 0
    var itemId: Int? = null
        private set
    var itemProtectionIndex: Short? = null
        private set
    var itemName: String? = null
        private set
    var contentType: String? = null
        private set
    var contentEncoding: String? = null
        private set

    // version 1
    var extensionType: Int? = null
        private set
    var extension: FDItemInfoExtension? = null
        private set

    // version 2
    var itemType: String? = null
        private set
    var itemUriType: String? = null
        private set

    override fun readInner(bis: ByteOrderedDataInputStream) {
        super.readInner(bis)
        val version = version?.toInt() ?: return
        if (version == 0 || version == 1) {
            itemId = bis.nReadShort().toInt()
            itemProtectionIndex = bis.nReadShort()
            itemName = bis.nReadUTF()
            contentType = bis.nReadUTF()
            if (hasNumByteLeft()) { // optional
                contentEncoding = bis.nReadUTF()
            }
        }
        if (version == 1) {
            if (hasNumByteLeft()) { // optional
                extensionType = bis.nReadInt()
            }
            if (hasNumByteLeft()) {
                extension = FDItemInfoExtension(extensionType!!).apply { read(bis) }
            }
        }
        if (version >= 2) {
            if (version == 2) {
                itemId = bis.nReadShort().toInt()
            } else if (version == 3) {
                itemId = bis.nReadInt()
            }
            itemProtectionIndex = bis.nReadShort()
            itemType = bis.nReadInt().toByteString()
            itemName = bis.nReadUTF()
            if (itemType == "mime") {
                contentType = bis.nReadUTF()
                if (hasNumByteLeft()) { // optional
                    contentEncoding = bis.nReadUTF()
                }
            } else if (itemType == "uri ") {
                itemUriType = bis.nReadUTF()
            }
        }
    }

    override fun fork(parent: Tree): Box {
        return ItemInfoEntryBox(parent)
    }

    override fun toString(): String {
        val version = version!!.toInt()
        return when (version) {
            0 -> {
                "${super.toString()}, itemId:${itemId?.toHexString()}, itemProtectionIndex:$itemProtectionIndex, itemName:$itemName, " +
                        "contentType:$contentType, contentEncoding:$contentEncoding"
            }

            1 -> {
                "${super.toString()}, itemId:${itemId?.toHexString()}, itemProtectionIndex:$itemProtectionIndex, itemName:$itemName, " +
                        "contentType:$contentType, contentEncoding:$contentEncoding, extensionType:${extensionType?.toByteString()}"
            }

            else -> {
                "${super.toString()}, itemId:${itemId?.toHexString()}, itemProtectionIndex:$itemProtectionIndex, itemName:$itemName, " +
                        "contentType:$contentType, contentEncoding:$contentEncoding, " +
                        "extensionType:${extensionType?.toByteString()}, itemType:$itemType, itemUriType:$itemUriType"
            }
        }
    }

    inner class FDItemInfoExtension(val type: Int) : IRead {
        var contentLocation: String? = null
        var contentMd5: String? = null
        var contentLength: Long? = null
        var transferLength: Long? = null
        var entryCount: Short? = null
        var groupIds: Array<Int>? = null
        override fun read(bis: ByteOrderedDataInputStream) {
            contentLocation = bis.nReadUTF()
            contentMd5 = bis.nReadUTF()
            contentLength = bis.nReadLong()
            transferLength = bis.nReadLong()
            entryCount = bis.readByte().toUByte().toShort()
            groupIds = Array(entryCount!!.toInt()) {
                bis.nReadInt()
            }
        }

        override fun toString(): String {
            return "type:$type, contentLocation:$contentLocation, contentMd5:$contentMd5, contentLength:${contentLength?.toHexString()}, " +
                    "transferLength:${transferLength?.toHexString()}, entryCount:${entryCount?.toHexString()}, groupIds:${groupIds?.contentToString()})"
        }

    }
}