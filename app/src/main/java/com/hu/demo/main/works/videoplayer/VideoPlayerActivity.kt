package com.hu.demo.main.works.videoplayer

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.VibrationEffect
import android.os.VibrationEffect.EFFECT_TICK
import android.os.Vibrator
import android.util.Log
import android.view.MotionEvent
import android.view.VelocityTracker
import android.view.View
import android.view.View.OnClickListener
import android.widget.AdapterView
import android.widget.AdapterView.OnItemSelectedListener
import android.widget.Button
import android.widget.SeekBar
import android.widget.SeekBar.OnSeekBarChangeListener
import android.widget.Spinner
import android.widget.TextView
import android.widget.ToggleButton
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.contract.ActivityResultContract
import androidx.annotation.RequiresApi
import com.hu.demo.base.ui.BaseActivity
import com.hu.demo.main.R
import com.oplus.tblplayer.retriever.TBLMediaMetadataRetriever
import kotlin.math.abs

class VideoPlayerActivity : BaseActivity(), OnClickListener {
    private var selectUri: Uri? = null
    private var tvCurrentVelocity: TextView? = null
    private var pvPlayer: PlayerView? = null
    private var tvTrim: TrimView? = null
    private var btnSelect: Button? = null
    private var tvRange: TextView? = null
    private var sbRange: SeekBar? = null
    private var tvVelocity: TextView? = null
    private var sbVelocity: SeekBar? = null
    private var tvRotation: TextView? = null
    private var sbRotation: SeekBar? = null
    private var tvScale: TextView? = null
    private var sbScale: SeekBar? = null
    private var spPinner: Spinner? = null
    private var tbColorMode: ToggleButton? = null
    private val pickTypeResultContract = PickTypeResultContract()
    private val pickVideoLauncher = registerForActivityResult(
        pickTypeResultContract,
        PickOnlyCallback { changeData(it) }
    )

    override fun getLayoutId(): Int {
        return R.layout.activity_video_player
    }

    override fun initToolbar() {
        super.initToolbar()
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowTitleEnabled(true)
            setDisplayShowHomeEnabled(false)
        }
    }

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        tvCurrentVelocity = findViewById(R.id.tvCurrentVelocity)
        pvPlayer = findViewById(R.id.pvPlayer)
        tvTrim = findViewById(R.id.tvTrim)
        tvRange = findViewById(R.id.tvRange)
        sbRange = findViewById(R.id.sbRange)
        tvVelocity = findViewById(R.id.tvVelocity)
        sbVelocity = findViewById(R.id.sbVelocity)
        tvVelocity = findViewById(R.id.tvVelocity)
        sbRotation = findViewById(R.id.sbRotation)
        tvScale = findViewById(R.id.tvScale)
        sbScale = findViewById(R.id.sbScale)
        btnSelect = findViewById(R.id.btnSelect)
        spPinner = findViewById(R.id.spPinner)
        tbColorMode = findViewById(R.id.tbColorMode)
    }

    @RequiresApi(Build.VERSION_CODES.S)
    @SuppressLint("ClickableViewAccessibility")
    override fun initEvent(savedInstanceState: Bundle?) {
        super.initEvent(savedInstanceState)
        btnSelect?.setOnClickListener(this)
        sbRange?.setOnSeekBarChangeListener(object : OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                tvRange?.text = "范围:$progress"
                tvTrim?.setRange(progress)
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) = Unit

            override fun onStopTrackingTouch(seekBar: SeekBar?) = Unit

        })
        sbVelocity?.setOnSeekBarChangeListener(object : OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                tvVelocity?.text = "速度:$progress"
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) = Unit

            override fun onStopTrackingTouch(seekBar: SeekBar?) = Unit

        })
        sbRotation?.setOnSeekBarChangeListener(object : OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar, progress: Int, fromUser: Boolean) {
                tvRotation?.text = "旋转:$progress"
                pvPlayer?.rotationSurface(progress.toFloat())
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) = Unit

            override fun onStopTrackingTouch(seekBar: SeekBar?) = Unit

        })
        sbScale?.setOnSeekBarChangeListener(object : OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar, progress: Int, fromUser: Boolean) {
                tvScale?.text = "缩放:$progress"
                val maxScale = 5.0f
                pvPlayer?.scaleSurface(progress * maxScale / seekBar.max)
            }

            override fun onStartTrackingTouch(seekBar: SeekBar) {
            }

            override fun onStopTrackingTouch(seekBar: SeekBar) {
            }

        })
        val tracker = VelocityTracker.obtain()
        val effect = VibrationEffect.createPredefined(EFFECT_TICK)
        var lastInRange = true
        tvTrim?.setOnTouchListener { v, event ->
            tracker!!.addMovement(event)
            when (event.actionMasked) {
                MotionEvent.ACTION_DOWN -> {
                    pvPlayer?.pause()
                    lastInRange = false
                }

                MotionEvent.ACTION_MOVE -> {
                    val percent = event.x / v.width
                    pvPlayer?.seek(percent)
                    tvTrim?.setWhiteLine(percent)
                    val range = tvTrim!!.getRange()
                    val isInRange = (percent * tvTrim!!.width).toInt() in range
                    if (!isInRange) {
                        lastInRange = false
                        tvTrim?.setAdsorption(false)
                    }
                    tracker.computeCurrentVelocity(VELOCITY_UNITS)
                    tvCurrentVelocity?.text = "px/s: ${abs(tracker.xVelocity.toInt())}"
                    Log.d(TAG, "initEvent: ${(percent * tvTrim!!.width).toInt()}, $range, ${tracker.xVelocity}, ${sbVelocity!!.progress}")
                    if (isInRange && abs(tracker.xVelocity) <= sbVelocity!!.progress && !lastInRange) {
                        getSystemService(Vibrator::class.java).vibrate(effect)
                        lastInRange = true
                        tvTrim?.setAdsorption(true)
                    }
                }

                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    pvPlayer?.play()
                    tracker.clear()
                    selectUri?.also { uri ->
                        contentResolver.openFileDescriptor(uri, "r")?.use {
                            val bitmap = TBLMediaMetadataRetriever().apply {
                                setDataSource(it.fileDescriptor)
                            }.getFrameAtTime(100)
                            Log.e(TAG, "initEvent: ${bitmap.colorSpace}")
                        }
                    }

                }
            }
            true
        }
        spPinner?.onItemSelectedListener = object : OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                pvPlayer?.changeVideoView(position)
            }

            override fun onNothingSelected(parent: AdapterView<*>?) = Unit

        }
        pvPlayer?.changeVideoView(spPinner!!.selectedItemPosition)
        tvTrim?.setRange(sbRange!!.progress)

        tbColorMode?.setOnCheckedChangeListener { buttonView, isChecked ->
            window?.colorMode = if (isChecked) ActivityInfo.COLOR_MODE_HDR else ActivityInfo.COLOR_MODE_DEFAULT
        }
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.btnSelect -> {
                pickVideoLauncher.launch(MIME_TYPE)
            }
        }
    }

    private fun changeData(obj: Any?) {
        this.selectUri = obj as Uri
        pvPlayer?.setVideo(obj)
        tvTrim?.setVideo(obj)
    }

    private open class PickOnlyCallback(private val resultFunc: (Uri) -> Unit) : ActivityResultCallback<Uri?> {
        override fun onActivityResult(result: Uri?) {
            result ?: run {
                Log.d(TAG, "PickOnlyCallback.onActivityResult: ")
                return
            }
            Log.d(TAG, "onActivityResult: $result")
            resultFunc(result)
        }
    }

    private class PickTypeResultContract : ActivityResultContract<String, Uri?>() {
        override fun createIntent(context: Context, input: String): Intent {
            return Intent(Intent.ACTION_PICK).setType(input)
        }

        override fun parseResult(resultCode: Int, intent: Intent?): Uri? {
            return if (resultCode != Activity.RESULT_OK) null else intent?.data
        }
    }

    companion object {
        private const val TAG = "VideoPlayerActivity"
        private const val MIME_TYPE = "video/*"
        private const val VELOCITY_UNITS = 1000
    }

}