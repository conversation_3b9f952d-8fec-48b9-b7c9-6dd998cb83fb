package com.hu.demo.main.works.fileparse.box.jpg

import com.google.common.base.Ascii.NUL
import com.hu.demo.main.works.fileparse.box.Tree
import com.hu.demo.utils.ByteOrderedDataInputStream
import java.io.ByteArrayOutputStream
import java.io.InputStream
import kotlin.math.min

internal open class IdentifierSection(parent: Tree, private val identifierMaxLength: Int = Int.MAX_VALUE) : SizeSection(parent) {
    protected var identifier: ByteArray? = null

    override fun read(bis: ByteOrderedDataInputStream) {
        super.read(bis)
        val contentSize = min(allSize - alreadyReadSize.toInt(), identifierMaxLength)
        identifier = bis.getIdentifier(contentSize).apply { alreadyReadSize += this.size + 1 }
    }

    override fun toString(): String {
        return "${super.toString()}\nidentifier=${identifier?.decodeToString()}"
    }
}

private fun <T : InputStream> T.getIdentifier(maxLength: Int = Int.MAX_VALUE): ByteArray {
    val bis = ByteArrayOutputStream()
    val buffer = ByteArray(1)
    while (read(buffer) > 0 && (bis.size() < maxLength) && buffer[0] != NUL) {
        bis.write(buffer, 0, buffer.size)
    }
    return bis.toByteArray()
}
