package com.hu.demo.main.works.multicache

import android.util.Log
import java.util.concurrent.ConcurrentHashMap

class MemoryCache<K, V> : ICache<K, V> {

    private val cache = ConcurrentHashMap<K, V>()

    override val name: String
        get() = NAME

    override fun get(k: K): V? {
        return k?.let(cache::get).apply {
            Log.d(TAG, "get: key: $k, value: $this")
        }
    }

    override fun set(k: K, v: V) {
        cache[k] = v
    }

    companion object {
        const val NAME = "MemoryCache"
        private const val TAG = "MemoryCache"
    }
}