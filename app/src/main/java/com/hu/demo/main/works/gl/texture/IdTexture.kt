/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - IdTexture.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/08/24
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * huca<PERSON><PERSON>@Apps.Gallery		2024/08/24		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.oplus.gallery.foundation.opengl2.texture

import android.graphics.ColorSpace
import android.opengl.GLES30
import com.hu.demo.main.works.gl.texture.TexConfig
import com.hu.demo.main.works.gl.texture.TexKey
import com.hu.demo.main.works.gl.texture.Texture
import com.hu.demo.utils.ColorSpaceExt

/**
 * id类型的[Texture]，通过传入已存在的纹理id
 *
 * @param textureId 纹理id，此参数通常不设置，即默认值[GLES30.GL_INVALID_INDEX]，目前仅在[IdTexture]中使用
 * @param width 纹理宽
 * @param height 纹理高
 * @param clamp 纹理范围放大倍率
 * @param texTarget 纹理绘制目标，默认[GLES30.GL_TEXTURE_2D]
 * @param texConfig 纹理类型，默认[TexConfig.ARGB_8888]
 * @param colorSpace 色域，默认[COLOR_SPACE_SRGB]
 */
open class IdTexture(
    textureId: Int,
    width: Int,
    height: Int,
    texConfig: TexConfig = TexConfig.ARGB_8888,
    colorSpace: ColorSpace = ColorSpaceExt.SRGB,
    clamp: Float = 1f,
    texTarget: Int = GLES30.GL_TEXTURE_2D,
    isSelf: Boolean = true,
) : Texture(TexKey(width, height, texConfig = texConfig, texTarget = texTarget, textureId = textureId), colorSpace, clamp, isSelf) {
    override val tag: String = TAG

    private companion object {
        private const val TAG = "IdTexture"
    }
}