package com.hu.demo.main.works.decode

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.media.MediaMetadataRetriever
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.View
import android.view.View.OnClickListener
import android.view.ViewGroup
import android.widget.Button
import android.widget.EditText
import android.widget.ImageView
import android.widget.Spinner
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.contract.ActivityResultContract
import androidx.appcompat.widget.SwitchCompat
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.hu.demo.main.R
import com.hu.demo.main.adapter.BaseData
import com.hu.demo.main.adapter.BaseVH
import com.hu.demo.main.adapter.DefaultAdapter
import com.hu.demo.utils.setImageUriAny
import com.hu.demo.base.ui.BaseActivity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.Closeable
import java.util.concurrent.TimeUnit

class DecodeVideoActivity : BaseActivity(), OnClickListener {
    private var selectUri: Uri? = null
    private var ivImage: ImageView? = null
    private var etMaxCount: EditText? = null
    private var etInterval: EditText? = null
    private var spinnerMethod: Spinner? = null
    private var spinnerOption: Spinner? = null
    private var spinnerRetriever: Spinner? = null
    private var switchLog: SwitchCompat? = null
    private var btnDecode: Button? = null
    private var rvRecycler: RecyclerView? = null
    private var logAdapter: LogAdapter? = null
    private var job: Job? = null
    private val pickTypeResultContract = PickTypeResultContract()
    private val pickImageLauncher = registerForActivityResult(
        pickTypeResultContract,
        PickOnlyCallback { changeData(it) }
    )

    override fun getLayoutId(): Int {
        return R.layout.activity_decode_video
    }

    override fun initToolbar() {
        super.initToolbar()
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowTitleEnabled(true)
            setDisplayShowHomeEnabled(false)
        }
    }

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        ivImage = findViewById(R.id.ivImage)
        etMaxCount = findViewById(R.id.etMaxCount)
        etInterval = findViewById(R.id.etInterval)
        spinnerMethod = findViewById(R.id.spinner_method)
        spinnerOption = findViewById(R.id.spinner_option)
        spinnerRetriever = findViewById(R.id.spinner_retriever)
        switchLog = findViewById(R.id.switchLog)
        btnDecode = findViewById(R.id.btnDecode)
        rvRecycler = findViewById(R.id.rvRecycler)
    }

    override fun initEvent(savedInstanceState: Bundle?) {
        super.initEvent(savedInstanceState)
        ivImage?.setOnClickListener(this)
        btnDecode?.setOnClickListener(this)
        rvRecycler?.apply {
            addItemDecoration(DividerItemDecoration(this@DecodeVideoActivity, RecyclerView.VERTICAL))
            adapter = LogAdapter(this).apply {
                resetData(emptyList())
                logAdapter = this
            }
            layoutManager = LinearLayoutManager(this@DecodeVideoActivity, RecyclerView.VERTICAL, false)
        }
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.ivImage -> {
                pickImageLauncher.launch(MIME_TYPE)
            }

            R.id.btnDecode -> {
                val uri = selectUri ?: return
                val count = etMaxCount?.text?.toString()?.toInt() ?: return
                val interval = etInterval?.text?.toString()?.toLong() ?: return
                lifecycleScope.launch(Dispatchers.IO) {
                    logStart(count, interval)
                    var avgTime = 0f
                    val duration = MediaMetadataRetriever().use {
                        it.setDataSource(this@DecodeVideoActivity, uri)
                        val metadata = it.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)
                        TimeUnit.MILLISECONDS.toMicros(metadata!!.toLong())
                    }

                    var haveError = false
                    if (spinnerMethod?.selectedItemPosition == 1) {
                        val frameCount = MediaMetadataRetriever().use {
                            it.setDataSource(this@DecodeVideoActivity, uri)
                            val metadata = it.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_FRAME_COUNT)
                            metadata!!.toLong()
                        }
                        addLog(LogData("视频总帧数：${frameCount}帧"))
                        if (frameCount < count) {
                            addLog(LogData("视频总共帧数：${frameCount}帧，小于要求总帧数：$count"))
                            haveError = true
                        }
                    }

                    val retriever: IRetriever = if (spinnerRetriever?.selectedItemPosition == 1) {
                        MediaRetriever()
                    } else {
                        MediaRetriever()
                    }.apply {
                        setDataSource(this@DecodeVideoActivity, uri)
                    }
                    val func = { index: Int ->
                        if (spinnerMethod?.selectedItemPosition == 1) {
                            decodeByIndex(retriever, uri, index)
                        } else {
                            decodeByTime(retriever, uri, duration / count * index)
                        }
                    }
                    (0 until count).forEach { index ->
                        if (haveError) {
                            return@forEach
                        }
                        val lastTime = System.currentTimeMillis()
                        val bitmap = func(index)
                        val time = (System.currentTimeMillis() - lastTime).toFloat()
                        avgTime = (avgTime * index + time) / (index + 1)
                        addLog(LogBitmapData("index: $index, cost: $time ms", bitmap))
                        delay(interval)
                    }
                    retriever.close()
                    addLog(LogData("解码完成, avg: ${String.format("%.2f", avgTime)} ms"))
                    logEnd()
                }
            }
        }
    }

    private suspend fun logStart(count: Int, interval: Long) = withContext(Dispatchers.Main) {
        logAdapter?.apply {
            resetData(emptyList())
            notifyDataSetChanged()
            addLog(LogData("解码开始"))
        }
        Toast.makeText(this@DecodeVideoActivity, "count: $count, interval: $interval", Toast.LENGTH_SHORT).show()
    }

    private suspend fun logEnd() = withContext(Dispatchers.Main) {
        Toast.makeText(this@DecodeVideoActivity, "解码完成", Toast.LENGTH_SHORT).show()
    }

    private suspend fun addLog(data: BaseData) = withContext(Dispatchers.Main) {
        if (switchLog?.isChecked == true) {
            logAdapter?.apply {
                dataList.add(data)
                notifyItemInserted(dataList.lastIndex)
                rvRecycler?.scrollToPosition(dataList.lastIndex)
            }
        }
    }

    private fun changeData(obj: Any?) {
        ivImage?.also {
            this.selectUri = obj as Uri
            it.setImageUriAny(selectUri)
        }
    }

    private fun decodeByTime(retriever: IRetriever, uri: Uri, timeUs: Long): Bitmap? {
        Log.d(TAG, "decode: start")
        val lastTime = System.currentTimeMillis()
        val bitmap = retriever.let {
            val option = spinnerOption?.selectedItemPosition!!
            it.getFrameAtTime(timeUs, option)
        }
        Log.d(TAG, "decode: time: ${System.currentTimeMillis() - lastTime}, ${bitmap?.width to bitmap?.height}")
        return bitmap
    }

    private fun decodeByIndex(retriever: IRetriever, uri: Uri, index: Int): Bitmap? {
        Log.d(TAG, "decode: start")
        val lastTime = System.currentTimeMillis()
        val bitmap = retriever.let {
            it.getFrameAtIndex(index)
        }
        Log.d(TAG, "decode: time: ${System.currentTimeMillis() - lastTime}, ${bitmap?.width to bitmap?.height}")
        return bitmap
    }

    class LogVH(parent: ViewGroup) : BaseVH<LogData>(parent, R.layout.item_decode_log) {
        private val tvItem: TextView = findViewById(R.id.tv_item)

        override fun bind(extraData: Map<String, Any>, data: LogData, position: Int) {
            tvItem.text = data.name
        }
    }

    class LogBitmapVH(parent: ViewGroup) : BaseVH<LogBitmapData>(parent, R.layout.item_decode_log_bitmap) {
        private val tvItem: TextView = findViewById(R.id.tv_item)
        private val ivImage: ImageView = findViewById(R.id.iv_image)

        override fun bind(extraData: Map<String, Any>, data: LogBitmapData, position: Int) {
            tvItem.text = data.name
            data.bitmap.run(ivImage::setImageBitmap)
        }
    }

    data class LogData(val name: String?) : BaseData(TYPE_DEFAULT)
    data class LogBitmapData(val name: String?, val bitmap: Bitmap? = null) : BaseData(TYPE_BITMAP)

    class LogAdapter(recyclerView: RecyclerView) : DefaultAdapter<BaseData, BaseVH<BaseData>>(recyclerView) {
        @Suppress("UNCHECKED_CAST")
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH<BaseData> {
            return if (viewType == TYPE_BITMAP) {
                LogBitmapVH(parent)
            } else {
                LogVH(parent)
            } as BaseVH<BaseData>
        }

        override fun onBindViewHolder(holder: BaseVH<BaseData>, position: Int) {
            holder.bind(emptyMap(), dataList[position], position)
        }
    }

    private open class PickOnlyCallback(private val resultFunc: (Uri) -> Unit) : ActivityResultCallback<Uri?> {
        override fun onActivityResult(result: Uri?) {
            result ?: run {
                Log.d(TAG, "PickOnlyCallback.onActivityResult: ")
                return
            }
            Log.d(TAG, "onActivityResult: $result")
            resultFunc(result)
        }
    }

    private class PickTypeResultContract : ActivityResultContract<String, Uri?>() {
        override fun createIntent(context: Context, input: String): Intent {
            return Intent(Intent.ACTION_PICK).setType(input)
        }

        override fun parseResult(resultCode: Int, intent: Intent?): Uri? {
            return if (resultCode != Activity.RESULT_OK) null else intent?.data
        }
    }


    interface IRetriever : Closeable {
        fun setDataSource(context: Context, uri: Uri)
        fun getFrameAtTime(timeUs: Long, option: Int): Bitmap?
        fun getFrameAtIndex(index: Int): Bitmap?

    }

    class MediaRetriever : IRetriever {
        private val retriever = MediaMetadataRetriever()

        override fun setDataSource(context: Context, uri: Uri) {
            retriever.setDataSource(context, uri)
        }

        override fun getFrameAtTime(timeUs: Long, option: Int): Bitmap? {
            return retriever.getFrameAtTime(timeUs, option)
        }

        override fun getFrameAtIndex(index: Int): Bitmap? {
            return retriever.getFrameAtIndex(index)
        }

        override fun close() {
            retriever.close()
        }
    }

//    class TblRetriever : IRetriever {
//        private val retriever = TBLMediaMetadataRetriever()
//
//        override fun setDataSource(context: Context, uri: Uri) {
//            retriever.setDataSource(context, uri)
//        }
//
//        override fun getFrameAtTime(timeUs: Long, option: Int): Bitmap? {
//            return retriever.getFrameAtTime(timeUs, option)
//        }
//
//        override fun getFrameAtIndex(index: Int): Bitmap? {
//            return null
//        }
//
//        override fun close() {
//            retriever.release()
//        }
//    }


    companion object {
        private const val TAG = "DecodeVideoFrameActivity"
        private const val MIME_TYPE = "video/*"

        private const val TYPE_DEFAULT = 0
        private const val TYPE_BITMAP = 1
    }
}