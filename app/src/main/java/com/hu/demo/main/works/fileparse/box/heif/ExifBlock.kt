package com.hu.demo.main.works.fileparse.box.heif

import com.hu.demo.main.works.fileparse.ExifReader
import com.hu.demo.main.works.fileparse.IRead
import com.hu.demo.main.works.fileparse.box.Tree
import com.hu.demo.utils.ByteOrderedDataInputStream
import com.hu.demo.utils.readByteOrder

internal class ExifBlock(private val ilocItem: ItemLocationBox.Item, level: Int, parent: Tree?) : Tree("exif", level, parent), IRead {
    override val realSize: Long
        get() = ilocItem.extentItem?.get(0)?.extentLength?.toLong() ?: 0L
    var sb: StringBuilder? = null
    override fun read(bis: ByteOrderedDataInputStream) {
        sb = StringBuilder()
        val realSize = realSize.toInt()
        if (realSize == 0) {
            sb?.append("EXIF数据大小为0")
            return
        }

        try {
            // 读取完整的EXIF数据块
            val exifData = ByteArray(realSize)
            bis.readFully(exifData)
            val exifBis = ByteOrderedDataInputStream(exifData)

            // HEIF中的EXIF数据通常以4字节偏移开始，指向TIFF数据的起始位置
            val tiffOffset = exifBis.readInt()
            sb?.append("TIFF offset: $tiffOffset")

            if (tiffOffset < 4 || tiffOffset >= realSize) {
                sb?.append(" (invalid offset)")
                return
            }

            // 跳转到TIFF数据开始位置
            exifBis.skipFully(tiffOffset) // 减去已读取的4字节偏移

            val tiffData = exifData.sliceArray(Int.SIZE_BYTES + tiffOffset until realSize)

            // 读取TIFF头部以确定字节序
            val byteOrder = exifBis.readByteOrder()
            sb?.append(" $byteOrder")
            exifBis.byteOrder = byteOrder

            // 读取IFD偏移
            val ifdOffset = exifBis.readInt()
            sb?.append("\n\tIFD offset: $ifdOffset")

            // 调用ExifReader解析
            ExifReader.readExifTag2(byteOrder, ifdOffset, sb!!, tiffData)
        } catch (e: Exception) {
            sb?.append("\nError reading EXIF: ${e.message}")
            android.util.Log.e("ExifBlock", "Error reading EXIF data", e)
        }
    }

    override fun toString(): String {
        return "$sb"
    }
}
