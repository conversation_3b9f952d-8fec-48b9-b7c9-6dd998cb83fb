/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - ITexture.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/09/20
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery		2024/09/20		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.texture

import android.graphics.Bitmap
import android.graphics.ColorSpace
import android.hardware.HardwareBuffer
import android.opengl.GLES30
import androidx.hardware.HardwareBufferFormat
import com.hu.demo.main.works.gl.IRecyclable
import com.hu.demo.main.works.gl.IReusable
import com.hu.demo.main.works.gl.utils.GLThread
import com.hu.demo.utils.ApiLevelUtil

interface ITexture : IRecyclable, IReusable {
    /**
     * 纹理是否可用，如果纹理已经执行了[recycle]纹理需要标记不可用
     */
    var canUse: Boolean

    /**
     * 纹理id，需执行[load]方法后，才会有效
     */
    val textureId: Int

    /**
     * 纹理的宽
     */
    val width: Int

    /**
     * 纹理的高
     */
    val height: Int

    /**
     * 深度
     */
    val depth: Int

    /**
     * 色域
     */
    val colorSpace: ColorSpace

    /**
     * 重写的色域，显示时使用
     */
    var displayColorSpace: ColorSpace

    /**
     * 纹理的类型
     */
    val texConfig: TexConfig

    /**
     * 纹理的target
     */
    val texTarget: Int

    /**
     * 纹理范围放大倍率
     */
    val clamp: Float

    /**
     * 纹理每个像素的字节数
     */
    val byteSize: Int

    /**
     * 添加标记，标记当前纹理不能执行reuse
     */
    fun increaseMark()


    /**
     * 移除标记
     */
    fun reduceMark()

    /**
     * 执行纹理的上传
     */
    fun load()

    /**
     * 强制执行纹理上传
     */
    fun force()

    @GLThread
    fun copy(outTexture: ITexture? = null): ITexture

    /**
     * 将纹理转成[Bitmap]
     *
     * @param usePbo 是否使用pbo形式获取
     *
     * @return 返回转成的[Bitmap]
     */
    fun toBitmap(scale: Float = 1.0f): Bitmap

    /**
     * 激活纹理
     *
     * @param texUnitIndex 索引，从0开始
     */
    fun active(texUnitIndex: Int)

    /**
     * 获取真正[Texture]，实体Texture直接返回自身，如果是[VirtualTexture]则返回其真正的[Texture]
     *
     * @return 返回真正的[Texture]，注意非[RealTexture]
     */
    fun getTexture(): Texture

    /**
     * 转换成[VirtualTexture]，[VirtualTexture]的[IReusable]以及[IRecyclable]接口为空实现，即无法执行复用以及回收
     *
     * @return 返回[VirtualTexture]类实例，当是实体Texture时，则构造[VirtualTexture]实体，当是[VirtualTexture]时，则返回自身
     */
    fun toVirtual(): ITexture

    companion object {
        /**
         * 获取Internal Format
         *
         * @return Internal Format
         */
        fun TexConfig.getInternalFormat(): Int {
            return when {
                this == TexConfig.ALPHA_8 -> GLES30.GL_R8
                this == TexConfig.ARGB_8888 -> GLES30.GL_RGBA
                this == TexConfig.RGBA_F16 -> GLES30.GL_RGBA16F
                ApiLevelUtil.isAtLeastAndroidT() && this == TexConfig.RGBA_1010102 -> GLES30.GL_RGB10_A2
                else -> GLES30.GL_INVALID_INDEX
            }
        }

        /**
         * 获取[HardwareBuffer]对应的类型
         *
         * @return 返回[HardwareBuffer]对应的类型
         */
        @HardwareBufferFormat
        fun TexConfig.getHwbFormat(): Int {
            return when {
                this == TexConfig.ALPHA_8 -> HardwareBuffer.S_UI8
                this == TexConfig.ARGB_8888 -> HardwareBuffer.RGBA_8888
                this == TexConfig.RGBA_F16 -> HardwareBuffer.RGBA_FP16
                this == TexConfig.RGBA_1010102 -> HardwareBuffer.RGBA_1010102
                else -> GLES30.GL_INVALID_INDEX
            }
        }

        /**
         * 获取像素数据的数据类型
         *
         * @return 像素数据的数据类型
         */
        fun TexConfig.getType(): Int {
            return when {
                this == TexConfig.ALPHA_8 -> GLES30.GL_UNSIGNED_BYTE
                this == TexConfig.RGB_565 -> GLES30.GL_UNSIGNED_SHORT_5_6_5
                this == TexConfig.ARGB_8888 -> GLES30.GL_UNSIGNED_BYTE
                this == TexConfig.RGBA_F16 -> GLES30.GL_HALF_FLOAT
                ApiLevelUtil.isAtLeastAndroidT() && this == TexConfig.RGBA_1010102 -> GLES30.GL_UNSIGNED_INT_2_10_10_10_REV
                else -> GLES30.GL_INVALID_INDEX
            }
        }

        /**
         * 获取图像数据的类型
         *
         * @param internalFormat Internal Format
         *
         * @return 图像数据的类型
         */
        fun getFormat(internalFormat: Int): Int {
            return when (internalFormat) {
                GLES30.GL_R8 -> GLES30.GL_RED
                GLES30.GL_RGBA16F -> GLES30.GL_RGBA
                GLES30.GL_SRGB8_ALPHA8 -> GLES30.GL_RGBA
                GLES30.GL_RGB10_A2 -> GLES30.GL_RGBA
                else -> internalFormat
            }
        }
    }
}