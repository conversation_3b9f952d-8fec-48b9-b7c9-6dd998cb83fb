package com.hu.demo.main.works.multicache

import android.content.SharedPreferences
import android.util.Log

class StringSpCache(private val sp: SharedPreferences) : ICache<String, String> {
    override val name: String
        get() = NAME

    override fun get(k: String): String? {
        return sp.getString(k, "").takeIf { it != "" }.apply {
            Log.d(TAG, "get: key: $k, value: $this")
        }
    }

    override fun set(k: String, v: String) {
        return sp.edit().putString(k, v).apply()
    }

    companion object {
        const val NAME = "StringSpCache"
        private const val TAG = "StringSpCache"
    }
}