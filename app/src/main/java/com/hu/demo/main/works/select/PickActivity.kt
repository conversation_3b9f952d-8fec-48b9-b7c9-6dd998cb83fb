package com.hu.demo.main.works.select

import android.app.Activity
import android.content.ClipData
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.util.Log
import android.view.ViewGroup
import android.widget.ImageView
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.contract.ActivityResultContract
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.FileProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.hu.demo.base.provider.DFileProvider
import com.hu.demo.base.ui.BaseActivity
import com.hu.demo.main.R
import com.hu.demo.main.adapter.BaseData
import com.hu.demo.main.adapter.BaseVH
import com.hu.demo.main.adapter.DefaultAdapter
import com.hu.demo.main.works.select.datas.BasePickData
import com.hu.demo.main.works.select.datas.GetContentAllData
import com.hu.demo.main.works.select.datas.GetContentAllMultiData
import com.hu.demo.main.works.select.datas.GetContentImageData
import com.hu.demo.main.works.select.datas.GetContentVideoData
import com.hu.demo.main.works.select.datas.PickAllData
import com.hu.demo.main.works.select.datas.PickAndCropData
import com.hu.demo.main.works.select.datas.PickAndEditData
import com.hu.demo.main.works.select.datas.PickImageData
import com.hu.demo.main.works.select.datas.PickImageUriData
import com.hu.demo.main.works.select.datas.PickVideoData
import com.hu.demo.main.works.select.datas.PickVideoUriData
import com.hu.demo.main.works.select.vhs.GetContentAllMultiVH
import com.hu.demo.main.works.select.vhs.GetContentAllVH
import com.hu.demo.main.works.select.vhs.GetContentImageVH
import com.hu.demo.main.works.select.vhs.GetContentVideoVH
import com.hu.demo.main.works.select.vhs.PickAllVH
import com.hu.demo.main.works.select.vhs.PickAndCropPageVH
import com.hu.demo.main.works.select.vhs.PickAndEditPageVH
import com.hu.demo.main.works.select.vhs.PickImageUriVH
import com.hu.demo.main.works.select.vhs.PickImageVH
import com.hu.demo.main.works.select.vhs.PickVideoUriVH
import com.hu.demo.main.works.select.vhs.PickVideoVH
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File

class PickActivity : BaseActivity() {
    private var recycler: RecyclerView? = null
    private var pickImage: ImageView? = null
    private val datas: MutableList<BasePickData> = mutableListOf()

    private val cropResultContract = CropResultContract()
    private val editResultContract = EditResultContract()

    private val pickTypeResultContract = PickTypeResultContract()
    private val pickDataResultContract = PickDataResultContract()

    private val cropLauncher = registerForActivityResult(
        cropResultContract,
        CropCallback { changeData(PickAndCropData::class.java, it) }
    )
    private val editLauncher = registerForActivityResult(
        editResultContract,
        EditCallback { changeData(PickAndEditData::class.java, it) }
    )
    private val getContentImageLauncher = registerForActivityResult(
        GetContentResultContract(),
        PickOnlyCallback { changeData(GetContentImageData::class.java, it) }
    )
    private val getContentVideoLauncher = registerForActivityResult(
        GetContentResultContract(),
        PickOnlyCallback { changeData(GetContentVideoData::class.java, it) }
    )
    private val getContentAllLauncher = registerForActivityResult(
        GetContentResultContract(),
        PickOnlyCallback { changeData(GetContentAllData::class.java, it) }
    )
    private val getContentAllMultiLauncher = registerForActivityResult(
        GetMultipleContents(2),
        PickMultiCallback { changeData(GetContentAllMultiData::class.java, it) }
    )
    private val pickImageLauncher = registerForActivityResult(
        pickTypeResultContract,
        PickOnlyCallback { changeData(PickImageData::class.java, it) }
    )
    private val pickImageTypeLauncher = registerForActivityResult(
        pickDataResultContract,
        PickOnlyCallback { changeData(PickImageUriData::class.java, it) }
    )
    private val pickVideoLauncher = registerForActivityResult(
        pickTypeResultContract,
        PickOnlyCallback { changeData(PickVideoData::class.java, it) }
    )
    private val pickVideoTypeLauncher = registerForActivityResult(
        pickDataResultContract,
        PickOnlyCallback { changeData(PickVideoUriData::class.java, it) }
    )
    private val pickAllLauncher = registerForActivityResult(
        pickTypeResultContract,
        PickOnlyCallback { changeData(PickAllData::class.java, it) }
    )
    private val pickAndCropLauncher = registerForActivityResult(
        pickTypeResultContract,
        PickCallback {
            changeData(PickAndCropData::class.java, it)
            cropLauncher.launch(it)
        }
    )
    private val pickAndEditLauncher = registerForActivityResult(
        pickTypeResultContract,
        PickCallback {
            changeData(PickAndEditData::class.java, it)
            editLauncher.launch(it)
        }
    )

    override fun getLayoutId(): Int {
        return R.layout.activity_crop
    }

    override fun initToolbar() {
        super.initToolbar()
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowTitleEnabled(true)
            setDisplayShowHomeEnabled(false)
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        datas.add(GetContentImageData(getContentImageLauncher))
        datas.add(GetContentVideoData(getContentVideoLauncher))
        datas.add(GetContentAllData(getContentAllLauncher))
        datas.add(GetContentAllMultiData(getContentAllMultiLauncher))
        datas.add(PickImageData(pickImageLauncher))
        datas.add(PickImageUriData(pickImageTypeLauncher))
        datas.add(PickVideoData(pickVideoLauncher))
        datas.add(PickVideoUriData(pickVideoTypeLauncher))
        datas.add(PickAllData(pickAllLauncher))
        datas.add(PickAndCropData(pickAndCropLauncher))
        datas.add(PickAndEditData(pickAndEditLauncher))
    }

    override fun initView(savedInstanceState: Bundle?) {
        pickImage = findViewById(R.id.pickImage)
        recycler = findViewById(R.id.recycler)
    }

    override fun initEvent(savedInstanceState: Bundle?) {
        recycler?.apply {
            addItemDecoration(DividerItemDecoration(this@PickActivity, RecyclerView.VERTICAL))
            adapter = KaAdapter(this).apply {
                resetData(datas)
            }
            layoutManager = LinearLayoutManager(this@PickActivity, RecyclerView.VERTICAL, false)
        }
    }

    private fun changeData(dataClass: Class<*>, obj: Any?) {
        lifecycleScope.launch(Dispatchers.Default) {
            datas.withIndex().find {
                dataClass.isAssignableFrom(it.value.javaClass)
            }?.apply {
                withContext(Dispatchers.Main) {
                    value.obj = obj
                    recycler?.adapter?.notifyItemChanged(index)
                }
            }
        }
    }

    class KaAdapter(recyclerView: RecyclerView) : DefaultAdapter<BaseData, BaseVH<BaseData>>(recyclerView) {
        @Suppress("UNCHECKED_CAST")
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH<BaseData> {
            return when (viewType) {
                TYPE_PICK_IMAGE -> PickImageVH(parent)
                TYPE_PICK_IMAGE_URI -> PickImageUriVH(parent)
                TYPE_PICK_VIDEO -> PickVideoVH(parent)
                TYPE_PICK_VIDEO_URI -> PickVideoUriVH(parent)
                TYPE_PICK_ALL -> PickAllVH(parent)
                TYPE_GET_CONTENT_IMAGE -> GetContentImageVH(parent)
                TYPE_GET_CONTENT_VIDEO -> GetContentVideoVH(parent)
                TYPE_GET_CONTENT_ALL -> GetContentAllVH(parent)
                TYPE_GET_CONTENT_MULTI_ALL -> GetContentAllMultiVH(parent)
                TYPE_PICK_AND_CROP -> PickAndCropPageVH(parent)
                TYPE_PICK_AND_EDIT -> PickAndEditPageVH(parent)
                else -> throw IllegalArgumentException("viewType error: $viewType")
            } as BaseVH<BaseData>
        }

        override fun onBindViewHolder(holder: BaseVH<BaseData>, position: Int) {
            holder.bind(emptyMap(), dataList[position], position)
        }
    }

    private open class GetContentResultContract : ActivityResultContract<String, Uri?>() {
        override fun createIntent(context: Context, input: String): Intent {
            return Intent(Intent.ACTION_GET_CONTENT).addCategory(Intent.CATEGORY_OPENABLE).setType(input)
        }

        override fun parseResult(resultCode: Int, intent: Intent?): Uri? {
            return if (resultCode != Activity.RESULT_OK) null else intent?.data
        }
    }

    private open class GetMultipleContents(private val maxCount: Int = 100) : ActivityResultContracts.GetMultipleContents() {
        override fun createIntent(context: Context, input: String): Intent {
            return super.createIntent(context, input).also {
                it.putExtra(MediaStore.EXTRA_PICK_IMAGES_MAX, maxCount)
            }
        }
    }

    private class PickTypeResultContract : ActivityResultContract<String, Uri?>() {
        override fun createIntent(context: Context, input: String): Intent {
            return Intent(Intent.ACTION_PICK).setType(input)
        }

        override fun parseResult(resultCode: Int, intent: Intent?): Uri? {
            return if (resultCode != Activity.RESULT_OK) null else intent?.data
        }
    }

    private class PickDataResultContract : ActivityResultContract<Uri, Uri?>() {
        override fun createIntent(context: Context, input: Uri): Intent {
            return Intent(Intent.ACTION_PICK).setData(input)
        }

        override fun parseResult(resultCode: Int, intent: Intent?): Uri? {
            return if (resultCode != Activity.RESULT_OK) null else intent?.data
        }
    }

    private class EditResultContract : ActivityResultContract<Uri, Uri?>() {
        override fun createIntent(context: Context, input: Uri): Intent {
            val outFile = File(context.externalCacheDir, "outFile.jpg")
            if (outFile.exists()) {
                outFile.delete()
            }
            val inputFile = File(context.externalCacheDir, "inputFile.jpg")
            context.contentResolver.openInputStream(input)?.use {
                inputFile.outputStream().use { os ->
                    it.copyTo(os)
                }
            }
            val inputUri = FileProvider.getUriForFile(context, DFileProvider.AUTHORITY, inputFile)
            val outUri = FileProvider.getUriForFile(context, DFileProvider.AUTHORITY, outFile)

            val intent = Intent(Intent.ACTION_EDIT).apply {
                setPackage("com.coloros.gallery3d") // 如果是一加外销 设置 com.oneplus.gallery
                setDataAndType(inputUri, "image/*")
                // 设置输出的uri
                putExtra(MediaStore.EXTRA_OUTPUT, outUri)
                // 给输出的uri提供写入权限
                addFlags(Intent.FLAG_GRANT_WRITE_URI_PERMISSION or Intent.FLAG_GRANT_READ_URI_PERMISSION)
                clipData = ClipData.newRawUri(MediaStore.EXTRA_OUTPUT, outUri)
            }
            return intent
        }

        override fun parseResult(resultCode: Int, intent: Intent?): Uri? {
            return intent?.data
        }
    }
    private class CropResultContract : ActivityResultContract<Uri, Uri?>() {
        override fun createIntent(context: Context, input: Uri): Intent {
            val outFile = File(context.externalCacheDir, "outFile.jpg")
            if (outFile.exists()) {
                outFile.delete()
            }
            val inputFile = File(context.externalCacheDir, "inputFile.jpg")
            context.contentResolver.openInputStream(input)?.use {
                inputFile.outputStream().use { os ->
                    it.copyTo(os)
                }
            }
            val inputUri = FileProvider.getUriForFile(context, DFileProvider.AUTHORITY, inputFile)
            val outUri = FileProvider.getUriForFile(context, DFileProvider.AUTHORITY, outFile)

            val intent = Intent("com.android.camera.action.CROP").apply {
                setDataAndType(inputUri, "image/*")

                putExtra("crop", "true")
                putExtra("scale", true)
                putExtra("scaleUpIfNeeded", true)
                putExtra("aspectX", 1)
                putExtra("aspectY", 1)
                putExtra("outputX", 300)
                putExtra("outputY", 300)

                // 返回bitmap
                // putExtra("return-data", true)
                putExtra(MediaStore.EXTRA_OUTPUT, outUri)

                addFlags(Intent.FLAG_GRANT_WRITE_URI_PERMISSION or Intent.FLAG_GRANT_READ_URI_PERMISSION)
                clipData = ClipData.newRawUri(MediaStore.EXTRA_OUTPUT, outUri)
            }
            return intent
        }

        override fun parseResult(resultCode: Int, intent: Intent?): Uri? {
            return intent?.data
        }
    }

    private open class PickMultiCallback(private val resultFunc: (List<Uri>) -> Unit) : ActivityResultCallback<List<Uri>> {
        override fun onActivityResult(result: List<Uri>) {
            if (result.isEmpty()) {
                Log.d(TAG, "PickMultiCallback.onActivityResult: ")
                return
            }
            Log.d(TAG, "onActivityResult: $result")
            resultFunc(result)
        }
    }

    private open class PickOnlyCallback(private val resultFunc: (Uri) -> Unit) : ActivityResultCallback<Uri?> {
        override fun onActivityResult(result: Uri?) {
            result ?: run {
                Log.d(TAG, "PickOnlyCallback.onActivityResult: ")
                return
            }
            Log.d(TAG, "onActivityResult: $result")
            resultFunc(result)
        }
    }

    private class PickCallback(
        private val resultFunc: (Uri) -> Unit,
    ) : ActivityResultCallback<Uri?> {
        override fun onActivityResult(result: Uri?) {
            result ?: run {
                Log.d(TAG, "PickCallback.onActivityResult: ")
                return
            }
            Log.d(TAG, "onActivityResult: $result")
            resultFunc(result)
        }
    }

    private class CropCallback(
        private val resultFunc: (Uri) -> Unit,
    ) : ActivityResultCallback<Uri?> {
        override fun onActivityResult(result: Uri?) {
            Log.d(TAG, "CropCallback.onActivityResult: $result")
            result ?: run {
                Log.d(TAG, "onActivityResult: ")
                return
            }
            Log.d(TAG, "onActivityResult: $result")
            resultFunc(result)
        }
    }

    private class EditCallback(
        private val resultFunc: (Uri) -> Unit,
    ) : ActivityResultCallback<Uri?> {
        override fun onActivityResult(result: Uri?) {
            Log.d(TAG, "EditCallback.onActivityResult: $result")
            result ?: run {
                Log.d(TAG, "onActivityResult: ")
                return
            }
            Log.d(TAG, "onActivityResult: $result")
            resultFunc(result)
        }
    }

    companion object {
        private const val TAG = "PickActivity"
    }
}