/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : BasicToneSoourceFactory.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2025/2/14
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2025/2/14      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.hu.demo.main.works.lut

import android.annotation.SuppressLint
import android.util.Half
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.nio.FloatBuffer

object Table {

    val ditherTable = floatArrayOf(
        0.3973f,
        0.1202f,
        -0.0375f,
        0.0469f,
        -0.2962f,
        0.0916f,
        -0.1595f,
        0.0533f,
        0.2004f,
        -0.2531f,
        0.4853f,
        -8.0E-4f,
        -0.4639f,
        0.4819f,
        -0.1706f,
        0.2195f,
        -0.0964f,
        0.4404f,
        0.3767f,
        0.0404f,
        -0.3289f,
        -0.1786f,
        -0.19f,
        -0.1249f,
        -0.2861f,
        0.0437f,
        -7.0E-4f,
        -0.3241f,
        0.4572f,
        0.127f,
        -0.1211f,
        0.4623f,
        -0.2528f,
        0.4493f,
        0.117f,
        0.0589f,
        0.0408f,
        0.4576f,
        -0.28f,
        -0.2589f,
        -0.1877f,
        -0.2948f,
        0.3482f,
        -0.1516f,
        -0.2051f,
        -0.2145f,
        -0.3242f,
        0.1985f,
        -0.1663f,
        -0.4476f,
        -0.1532f,
        0.0048f,
        -0.0182f,
        0.1568f,
        0.38f,
        0.1957f,
        -0.1917f,
        -0.0129f,
        0.3716f,
        -0.1267f,
        -0.386f,
        -0.2496f,
        -0.1644f,
        -0.1388f,
        -0.1924f,
        -0.4396f,
        -0.4721f,
        0.3484f,
        -0.3742f,
        -0.2521f,
        -0.3026f,
        0.0461f,
        0.061f,
        -0.4545f,
        0.3945f,
        0.356f,
        -0.0611f,
        -0.3219f,
        0.1623f,
        0.2727f,
        -0.1615f,
        -0.3831f,
        -0.2226f,
        0.3203f,
        0.2737f,
        -0.2348f,
        0.1238f,
        -0.418f,
        0.2523f,
        -0.0047f,
        -0.0447f,
        0.3664f,
        0.3536f,
        0.3988f,
        -0.1646f,
        -0.3389f,
        -0.4329f,
        -0.1366f,
        -0.3826f,
        -0.3071f,
        0.2192f,
        -0.0773f,
        0.239f,
        -0.2198f,
        0.0761f,
        0.2414f,
        -0.256f,
        -0.485f,
        -0.4727f,
        -0.4859f,
        0.2877f,
        -0.0263f,
        -0.3689f,
        -0.3271f,
        -0.0982f,
        -0.4874f,
        -0.0618f,
        -0.4745f,
        -0.2975f,
        -0.3096f,
        0.1287f,
        0.2658f,
        -0.3354f,
        -0.0177f,
        0.1645f,
        1.0E-4f,
        0.2513f,
        0.3395f,
        0.4714f,
        0.4766f,
        -0.3597f,
        0.2984f,
        -0.1008f,
        0.4872f,
        -0.3136f,
        0.4754f,
        0.3364f,
        -0.0696f,
        -0.4018f,
        0.3638f,
        0.0525f,
        0.3859f,
        -0.0547f,
        0.1836f,
        -0.3334f,
        0.3472f,
        0.1962f,
        0.1048f,
        0.3727f,
        0.3988f,
        0.4032f,
        0.0014f,
        0.2724f,
        -0.3244f,
        -0.4085f,
        0.0447f,
        0.2836f,
        0.4507f,
        -0.1158f,
        0.2549f,
        0.4273f,
        0.0245f,
        0.1612f,
        -0.0657f,
        0.0116f,
        0.3476f,
        -0.0904f,
        -0.0441f,
        -0.222f,
        0.1157f,
        -0.1804f,
        0.4384f,
        0.1094f,
        0.2649f,
        0.1219f,
        0.2761f,
        0.2199f,
        -0.0741f,
        -0.1192f,
        0.0926f,
        -0.0675f,
        -0.1082f,
        -0.2982f,
        -0.2952f,
        0.0675f,
        -0.0988f,
        0.2496f,
        -0.149f,
        -0.1482f,
        -0.3663f,
        -0.2862f,
        -0.221f,
        0.2661f,
        0.375f,
        0.3213f,
        -0.1145f,
        0.2225f,
        -0.2692f,
        0.3414f,
        -0.3917f,
        0.3466f,
        -0.2312f,
        -0.4534f,
        -0.0441f,
        -0.3584f,
        0.2764f,
        -0.1602f,
        0.3615f,
        -0.2977f,
        0.3286f,
        0.0619f,
        0.2427f,
        -0.2797f,
        0.3716f,
        0.4476f,
        0.3957f,
        -0.2273f,
        0.305f,
        -0.1454f,
        0.1246f,
        -0.4535f,
        0.0684f,
        -0.4886f,
        0.3126f,
        0.0512f,
        0.3327f,
        -0.1941f,
        -0.1185f,
        -0.3286f,
        -0.2448f,
        -0.0101f,
        -0.3742f,
        0.1319f,
        0.1444f,
        0.1896f,
        0.3814f,
        0.0287f,
        -0.4706f,
        0.2429f,
        0.3389f,
        0.358f,
        -0.0874f,
        0.0815f,
        -0.3139f,
        -0.2158f,
        0.1369f,
        -0.4182f,
        0.1648f,
        -0.0582f,
        -0.0636f,
        -0.2107f,
        -0.0117f,
        -0.3874f,
        -0.0913f,
        -0.0913f,
        0.1638f,
        -0.2587f,
        0.3226f,
        -0.4548f,
        0.0206f,
        -0.4223f,
        0.143f,
        0.1464f,
        0.3175f,
        -0.1047f,
        -0.0561f,
        0.1989f,
        0.424f,
        -0.0267f,
        0.0496f,
        0.3707f,
        -0.0609f,
        0.4622f,
        -0.0479f,
        0.1252f,
        -0.1458f,
        -0.411f,
        0.3149f,
        -0.481f,
        0.1387f,
        -0.2488f,
        -0.0838f,
        -0.373f,
        -0.0283f,
        0.3249f,
        0.1436f,
        -0.3646f,
        -0.4338f,
        -0.0339f,
        -0.2114f,
        0.1947f,
        0.1517f,
        -0.4606f,
        -0.1589f,
        -0.0308f,
        -0.0653f,
        0.285f,
        -0.2241f,
        -0.0335f,
        -0.1339f,
        0.3255f,
        -0.1629f,
        0.3052f,
        0.2876f,
        0.3972f,
        0.0382f,
        -0.2503f,
        -0.406f,
        -0.1469f,
        -0.2313f,
        0.2327f,
        0.2122f,
        0.1849f,
        0.4676f,
        -0.2082f,
        0.0098f,
        0.1112f,
        0.0351f,
        0.1839f,
        -0.3149f,
        0.3237f,
        -0.1215f,
        0.4448f,
        0.4711f,
        0.3275f,
        0.0218f,
        0.0136f,
        0.2203f,
        0.2977f,
        0.4802f,
        -0.4137f,
        0.1231f,
        -0.0749f,
        0.4994f,
        0.0185f,
        -0.0699f,
        0.1455f,
        0.2682f,
        0.0241f,
        -0.3936f,
        -0.3553f,
        -0.1353f,
        0.4265f,
        0.3297f,
        -0.1678f,
        -0.2818f,
        -0.0527f,
        -0.4488f,
        0.2534f,
        -0.3689f,
        -0.2636f,
        0.1849f,
        0.1175f,
        -0.3189f,
        0.1559f,
        0.0529f,
        0.2029f,
        -0.3305f,
        -0.2269f,
        0.1084f,
        -0.2425f,
        -0.0326f,
        -0.2686f,
        0.2905f,
        0.0746f,
        0.3578f,
        -0.2794f,
        -0.28f,
        0.126f,
        0.3526f,
        -0.1736f,
        0.3786f,
        -0.2828f,
        -0.1392f,
        0.2082f,
        0.0495f,
        0.079f,
        -0.2367f,
        0.1007f,
        -0.1677f,
        0.0023f,
        0.445f,
        -0.4828f,
        -0.3802f,
        -0.2661f,
        0.281f,
        0.1727f,
        -0.4554f,
        -0.4417f,
        -0.4464f,
        0.153f,
        -0.1842f,
        0.021f,
        0.4923f,
        -0.2858f,
        -0.2965f,
        0.3501f,
        -0.0652f,
        -0.0765f,
        0.0839f,
        -0.1048f,
        0.3579f,
        -0.0376f,
        0.1125f,
        -0.2814f,
        -0.2215f,
        -0.2302f,
        0.2976f,
        0.0418f,
        0.4784f,
        -0.2622f,
        -0.4559f,
        0.4234f,
        -0.1371f,
        -0.2282f,
        -0.3428f,
        -0.2483f,
        -0.4477f,
        -0.2981f,
        -0.1899f,
        -0.3941f,
        0.4628f,
        0.1259f,
        0.2349f,
        0.4551f,
        0.3401f,
        0.4384f,
        0.3051f,
        -0.1173f,
        -0.0303f,
        -0.0032f,
        0.278f,
        -0.1725f,
        0.4592f,
        -0.0017f,
        0.154f,
        -0.1545f,
        0.2681f,
        0.0595f,
        0.3874f,
        0.2464f,
        0.2973f,
        -0.4607f,
        0.2776f,
        -0.232f,
        -0.1889f,
        -0.4573f,
        0.0197f,
        -0.1366f,
        -0.1475f,
        0.3298f,
        0.0772f,
        -0.1848f,
        0.0636f,
        -0.188f,
        -0.2298f,
        0.0115f,
        -0.2497f,
        -0.3169f,
        0.3943f,
        0.2201f,
        0.1799f,
        0.2801f,
        -0.3446f,
        0.247f,
        -0.1138f,
        0.3095f,
        -0.4076f,
        -0.3458f,
        -0.1311f,
        -0.4124f,
        -0.4916f,
        -0.226f,
        -0.3731f,
        0.2861f,
        0.042f,
        0.0459f,
        0.4367f,
        -0.3305f,
        0.4093f,
        -0.2109f,
        0.4994f,
        -0.0135f,
        0.1043f,
        0.1708f,
        0.4064f,
        0.4825f,
        -0.3178f,
        -0.3433f,
        -0.3345f,
        -0.3157f,
        0.4847f,
        0.4534f,
        0.4644f,
        -0.36f,
        0.3082f,
        -0.1495f,
        -0.4427f,
        -0.4916f,
        0.1126f,
        -0.0738f,
        -0.4039f,
        0.1211f,
        0.3081f,
        -0.1691f,
        0.015f,
        -0.15f,
        0.3769f,
        -0.0484f,
        0.0196f,
        0.394f,
        0.2407f,
        0.1268f,
        -0.1196f,
        -0.0472f,
        -0.2025f,
        -0.2132f,
        -0.0648f,
        -0.0203f,
        0.0514f,
        0.2087f,
        0.1641f,
        0.036f,
        0.2699f,
        0.2363f,
        0.2839f,
        0.078f,
        -0.3053f,
        0.3413f,
        0.0864f,
        0.4152f,
        -0.1247f,
        0.2904f,
        0.1441f,
        -0.3166f,
        -0.3787f,
        -0.341f,
        0.1413f,
        -0.394f,
        0.2185f,
        -0.2313f,
        0.5f,
        0.067f,
        -0.4967f,
        -0.0117f,
        -0.3723f,
        -0.1992f,
        0.383f,
        0.0629f,
        0.3885f,
        0.0422f,
        -0.1206f,
        0.1604f,
        -0.314f,
        -0.3508f,
        0.0045f,
        0.4699f,
        0.3351f,
        0.3071f,
        0.419f,
        0.0294f,
        0.2223f,
        -0.0978f,
        -0.1803f,
        -0.1337f,
        0.0856f,
        0.0489f,
        0.1333f,
        -0.1653f,
        0.1549f,
        -0.0404f,
        0.2114f,
        0.2628f,
        -0.4734f,
        0.2147f,
        -0.1411f,
        -0.3457f,
        -0.4845f,
        -0.2582f,
        0.3251f,
        -0.4882f,
        0.284f,
        -0.2955f,
        0.1722f,
        0.47f,
        -0.0384f,
        -0.2155f,
        -0.4524f,
        -0.0954f,
        -0.4084f,
        0.4667f,
        0.434f,
        0.3139f,
        -0.1312f,
        -0.1385f,
        -0.212f,
        -0.4378f,
        0.4105f,
        0.4213f,
        0.0048f,
        0.1732f,
        -0.1191f,
        -0.2839f,
        0.0438f,
        0.0154f,
        0.4309f,
        0.4027f,
        0.2776f,
        -0.4458f,
        -0.2477f,
        0.1026f,
        -0.434f,
        -0.3559f,
        0.415f,
        0.3461f,
        -0.2781f,
        -0.0155f,
        -0.2616f,
        -0.2304f,
        0.3891f,
        -0.17f,
        -0.1559f,
        0.4309f,
        -0.2483f,
        0.3208f,
        -0.2077f,
        0.1477f,
        0.4909f,
        -0.1894f,
        0.0689f,
        -0.0044f,
        0.4839f,
        -0.4424f,
        0.2118f,
        0.1355f,
        0.1809f,
        0.2505f,
        0.0382f,
        -0.0416f,
        0.3047f,
        0.3984f,
        -0.3311f,
        0.4786f,
        -0.4575f,
        -0.4162f,
        0.4325f,
        -0.2356f,
        0.1762f,
        -0.3292f,
        0.1419f,
        0.0652f,
        0.1088f,
        0.486f,
        -0.004f,
        0.4684f,
        0.4146f,
        0.3963f,
        0.116f,
        0.4054f,
        -0.2932f,
        -0.2072f,
        0.0089f,
        -0.3094f,
        -0.0417f,
        -0.1715f,
        0.4341f,
        -0.3608f,
        -0.4211f,
        -0.0278f,
        0.2055f,
        0.4915f,
        -0.0216f,
        0.3744f,
        -0.4222f,
        0.0209f,
        -0.4339f,
        -0.3819f,
        0.2854f,
        0.2423f,
        -0.1032f,
        0.0351f,
        -0.0846f,
        -0.4945f,
        0.0211f,
        -0.4808f,
        0.474f,
        0.0435f,
        0.4155f,
        0.1978f,
        -0.0511f,
        -0.3777f,
        0.4906f,
        0.4578f,
        -0.0792f,
        -0.0511f,
        -0.1059f,
        -0.1452f,
        0.0881f,
        -0.027f,
        0.4349f,
        -0.2065f,
        0.0723f,
        -0.0867f,
        -0.2242f,
        0.1501f,
        -0.458f,
        -0.1581f,
        0.3761f,
        0.4353f,
        -0.308f,
        -0.2272f,
        -0.0296f,
        0.1074f,
        -0.1137f,
        0.4915f,
        0.1267f,
        -0.0319f,
        0.0349f,
        0.15f,
        -0.3342f,
        -0.4084f,
        0.3802f,
        -0.3436f,
        -0.3428f,
        -0.199f,
        0.1053f,
        0.0513f,
        0.2637f,
        -0.1988f,
        -0.3679f,
        0.3065f,
        0.2026f,
        0.2044f,
        -0.1724f,
        0.4784f,
        -0.0377f,
        -0.1304f,
        -0.0719f,
        -0.1617f,
        -0.1951f,
        0.1202f,
        0.1112f,
        0.2753f,
        -0.1646f,
        0.4975f,
        0.3746f,
        -0.4301f,
        -0.0345f,
        0.0173f,
        0.22f,
        0.2392f,
        0.109f,
        0.1001f,
        0.3956f,
        0.3741f,
        -0.4911f,
        0.1087f,
        -0.0746f,
        0.3806f,
        -0.4823f,
        0.1654f,
        0.187f,
        0.2204f,
        -0.1302f,
        -0.4855f,
        0.3066f,
        0.3321f,
        -0.1159f,
        -0.1574f,
        -0.2217f,
        0.2969f,
        -0.4294f,
        0.4974f,
        0.0721f,
        -0.094f,
        0.4948f,
        -0.0534f,
        -0.024f,
        0.0681f,
        -0.4282f,
        -0.1962f,
        -0.1928f,
        0.1808f,
        -0.4883f,
        -0.1893f,
        0.1627f,
        -0.4793f,
        0.4194f,
        -0.3041f,
        0.4013f,
        0.4372f,
        0.3614f,
        0.1961f,
        0.2654f,
        -0.2689f,
        0.3185f,
        0.1798f,
        -0.329f,
        -0.2975f,
        -0.4777f,
        0.0572f,
        0.4995f,
        -0.4071f,
        0.0545f,
        0.1794f,
        0.1069f,
        0.0492f,
        -0.2661f,
        -0.4172f,
        -0.2749f,
        -0.1943f,
        -0.1134f,
        0.0324f,
        -0.4057f,
        -0.1017f,
        0.3431f,
        0.257f,
        0.0269f,
        0.3703f,
        0.453f,
        0.036f,
        0.4153f,
        0.3143f,
        -0.1601f,
        0.2885f,
        -0.3468f,
        -0.3417f,
        -0.0317f,
        -0.0679f,
        -0.1391f,
        0.0985f,
        0.4894f,
        -0.0318f,
        0.2994f,
        0.0438f,
        -0.3525f,
        -0.0938f,
        -0.2991f,
        -0.0107f,
        0.0969f,
        -0.074f,
        0.295f,
        -0.4087f,
        -0.4338f,
        0.3893f,
        0.0975f,
        -0.4829f,
        0.1463f,
        -0.2677f,
        0.3875f,
        0.2071f,
        0.2683f,
        0.3027f,
        0.0213f,
        -0.3918f,
        0.0912f,
        0.2824f,
        -0.1256f,
        -0.3327f,
        -0.2856f,
        0.2353f,
        0.3737f,
        -0.2963f,
        -0.1887f,
        0.173f,
        0.2476f,
        0.0668f,
        -0.313f,
        0.4485f,
        -0.444f,
        0.2839f,
        -0.0177f,
        0.3511f,
        0.4831f,
        0.0486f,
        0.3482f,
        0.0806f,
        0.0657f,
        -0.0056f,
        0.3129f,
        -0.0469f,
        -0.2986f,
        0.189f,
        -0.1363f,
        0.3306f,
        0.4051f,
        -0.4373f,
        0.1129f,
        -0.2205f,
        -0.1621f,
        0.3274f,
        -0.3774f,
        -0.2885f,
        -0.3611f,
        0.0419f,
        0.3846f,
        0.3865f,
        -0.3914f,
        -0.3206f,
        0.4428f,
        -0.2275f,
        -0.4288f,
        -0.0749f,
        -0.2686f,
        -0.4458f,
        -0.4185f,
        -0.4204f,
        0.2427f,
        0.1472f,
        0.074f,
        0.1634f,
        -0.2918f,
        0.3834f,
        -0.1476f,
        0.1798f,
        0.3218f,
        -0.1347f,
        0.2425f,
        -0.0653f,
        0.2527f,
        -0.4197f,
        -0.1301f,
        0.3754f,
        -0.2082f,
        0.0088f,
        0.0251f,
        -0.2158f,
        0.0032f,
        0.1337f,
        -0.0363f,
        0.0538f,
        -0.486f,
        0.0349f,
        -0.4133f,
        -0.2545f,
        0.197f,
        -0.3318f,
        -0.1749f,
        -0.0603f,
        0.4234f,
        -0.4931f,
        -0.397f,
        -0.3685f,
        0.4982f,
        0.0633f,
        0.3113f,
        0.32f,
        -0.4635f,
        0.1616f,
        -0.1375f,
        0.2892f,
        0.3498f,
        0.2324f,
        0.2724f,
        -0.3585f,
        -0.151f,
        -0.2026f,
        0.0337f,
        0.4601f,
        -0.4611f,
        -0.3948f,
        0.0139f,
        -0.447f,
        0.248f,
        0.1006f,
        -0.2015f,
        -0.0551f,
        0.3768f,
        0.1236f,
        0.4925f,
        0.3001f,
        0.2384f,
        -0.2966f,
        0.4316f,
        0.2366f,
        0.2667f,
        0.3507f,
        0.0565f,
        0.4111f,
        0.1201f,
        0.419f,
        0.2003f,
        -0.0302f,
        0.2592f,
        0.0805f,
        0.1113f,
        -0.284f,
        0.378f,
        -0.2472f,
        -0.3239f,
        0.4169f,
        -0.142f,
        0.2979f,
        -0.4223f,
        -0.3941f,
        0.0063f,
        -0.1238f,
        0.1588f,
        -0.117f,
        -0.3924f,
        0.1512f,
        -0.317f,
        0.3461f,
        0.3546f,
        -0.2776f,
        0.1905f,
        0.2292f,
        -0.3191f,
        -0.2531f,
        0.1402f,
        0.301f,
        -0.2263f,
        -0.0517f,
        -0.2293f,
        -0.3593f,
        -0.4712f,
        0.49f,
        -0.0354f,
        -0.4854f,
        -0.2573f,
        0.1407f,
        0.4315f,
        0.2087f,
        0.0464f,
        -0.3829f,
        0.4225f,
        -0.4473f,
        -0.0067f,
        0.0812f,
        -0.0643f,
        0.2089f,
        -0.1597f,
        0.2267f,
        0.1628f,
        -0.3052f,
        0.4491f,
        -0.1468f,
        0.424f,
        -0.2621f,
        0.1001f,
        0.1721f,
        -0.4612f,
        0.4817f,
        -0.3797f,
        -0.1904f,
        -0.3777f,
        -0.3509f,
        -0.0926f,
        0.1948f,
        -0.2284f,
        0.1501f,
        -0.1645f,
        -0.189f,
        -0.0334f,
        0.4898f,
        -0.0719f,
        -0.1109f,
        -0.4576f,
        -0.4708f,
        -0.4219f,
        0.0861f,
        0.2381f,
        -0.0816f,
        -0.1873f,
        -0.0992f,
        0.2212f,
        -0.1304f,
        0.254f,
        0.253f,
        0.1075f,
        -0.0381f,
        -0.075f,
        0.2543f,
        -0.0565f,
        0.0454f,
        -0.3283f,
        0.1737f,
        0.3024f,
        0.0791f,
        -0.1315f,
        -0.426f,
        -0.163f,
        0.3119f,
        -0.115f,
        0.3037f,
        0.4095f,
        0.421f,
        -0.1994f,
        0.452f,
        0.4502f,
        -0.1213f,
        0.1459f,
        0.1883f,
        0.4051f,
        0.4586f,
        -0.3031f,
        0.1262f,
        -0.064f,
        0.451f,
        -0.1209f,
        -0.3486f,
        0.0207f,
        0.4121f,
        0.4057f,
        -0.4279f,
        -0.0426f,
        -0.3148f,
        0.3537f,
        -0.1324f,
        0.2643f,
        -0.17f,
        0.0495f,
        -0.3988f,
        -0.2503f,
        -0.4577f,
        0.4049f,
        -0.3408f,
        0.4633f,
        -0.1867f,
        -0.3889f,
        -0.4787f,
        0.1921f,
        0.257f,
        0.2096f,
        0.205f,
        0.3234f,
        -0.4857f,
        -0.1689f,
        -0.1328f,
        -0.4269f,
        0.3181f,
        0.0186f,
        0.0939f,
        0.2301f,
        0.0321f,
        0.166f,
        -0.2047f,
        0.2173f,
        0.1275f,
        0.2709f,
        0.0894f,
        0.4576f,
        -0.1797f,
        0.1907f,
        -0.2928f,
        -0.0294f,
        0.2034f,
        -0.1336f,
        0.0417f,
        -0.4833f,
        0.0854f,
        0.063f,
        0.3167f,
        -0.1577f,
        -0.1196f,
        0.0216f,
        -0.2265f,
        -0.1052f,
        0.4606f,
        0.1408f,
        -0.0321f,
        0.3865f,
        -0.2328f,
        -0.3304f,
        0.1166f,
        0.4073f,
        0.4435f,
        -0.4803f,
        0.2324f,
        0.071f,
        0.2906f,
        -0.1782f,
        0.0285f,
        -0.2812f,
        -0.3797f,
        0.3436f,
        0.1894f,
        0.3237f,
        -0.29f,
        -0.1611f,
        0.4483f,
        0.2954f,
        0.402f,
        0.2649f,
        -0.2544f,
        -0.1098f,
        -0.1057f,
        0.0191f,
        0.3929f,
        -0.0373f,
        -0.3402f,
        -0.1392f,
        -0.1508f,
        0.035f,
        0.1383f,
        -0.4264f,
        -0.0578f,
        0.0818f,
        -0.4067f,
        -0.3255f,
        -0.3473f,
        0.384f,
        -0.0037f,
        0.2891f,
        -0.3973f,
        0.1166f,
        0.1326f,
        0.4f,
        0.0482f,
        0.4505f,
        -0.2612f,
        -0.0036f,
        0.2459f,
        -0.2514f,
        -0.1308f,
        -0.4007f,
        0.1389f,
        0.3714f,
        0.1184f,
        0.1396f,
        -0.1659f,
        0.3862f,
        -0.4997f,
        0.2912f,
        -0.0789f,
        0.2466f,
        0.3648f,
        0.4712f,
        -0.1717f,
        -0.4341f,
        -0.3544f,
        0.0889f,
        0.4499f,
        0.2499f,
        -0.0142f,
        -0.3396f,
        -0.0257f,
        -0.3817f,
        -0.4397f,
        -0.3697f,
        -0.3233f,
        -0.0929f,
        0.2347f,
        -0.4696f,
        0.1557f,
        -0.3962f,
        -0.3703f,
        -0.0976f,
        0.4752f,
        0.2481f,
        -0.458f,
        -0.0829f,
        0.2421f,
        -0.3498f,
        -0.1839f,
        -0.229f,
        0.3968f,
        -0.3192f,
        -0.2579f,
        -0.1671f,
        -0.2532f,
        -0.0043f,
        0.4218f,
        -0.1955f,
        -0.2545f,
        -0.0925f,
        -0.035f,
        0.3277f,
        0.1338f,
        0.1332f,
        -0.4341f,
        0.4184f,
        -0.4598f,
        0.3006f,
        0.4488f,
        0.3039f,
        0.4044f,
        -0.4216f,
        -0.2938f,
        0.4874f,
        0.4344f,
        -0.1439f,
        0.0124f,
        0.1765f,
        0.1143f,
        0.3285f,
        0.4475f,
        0.011f,
        -0.3828f,
        -0.3104f,
        0.4519f,
        -0.0281f,
        0.2932f,
        0.4815f,
        0.2764f,
        -0.3535f,
        -0.0031f,
        -0.1508f,
        0.4742f,
        -0.3694f,
        0.4825f,
        -0.46f,
        -0.3432f,
        -0.4774f,
        0.3406f,
        -0.3945f,
        0.3265f,
        0.3528f,
        -0.3161f,
        -0.3594f,
        0.4481f,
        -0.2739f,
        -0.0033f,
        -0.0396f,
        0.4026f,
        -0.3891f,
        -0.2112f,
        0.458f,
        0.2299f,
        0.0139f,
        -0.2446f,
        0.1817f,
        0.4858f,
        -0.4515f,
        0.271f,
        0.2622f,
        -0.305f,
        -0.2322f,
        -0.2808f,
        -0.223f,
        0.0063f,
        -0.2984f,
        -0.075f,
        0.1631f,
        -0.2757f,
        -0.2345f,
        0.2686f,
        -0.3414f,
        -0.2738f,
        -0.4397f,
        -0.2008f,
        -0.3258f,
        -0.2135f,
        0.4039f,
        0.1346f,
        -0.2031f,
        -0.3774f,
        -0.4688f,
        -0.2452f,
        0.4604f,
        0.0452f,
        0.1181f,
        0.2499f,
        0.1388f,
        0.1666f,
        0.0209f,
        -0.0991f,
        0.4695f,
        0.2887f,
        0.2281f,
        -0.2535f,
        -0.205f,
        0.4297f,
        0.1715f,
        0.4581f,
        -0.2382f,
        -0.4552f,
        0.3346f,
        -0.0795f,
        -0.1211f,
        0.3949f,
        0.3276f,
        0.0531f,
        -0.2108f,
        0.3393f,
        -0.2045f,
        0.0862f,
        0.462f,
        -0.1732f,
        0.4489f,
        0.4223f,
        0.4799f,
        0.067f,
        0.1722f,
        0.1186f,
        -0.1586f,
        -0.307f,
        -0.3726f,
        -0.0812f,
        0.4817f,
        0.3555f,
        0.1653f,
        -0.1155f,
        0.2851f,
        -0.0554f,
        -0.1574f,
        -0.3452f,
        0.0973f,
        -0.3229f,
        0.0753f,
        0.4763f,
        -0.3202f,
        0.0107f,
        0.0293f,
        0.077f,
        -0.15f,
        0.3249f,
        -0.3369f,
        -0.1881f,
        -0.2405f,
        -0.2802f,
        -0.158f,
        -0.2607f,
        0.3947f,
        -0.4859f,
        0.4658f,
        -0.156f,
        -0.185f,
        -0.4069f,
        0.2628f,
        -0.0954f,
        0.4486f,
        0.0359f,
        0.2891f,
        0.3416f,
        0.4805f,
        -0.3684f,
        0.4964f,
        0.0777f,
        -0.0834f,
        0.1795f,
        0.0539f,
        0.2044f,
        -0.3099f,
        -0.4168f,
        -0.2187f,
        0.148f,
        -0.4841f,
        0.0523f,
        -0.4323f,
        -0.2246f,
        0.2721f,
        -0.0902f,
        0.1226f,
        0.2747f,
        0.0318f,
        0.0883f,
        -0.3814f,
        0.4548f,
        0.2894f,
        0.4893f,
        -0.1407f,
        0.2379f,
        0.0251f,
        -0.3517f,
        0.1873f,
        0.0055f,
        -0.1121f,
        0.2915f,
        -0.309f,
        0.4124f,
        0.0788f,
        0.3529f,
        0.1167f,
        0.3769f,
        -0.4561f,
        0.398f,
        0.0248f,
        -0.4402f,
        -0.0498f,
        0.0926f,
        -0.0569f,
        -0.1698f,
        -0.3898f,
        -0.4344f,
        -0.2873f,
        0.142f,
        0.2618f,
        -0.0608f,
        0.0967f,
        0.0511f,
        -0.0716f,
        -0.4362f,
        -0.1031f,
        0.4535f,
        -0.2878f,
        -0.308f,
        0.0668f,
        0.208f,
        0.4836f,
        0.3657f,
        0.1203f,
        0.0623f,
        0.2185f,
        -0.263f,
        -0.0609f,
        0.2624f,
        -0.2572f,
        -0.4282f,
        0.4301f,
        0.1931f,
        0.1644f,
        -0.1269f,
        -0.3689f,
        0.2746f,
        0.0466f,
        -0.1562f,
        0.0244f,
        -0.1916f,
        0.283f,
        -0.271f,
        0.3595f,
        -0.2887f,
        -0.2072f,
        -0.1358f,
        -0.2274f,
        0.1129f,
        0.0563f,
        0.4473f,
        -0.1792f,
        0.0398f,
        0.4208f,
        -0.451f,
        -0.2901f,
        0.1393f,
        -0.214f,
        0.257f,
        0.0095f,
        0.0288f,
        0.3288f,
        -0.0604f,
        -0.1703f,
        0.101f,
        0.3127f,
        -0.0392f,
        -0.0166f,
        -0.0328f,
        0.3046f,
        -0.4923f,
        0.2756f,
        0.1954f,
        -0.1554f,
        0.2429f,
        -0.4855f,
        0.2454f,
        -0.285f,
        -0.2129f,
        -0.1418f,
        0.2713f,
        -0.1578f,
        0.287f,
        -0.0811f,
        -0.237f,
        0.336f,
        0.1288f,
        -0.4899f,
        -0.3781f,
        -0.0064f,
        0.1275f,
        0.2586f,
        -0.1777f,
        -0.433f,
        -0.3038f,
        -0.4689f,
        0.4877f,
        0.157f,
        0.0145f,
        -0.0452f,
        0.0695f,
        0.1302f,
        -0.1618f,
        -0.1273f,
        0.4748f,
        -0.3111f,
        -0.1127f,
        0.2201f,
        -0.0961f,
        0.1744f,
        -0.3138f,
        -0.3249f,
        -0.3755f,
        0.4732f,
        0.2019f,
        -0.0046f,
        0.417f,
        -0.0614f,
        0.1134f,
        -0.3533f,
        0.4322f,
        -0.2592f,
        0.4053f,
        -0.1376f,
        -0.1921f,
        -0.3986f,
        -0.1065f,
        -0.0966f,
        0.3664f,
        0.4081f,
        0.4661f,
        0.0437f,
        0.0382f,
        -0.1958f,
        0.4164f,
        0.1208f,
        0.101f,
        -0.1964f,
        -0.1591f,
        -0.4951f,
        -0.4141f,
        0.135f,
        -0.2121f,
        -0.1817f,
        0.216f,
        0.4899f,
        0.3137f,
        0.1329f,
        -0.0716f,
        -0.073f,
        0.2796f,
        -0.0316f,
        0.2757f,
        0.2927f,
        0.3308f,
        -0.3086f,
        0.3942f,
        -0.2757f,
        0.0948f,
        0.3684f,
        -0.3677f,
        0.0608f,
        0.0199f,
        0.1705f,
        0.4729f,
        -0.0638f,
        -0.2088f,
        0.1818f,
        0.3478f,
        0.24f,
        0.2946f,
        -0.4585f,
        -0.0172f,
        -0.4176f,
        -0.1402f,
        -0.3013f,
        -0.3199f,
        -0.3266f,
        0.3316f,
        0.2164f,
        0.2083f,
        0.1111f,
        -0.3152f,
        0.0918f,
        -0.0962f,
        -0.4845f,
        0.2833f,
        -0.0942f,
        -0.2602f,
        -0.122f,
        -0.118f,
        -0.02f,
        -0.4533f,
        0.4019f,
        -0.2417f,
        -0.3726f,
        -0.0541f,
        0.1575f,
        0.4171f,
        -0.2064f,
        -0.1026f,
        0.2116f,
        -0.1649f,
        0.4882f,
        0.402f,
        0.3028f,
        -0.3132f,
        -0.418f,
        -0.416f,
        -0.3737f,
        0.4064f,
        0.2923f,
        0.3453f,
        -0.4089f,
        -0.1159f,
        -0.1431f,
        -0.2855f,
        -0.2248f,
        0.2627f,
        0.0623f,
        0.2611f,
        -0.2475f,
        -0.4578f,
        0.3078f,
        -0.2378f,
        -0.1994f,
        -0.4569f,
        0.2082f,
        0.4581f,
        0.4602f,
        -0.3904f,
        -0.0367f,
        0.2797f,
        0.0527f,
        0.0593f,
        0.1816f,
        -0.1446f,
        0.2462f,
        0.3715f,
        -0.0605f,
        0.4804f,
        0.2778f,
        -0.1604f,
        0.3256f,
        0.4768f,
        0.3316f,
        -0.3175f,
        -0.3087f,
        -0.3933f,
        -0.447f,
        0.3615f,
        0.3679f,
        -0.0865f,
        -0.4885f,
        0.2835f,
        0.1757f,
        -0.1879f,
        0.4345f,
        -0.1162f,
        -0.122f,
        0.3947f,
        -0.0065f,
        0.4493f,
        0.1743f,
        -0.346f,
        0.1164f,
        -0.0363f,
        0.0094f,
        -0.0296f,
        -0.1649f,
        -0.4433f,
        -0.0493f,
        -0.3871f,
        -0.1036f,
        -0.2237f,
        -0.4104f,
        -0.1642f,
        0.0667f,
        -0.1112f,
        -0.0574f,
        0.1197f,
        -0.2498f,
        -0.0817f,
        -0.4669f,
        -0.2382f,
        -0.2983f,
        0.3168f,
        0.1818f,
        -0.3638f,
        -0.2995f,
        -0.3323f,
        -0.3613f,
        0.3019f,
        -0.2752f,
        0.4209f,
        0.4559f,
        0.3412f,
        -0.1155f,
        0.0732f,
        -0.1884f,
        0.2196f,
        0.1299f,
        0.3702f,
        0.4404f,
        -0.3659f,
        -0.3536f,
        -0.3621f,
        -0.03f,
        0.321f,
        0.0267f,
        -0.4796f,
        0.0486f,
        0.3849f,
        -0.0613f,
        0.1896f,
        -0.2455f,
        0.2483f,
        0.0063f,
        -0.4559f,
        0.4924f,
        0.2069f,
        -0.2882f,
        -0.2611f,
        0.1166f,
        0.0445f,
        -0.3403f,
        0.1804f,
        -0.0064f,
        0.1521f,
        -0.1386f,
        0.4131f,
        -0.0204f,
        -0.4009f,
        0.2832f,
        -0.0801f,
        -0.2667f,
        -0.4625f,
        0.0578f,
        0.3112f,
        0.3585f,
        -0.3076f,
        0.3316f,
        -0.093f,
        -0.4228f,
        -0.1219f,
        -0.4034f,
        -0.1683f,
        -0.2658f,
        0.1029f,
        -0.1242f,
        -0.2734f,
        -0.0824f,
        0.1956f,
        -0.0345f,
        -0.3579f,
        -0.1521f,
        0.2332f,
        0.3225f,
        0.3415f,
        -0.1148f,
        -0.2083f,
        0.2545f,
        0.4727f,
        -0.1092f,
        0.0377f,
        -0.1074f,
        0.232f,
        0.1831f,
        -0.4418f,
        0.0431f,
        0.0415f,
        -0.2494f,
        -0.0175f,
        0.4486f,
        -0.0643f,
        0.4685f,
        -0.347f,
        0.2674f,
        -0.2973f,
        0.2559f,
        -0.2489f,
        -0.0707f,
        -0.2186f,
        -0.4455f,
        -0.4974f,
        -0.0765f,
        -0.0976f,
        0.2358f,
        -0.1462f,
        -0.1482f,
        -0.2711f,
        0.1455f,
        -0.2859f,
        -0.2985f,
        -0.4638f,
        0.2518f,
        0.202f,
        0.2683f,
        0.0427f,
        0.3682f,
        -0.0808f,
        -0.4158f,
        -0.3813f,
        0.4018f,
        -0.3594f,
        0.0545f,
        0.3702f,
        -0.2064f,
        -0.0703f,
        -0.3193f,
        -0.3427f,
        0.2887f,
        0.11f,
        -0.0613f,
        0.3432f,
        0.2206f,
        0.4701f,
        -0.1465f,
        -0.0437f,
        -0.1762f,
        0.2053f,
        0.2931f,
        -0.4229f,
        0.4194f,
        -0.3975f,
        -0.3866f,
        0.2791f,
        0.4124f,
        0.4896f,
        -0.1783f,
        0.2805f,
        0.0166f,
        0.0138f,
        0.3993f,
        0.0262f,
        0.1544f,
        0.0616f,
        0.0043f,
        0.448f,
        0.4913f,
        0.185f,
        -0.3947f,
        0.3879f,
        -0.0971f,
        0.1519f,
        0.231f,
        -0.3766f,
        0.122f,
        -0.3077f,
        0.1876f,
        0.4458f,
        -0.4946f,
        -0.0193f,
        -0.4771f,
        0.4249f,
        0.1911f,
        -0.2558f,
        0.3118f,
        0.1034f,
        -0.1584f,
        -0.2587f,
        -0.0082f,
        0.3582f,
        0.2552f,
        -0.0011f,
        -0.0077f,
        -0.0905f,
        -0.4396f,
        0.4966f,
        -0.0346f,
        -0.3405f,
        0.1815f,
        0.0707f,
        -0.3448f,
        -0.4157f,
        -0.2775f,
        0.3863f,
        -0.2923f,
        0.3445f,
        -0.3136f,
        0.3954f,
        0.3982f,
        -0.3081f,
        -0.0161f,
        -0.4711f,
        -0.2754f,
        -0.2172f,
        -0.2269f,
        -0.4637f,
        0.4941f,
        0.2226f,
        -0.1145f,
        -0.0142f,
        0.1886f,
        -0.3594f,
        0.4847f,
        -0.3192f,
        0.1581f,
        -0.3471f,
        -0.3227f,
        -0.3766f,
        -0.0796f,
        0.3589f,
        0.1941f,
        0.0756f,
        -0.449f,
        -0.4756f,
        0.0697f,
        -0.2412f,
        0.4769f,
        0.2562f,
        -0.238f,
        0.375f,
        -0.4441f,
        0.3538f,
        0.4039f,
        -0.2195f,
        -0.3635f,
        -0.2152f,
        -0.1832f,
        -0.3695f,
        -0.3848f,
        0.2023f,
        0.2243f,
        0.3038f,
        0.4509f,
        0.3168f,
        0.4847f,
        0.1089f,
        -0.4224f,
        -0.3381f,
        0.2323f,
        0.1059f,
        -0.3714f,
        0.0342f,
        -0.2107f,
        -0.3203f,
        0.0586f,
        0.3591f,
        0.0464f,
        0.1433f,
        0.2231f,
        0.3084f,
        0.0182f,
        0.3869f,
        0.1621f,
        0.0299f,
        -0.3327f,
        0.4065f,
        0.4226f,
        0.092f,
        -0.4631f,
        -0.4623f,
        -0.0979f,
        0.3691f,
        0.3416f,
        -0.1471f,
        0.1859f,
        0.4341f,
        -0.4304f,
        0.2635f,
        -0.4041f,
        0.3019f,
        -0.1307f,
        -0.2754f,
        -0.0561f,
        0.1586f,
        0.0122f,
        -0.4975f,
        0.1255f,
        -0.4415f,
        0.2537f,
        -0.0436f,
        0.3669f,
        -0.2281f,
        -0.1568f,
        0.1368f,
        0.4097f,
        0.0105f,
        0.0432f
    )
    val vigTable = floatArrayOf(
        0.78125f,
        0.8125f,
        0.8359375f,
        0.859375f,
        0.8828125f,
        0.8984375f,
        0.9140625f,
        0.921875f,
        0.921875f,
        0.921875f,
        0.9140625f,
        0.89843756f,
        0.8828125f,
        0.85937506f,
        0.8359375f,
        0.81250006f,
        0.78883165f,
        0.8046875f,
        0.828125f,
        0.859375f,
        0.8828125f,
        0.90625f,
        0.921875f,
        0.9375f,
        0.9453125f,
        0.9453125f,
        0.9453125f,
        0.9375f,
        0.921875f,
        0.90625f,
        0.8828125f,
        0.859375f,
        0.828125f,
        0.8046875f,
        0.8203125f,
        0.8515625f,
        0.8828125f,
        0.90625f,
        0.9296875f,
        0.9453125f,
        0.9609375f,
        0.96875f,
        0.96875f,
        0.96875f,
        0.9609375f,
        0.9453125f,
        0.9296875f,
        0.90625006f,
        0.8828125f,
        0.8515625f,
        0.82031256f,
        0.8359375f,
        0.8671875f,
        0.8984375f,
        0.921875f,
        0.9453125f,
        0.9609375f,
        0.9765625f,
        0.9765625f,
        0.984375f,
        0.9765625f,
        0.9765625f,
        0.96093744f,
        0.9453125f,
        0.92187494f,
        0.89843756f,
        0.8671875f,
        0.8359375f,
        0.84375f,
        0.875f,
        0.90625f,
        0.9375f,
        0.9609375f,
        0.9765625f,
        0.984375f,
        0.9921875f,
        0.9921875f,
        0.9921875f,
        0.984375f,
        0.9765625f,
        0.9609375f,
        0.9375f,
        0.90625f,
        0.875f,
        0.84375f,
        0.8515625f,
        0.8828125f,
        0.9140625f,
        0.9453125f,
        0.96875f,
        0.9765625f,
        0.9921875f,
        0.9921875f,
        1.0f,
        0.9921875f,
        0.9921875f,
        0.9765625f,
        0.96874994f,
        0.9453125f,
        0.9140625f,
        0.88281256f,
        0.8515625f,
        0.8515625f,
        0.8828125f,
        0.9140625f,
        0.9453125f,
        0.96875f,
        0.984375f,
        0.9921875f,
        1.0f,
        1.0f,
        1.0f,
        0.9921875f,
        0.984375f,
        0.96875f,
        0.9453125f,
        0.9140625f,
        0.8828125f,
        0.8515625f,
        0.8515625f,
        0.8828125f,
        0.9140625f,
        0.9453125f,
        0.96875f,
        0.9765625f,
        0.9921875f,
        0.9921875f,
        1.0f,
        0.9921875f,
        0.9921875f,
        0.9765625f,
        0.96875f,
        0.9453125f,
        0.9140625f,
        0.8828125f,
        0.8515625f,
        0.84375f,
        0.875f,
        0.90625f,
        0.9375f,
        0.9609375f,
        0.9765625f,
        0.984375f,
        0.9921875f,
        0.9921875f,
        0.9921875f,
        0.984375f,
        0.9765625f,
        0.9609375f,
        0.9375f,
        0.90625f,
        0.875f,
        0.84375f,
        0.8359375f,
        0.8671875f,
        0.8984375f,
        0.921875f,
        0.9453125f,
        0.9609375f,
        0.9765625f,
        0.9765625f,
        0.984375f,
        0.9765625f,
        0.9765625f,
        0.96093756f,
        0.9453125f,
        0.92187506f,
        0.89843744f,
        0.8671875f,
        0.8359375f,
        0.8203125f,
        0.8515625f,
        0.8828125f,
        0.90625f,
        0.9296875f,
        0.9453125f,
        0.9609375f,
        0.96875f,
        0.96875f,
        0.96875f,
        0.9609375f,
        0.9453125f,
        0.92968744f,
        0.90625f,
        0.8828125f,
        0.8515625f,
        0.8203125f,
        0.8046875f,
        0.828125f,
        0.859375f,
        0.8828125f,
        0.90625f,
        0.921875f,
        0.9375f,
        0.9453125f,
        0.9453125f,
        0.9453125f,
        0.9375f,
        0.921875f,
        0.90625f,
        0.8828125f,
        0.859375f,
        0.828125f,
        0.8046875f,
        0.7862076f,
        0.8125f,
        0.8359375f,
        0.859375f,
        0.8828125f,
        0.8984375f,
        0.9140625f,
        0.921875f,
        0.921875f,
        0.921875f,
        0.9140625f,
        0.8984375f,
        0.8828125f,
        0.859375f,
        0.8359375f,
        0.8125f,
        0.78898257f
    )

    @SuppressLint("HalfFloat")
    fun toHalfBuffer(array: FloatArray): ByteBuffer {
        val buffer = ByteBuffer.allocate(array.size*2)
        buffer.order(ByteOrder.nativeOrder())
        val shortBuffer = buffer.asShortBuffer()
        for (v in array) {
            shortBuffer.put(Half.toHalf(v))
        }
        buffer.rewind()
        return buffer
    }

    @SuppressLint("HalfFloat")
    fun toFloatArray(array: ByteArray): FloatArray {
        val floatBuffer = FloatBuffer.allocate(array.size / 2)
        val shortBuffer = ByteBuffer.wrap(array).order(ByteOrder.nativeOrder()).asShortBuffer()
        for (value in 0 until shortBuffer.capacity()) {
            floatBuffer.put(Half.toFloat(shortBuffer.get()))
        }
        floatBuffer.rewind()
        return floatBuffer.array()
    }
}