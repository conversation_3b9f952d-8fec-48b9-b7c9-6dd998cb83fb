/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - ViewTouchAdapter.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2022/01/13
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery		2022/01/13		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.recyclerview.view

import android.view.View
import android.view.ViewGroup

abstract class ViewTouchAdapter<V : ViewGroup>(val view: V) {
    abstract fun attach()
    abstract fun detach()
    abstract fun addCallback(processor: IItemTouchProcessor<V>)
    abstract fun removeCallback(processor: IItemTouchProcessor<V>)
    abstract fun findChildPosition(child: View): Int
    abstract fun findChildView(x: Float, y: Float, findNear: Boolean): View?

    abstract fun isSelectable(position: Int): Boolean

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        other as ViewTouchAdapter<*>
        if (view != other.view) return false
        return true
    }

    override fun hashCode(): Int {
        return view.hashCode()
    }
}