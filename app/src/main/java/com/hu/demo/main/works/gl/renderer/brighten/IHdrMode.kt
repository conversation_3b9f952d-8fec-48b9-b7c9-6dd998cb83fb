/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - IHdrMode.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/10/14
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery		2024/10/14		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.renderer.brighten

import android.annotation.SuppressLint
import android.hardware.DataSpace
import androidx.graphics.surface.SurfaceControlCompat
import com.hu.demo.main.works.gl.renderer.param.RenderArgs
import com.hu.demo.utils.ApiLevelUtil

/**
 * HDR提亮模式执行单元，与sf交互提亮相关信息在此模式下执行
 */
interface IHdrMode {
    /**
     * 当希望页面能够渲染提亮时调用此方法
     */
    fun startShow()

    /**
     * 执行渲染
     *
     * @param renderArgs 渲染参数
     * @param surfaceControl [surfaceControl]对象
     * @param transaction [transaction]事务对象
     */
    fun render(renderArgs: RenderArgs, surfaceControl: SurfaceControlCompat, transaction: SurfaceControlCompat.Transaction)

    /**
     * 当希望页面不再渲染提亮时调用此方法
     */
    fun stopShow()
}

/**
 * 真正实现HDR提亮的接口，子类实现
 *
 * 区别于[IHdrMode]，会提供[inWarn]、[checkHandle]、[unrender]方法，用户实际HDR提亮模式判断以及逻辑执行使用
 */
interface ISubHdrMode : IHdrMode {
    /**
     * 是否已经进入防烧屏状态
     */
    var inWarn: Boolean

    /**
     * 检查是否要处理当前的显示模式
     *
     * @param renderArgs 渲染参数
     *
     * @return 返回检查结果，true表示需要当前模式处理，false不能使用当前模式处理
     */
    fun checkHandle(renderArgs: RenderArgs): Boolean

    /**
     * 执行执行取消渲染
     *
     * @param renderArgs 渲染参数
     * @param surfaceControl [surfaceControl]对象
     * @param transaction [transaction]事务对象
     */
    fun unrender(renderArgs: RenderArgs, surfaceControl: SurfaceControlCompat, transaction: SurfaceControlCompat.Transaction)

    @SuppressLint("NewApi")
    companion object {
        /**
         * scRGB gamma编码。
         *
         * 由以下内容组成:
         * - Primaries: [DataSpace.STANDARD_BT709]
         * - Transfer: [DataSpace.TRANSFER_SRGB]
         * - Range: [DataSpace.RANGE_EXTENDED]
         */
        internal val DATASPACE_SCRGB by lazy {
            if (ApiLevelUtil.isAtLeastAndroidU()) {
                DataSpace.DATASPACE_SCRGB
            } else 0
        }

        /**
         * 扩展DCI-P3 gamma 编码。
         *
         * 由以下内容组成:
         * - Primaries: [DataSpace.STANDARD_DCI_P3]
         * - Transfer: [DataSpace.TRANSFER_SRGB]
         * - Range: [DataSpace.RANGE_EXTENDED]
         */
        internal val DATASPACE_CDISPLAY_P3 by lazy {
            if (ApiLevelUtil.isAtLeastAndroidU()) {
                DataSpace.pack(DataSpace.STANDARD_DCI_P3, DataSpace.TRANSFER_SRGB, DataSpace.RANGE_EXTENDED)
            } else 0
        }

        internal val DATASPACE_LINEAR_DISPLAY_P3 by lazy {
            if (ApiLevelUtil.isAtLeastAndroidU()) {
                DataSpace.pack(DataSpace.STANDARD_DCI_P3, DataSpace.TRANSFER_LINEAR, DataSpace.RANGE_EXTENDED)
            } else 0
        }

        internal val DATASPACE_LINEAR_SRGB by lazy {
            if (ApiLevelUtil.isAtLeastAndroidU()) {
                DataSpace.pack(DataSpace.STANDARD_BT709, DataSpace.TRANSFER_LINEAR, DataSpace.RANGE_EXTENDED)
            } else 0
        }

        /**
         * HLG gamma 编码。
         *
         * 由以下内容组成：
         * - Primaries: [DataSpace.STANDARD_BT2020]
         * - Transfer: [DataSpace.TRANSFER_HLG]
         * - Range: [DataSpace.RANGE_FULL]
         */
        internal val DATASPACE_BT2020_HLG by lazy {
            if (ApiLevelUtil.isAtLeastAndroidU()) {
                DataSpace.DATASPACE_BT2020_HLG
            } else 0
        }

        /**
         * 感知量化器编码。
         *
         * 由以下内容组成：
         * - Primaries: [DataSpace.STANDARD_BT2020]
         * - Transfer: [DataSpace.TRANSFER_ST2084]
         * - Range: [DataSpace.RANGE_FULL]
         */
        internal val DATASPACE_BT2020_PQ by lazy {
            if (ApiLevelUtil.isAtLeastAndroidU()) {
                DataSpace.DATASPACE_BT2020_PQ
            } else 0
        }

        /**
         * 自定义P3-HLG编码。
         *
         * 由以下内容组成：
         * - Primaries: [DataSpace.STANDARD_DCI_P3]
         * - Transfer: [DataSpace.TRANSFER_HLG]
         * - Range: [DataSpace.RANGE_FULL]
         */
        internal val DATASPACE_P3_HLG by lazy {
            if (ApiLevelUtil.isAtLeastAndroidU()) {
                DataSpace.pack(DataSpace.STANDARD_DCI_P3, DataSpace.TRANSFER_HLG, DataSpace.RANGE_FULL)
            } else 0
        }

        /**
         * sRGB 伽马编码。
         *
         * 由以下内容组成：
         * - Primaries: [DataSpace.STANDARD_BT709]
         * - Transfer: [DataSpace.TRANSFER_SRGB]
         * - Range: [DataSpace.RANGE_FULL]
         */
        internal val DATASPACE_SRGB by lazy {
            if (ApiLevelUtil.isAtLeastAndroidU()) {
                DataSpace.DATASPACE_SRGB
            } else 0
        }

        /**
         * Display-P3编码。
         *
         * 由以下内容组成：
         * - Primaries: [DataSpace.STANDARD_DCI_P3]
         * - Transfer: [DataSpace.TRANSFER_SRGB]
         * - Range: [DataSpace.RANGE_FULL]
         */
        internal val DATASPACE_DISPLAY_P3 by lazy {
            if (ApiLevelUtil.isAtLeastAndroidU()) {
                DataSpace.DATASPACE_DISPLAY_P3
            } else 0
        }


        /**
         * SMPTE EG 432-1 和 SMPTE RP 431-2 数字影院 DCI-P3。
         *
         * 由以下内容组成：
         * - Primaries: [DataSpace.STANDARD_DCI_P3]
         * - Transfer: [DataSpace.TRANSFER_GAMMA2_6]
         * - Range: [DataSpace.RANGE_FULL]
         */
        internal val DATASPACE_DCI_P3 by lazy {
            if (ApiLevelUtil.isAtLeastAndroidU()) {
                DataSpace.DATASPACE_DCI_P3
            } else 0
        }
    }
}