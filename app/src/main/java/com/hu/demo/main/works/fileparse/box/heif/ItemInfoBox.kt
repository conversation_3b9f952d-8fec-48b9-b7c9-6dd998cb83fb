package com.hu.demo.main.works.fileparse.box.heif

import com.hu.demo.main.works.fileparse.box.Box
import com.hu.demo.main.works.fileparse.box.FullBox
import com.hu.demo.main.works.fileparse.box.Tree
import com.hu.demo.utils.ByteOrderedDataInputStream

internal open class ItemInfoBox(parent: Tree?) : FullBox("iinf", 2, parent) {
    override val boxReadChild: Boolean = true

    var entryCount: Int? = null
        private set

    override fun readInner(bis: ByteOrderedDataInputStream) {
        super.readInner(bis)
        val version = version?.toInt() ?: return
        entryCount = if (version == 0) bis.nReadShort().toInt() else bis.nReadInt()
    }

    override fun fork(parent: Tree): Box {
        return ItemInfoBox(parent)
    }

    override fun toString(): String {
        return "${super.toString()}, entryCount:$entryCount"
    }
}