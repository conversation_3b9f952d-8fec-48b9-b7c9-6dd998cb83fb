/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : OesRawTexture.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/10/21
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2024/10/21      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.foundation.opengl2.texture

import android.graphics.ColorSpace
import android.graphics.SurfaceTexture
import android.opengl.GLES11Ext
import android.opengl.GLES30
import com.hu.demo.main.works.gl.texture.RawTexture
import com.hu.demo.main.works.gl.texture.TexConfig
import com.hu.demo.main.works.gl.utils.GlUtil
import com.hu.demo.utils.ColorSpaceExt

/**
 * OES空的纹理，开辟了纹理空间，无内容
 *
 * @param width 纹理宽
 * @param height 纹理高
 * @param texConfig 纹理类型，默认[TexConfig.ARGB_8888]
 * @param colorSpace 纹理的色域，默认[COLOR_SPACE_SRGB]
 */
class OesRawTexture(
    width: Int,
    height: Int,
    texConfig: TexConfig = TexConfig.ARGB_8888,
    colorSpace: ColorSpace = ColorSpaceExt.BT709,
) : RawTexture(width, height, colorSpace, texConfig, texTarget = GLES11Ext.GL_TEXTURE_EXTERNAL_OES) {
    override val tag: String = TAG

    /**
     * 获取SurfaceTexture 如果没有则创建一个
     */
    var surfaceTexture: SurfaceTexture? = null
        private set
        get() {
            if ((field == null) && (textureId != GLES30.GL_NONE)) {
                field = SurfaceTexture(textureId)
            }
            return field
        }

    /**
     * 为[surfaceTexture]设置[SurfaceTexture.OnFrameAvailableListener]
     */
    var onFrameAvailableListener: SurfaceTexture.OnFrameAvailableListener? = null
        set(value) {
            surfaceTexture?.setOnFrameAvailableListener(value)
        }

    override fun load() {
        super.load()
        surfaceTexture?.updateTexImage()
        GlUtil.checkGlError()
    }

    override fun reuse() {
        recycle()
    }

    override fun recycle() {
        surfaceTexture?.setOnFrameAvailableListener(null)
        surfaceTexture?.release()
        super.recycle()
    }

    private companion object {
        private const val TAG = "OesRawTexture"
    }
}