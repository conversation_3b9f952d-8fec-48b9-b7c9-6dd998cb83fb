package com.hu.demo.main.works.drawable

import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Path
import android.graphics.drawable.Drawable
import android.graphics.drawable.DrawableWrapper
import kotlin.math.min

class CircleDrawable(
    drawable: Drawable? = null,
    private val background: Drawable? = null,
    private val rotate: Float = 0f,
    lineWidth: Float = 0f,
    lineColor: Int = Color.TRANSPARENT,
    private val drawType: DrawType = DrawType.CENTER_CROP
) : DrawableWrapper(drawable) {
    private val linePaint = Paint().apply {
        isAntiAlias = true
        style = Paint.Style.STROKE
        strokeWidth = lineWidth
        color = lineColor
    }

    override fun draw(canvas: Canvas) {
        canvas.save()
        val path = Path().apply {
            val rect = (canvas.clipBounds.takeIf { !it.isEmpty } ?: bounds)
            addCircle(
                rect.width().toFloat() / 2,
                rect.height().toFloat() / 2,
                min(rect.width(), rect.height()).toFloat() / 2,
                Path.Direction.CW
            )
        }
        canvas.clipPath(path)
        canvas.drawDrawable(background, bounds.width().toFloat(), bounds.height().toFloat())
        canvas.drawDrawable(drawable, bounds.width().toFloat(), bounds.height().toFloat(), rotate, drawType)
        canvas.drawPath(path, linePaint)
        canvas.restore()
    }
}

fun Drawable.toCircleDrawable(
    rotate: Float = 0f,
    background: Drawable? = null,
    lineWidth: Float = 0f,
    lineColor: Int = Color.TRANSPARENT,
    drawType: DrawType = DrawType.CENTER_CROP
): CircleDrawable {
    return CircleDrawable(this, background, rotate, lineWidth, lineColor, drawType)
}