package com.hu.demo.main.works.paging.ui

import android.content.Context
import android.util.AttributeSet
import android.util.Log
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView

class MediaItemView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : ConstraintLayout(context, attrs) {

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        Log.d(TAG, "onMeasure: ${(layoutParams as RecyclerView.LayoutParams).viewLayoutPosition}, ${MeasureSpec.getSize(widthMeasureSpec)}")
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
    }

    companion object {
        private const val TAG = "MediaItemView"
    }
}