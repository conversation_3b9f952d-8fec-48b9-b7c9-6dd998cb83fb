package com.hu.demo.main.works.gl.renderer.image

import android.graphics.ColorSpace
import com.hu.demo.main.works.gl.renderer.normal.GamutRenderer
import com.hu.demo.main.works.gl.renderer.normal.RendererGroup
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_COLOR_SPACE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_DEVICE_HDR_SDR_RATIO
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_GAINMAP_TEXTURE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_IMAGE_TEXTURE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.getKey
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.NEXT_NP
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.PROCEDURAL_NP
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.STABLE_NP
import com.hu.demo.main.works.gl.renderer.param.RenderArgs
import com.hu.demo.main.works.gl.utils.GlProgram
import com.hu.demo.utils.ApiLevelUtil
import com.hu.demo.utils.ColorSpaceExt.toGamma2_2

class UhdrRendererGroup : RendererGroup(TAG) {
    init {
        if (ApiLevelUtil.isAtLeastAndroidU()) {
            addChild(new(UhdrRenderer::class.java))
        }
        addChild(new(GamutRenderer::class.java))
    }

    override fun render(renderArgs: RenderArgs, glProgram: GlProgram?) {
        renderArgs.remove(NEXT_NP)

        // 目前发现gamma2.2是oppo显示用的gamma，并不是srgb
        val colorSpace = renderArgs.get<ColorSpace>(PROCEDURAL_NP.getKey(KEY_COLOR_SPACE))?.run {
            if ((renderArgs.get<Float>(STABLE_NP.getKey(KEY_DEVICE_HDR_SDR_RATIO)) ?: 1.0f) > 1.0f) {
                toGamma2_2()
            } else this
        }

        // 如果当前是图片渲染，则设置当前要给SF的色域为屏幕的色域，如果图片是UHDR的，则会在UhdrRenderer中再次修改
        renderArgs[PROCEDURAL_NP.getKey(KEY_COLOR_SPACE)] = colorSpace

        renderArgs[NEXT_NP.getKey(KEY_COLOR_SPACE)] = colorSpace

        renderArgs[NEXT_NP.getKey(KEY_GAINMAP_TEXTURE)] = renderArgs.get<Any>(PROCEDURAL_NP.getKey(KEY_GAINMAP_TEXTURE))
        renderArgs[NEXT_NP.getKey(KEY_IMAGE_TEXTURE)] = renderArgs.get<Any>(PROCEDURAL_NP.getKey(KEY_IMAGE_TEXTURE))

        super.render(renderArgs, glProgram)
    }

    companion object {
        const val TAG = "UhdrRendererGroup"
    }
}