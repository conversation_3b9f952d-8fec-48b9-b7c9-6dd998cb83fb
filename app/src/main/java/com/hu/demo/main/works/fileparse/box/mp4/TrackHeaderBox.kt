package com.hu.demo.main.works.fileparse.box.mp4

import com.hu.demo.main.works.fileparse.box.Box
import com.hu.demo.main.works.fileparse.box.FullBox
import com.hu.demo.main.works.fileparse.box.Tree
import com.hu.demo.utils.ByteOrderedDataInputStream

internal class TrackHeaderBox(parent: Tree?) : FullBox("tkhd", 3, parent) {
    var creationTime: Long? = null
    var modificationTime: Long? = null
    var trackId: Int? = null
    var duration: Long? = null
    var layer: Short? = null
    var alternateGroup: Short? = null
    var volume: Short? = null
    var matrix: IntArray? = null
    var width: Int? = null
    var height: Int? = null

    override fun readInner(bis: ByteOrderedDataInputStream) {
        super.readInner(bis)
        val version = version?.toInt()!!
        if (version == 1) {
            creationTime = bis.nReadLong()
            modificationTime = bis.nReadLong()
            trackId = bis.nReadInt()
            bis.nSkipFully(4)
            duration = bis.nReadLong()
        } else {
            creationTime = bis.nReadInt().toULong().toLong()
            modificationTime = bis.nReadInt().toULong().toLong()
            trackId = bis.nReadInt()
            bis.nSkipFully(4)
            duration = bis.nReadInt().toULong().toLong()
        }
        bis.nSkipFully(4*2)
        layer = bis.nReadShort()
        alternateGroup = bis.nReadShort()
        volume = bis.nReadShort()
        bis.nSkipFully(2)
        matrix = bis.nReadInts(9)
        width = bis.nReadInt()
        height = bis.nReadInt()
    }

    override fun fork(parent: Tree): Box {
        return TrackHeaderBox(parent)
    }
}