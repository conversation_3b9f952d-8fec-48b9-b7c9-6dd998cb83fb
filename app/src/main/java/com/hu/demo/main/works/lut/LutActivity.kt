package com.hu.demo.main.works.lut

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.net.Uri
import android.opengl.GLES30
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.Display
import android.view.View
import android.view.View.OnClickListener
import android.widget.Button
import android.widget.SeekBar
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.contract.ActivityResultContract
import androidx.annotation.RequiresApi
import androidx.graphics.opengl.egl.EGLConfigAttributes
import androidx.lifecycle.lifecycleScope
import com.hu.demo.base.ui.BaseActivity
import com.hu.demo.main.R
import com.hu.demo.main.works.gl.EditingSurfaceView
import com.hu.demo.main.works.gl.OnSeekBarChangeListener2
import com.hu.demo.main.works.gl.renderer.brighten.CombineImageOnlyHdrMode
import com.hu.demo.main.works.gl.renderer.brighten.HdrModeManager
import com.hu.demo.main.works.gl.renderer.image.LutRendererGroup
import com.hu.demo.main.works.gl.renderer.normal.Renderer
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_3D_LUT_SIZE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_3D_LUT_TEXTURE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_COLOR_SPACE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_DEVICE_HDR_SDR_RATIO
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_DITHER_RATIO
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_DITHER_TABLE_TEXTURE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_GAINMAP_TEXTURE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_IMAGE_TEXTURE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_VIG_RATIO
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_VIG_TABLE_TEXTURE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.getKey
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.STABLE_NP
import com.hu.demo.main.works.gl.texture.BitmapTexture
import com.hu.demo.main.works.gl.texture.BufferTexture
import com.hu.demo.main.works.gl.texture.GainmapTexture
import com.hu.demo.main.works.gl.texture.TexConfig
import com.hu.demo.main.works.gl.utils.EditingEglSpec
import com.hu.demo.main.works.gl.utils.toBuffer
import com.hu.demo.utils.ColorSpaceExt
import com.hu.demo.utils.decodeImage
import kotlinx.coroutines.launch
import java.util.function.Consumer

@RequiresApi(Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
class LutActivity : BaseActivity(), OnClickListener, OnSeekBarChangeListener2 {
    private var glView: EditingSurfaceView? = null
    private var sbDither: SeekBar? = null
    private var sbVig: SeekBar? = null
    private var btnSelectImg: Button? = null
    private val pickTypeResultContract = PickTypeResultContract()
    private val pickImageLauncher = registerForActivityResult(
        pickTypeResultContract,
        PickOnlyCallback { changeImageData(it) }
    )
    private var percent: Float = 0f
    private var hdrSdrRatio: Float = 1.0f
    private var desiredRatio: Float = 1.0f
    private val hdrSdrListener = Consumer<Display> { display ->
        hdrSdrRatio = display?.hdrSdrRatio ?: 1.0f
        glView?.setDataAndRender(STABLE_NP.getKey(KEY_DEVICE_HDR_SDR_RATIO), hdrSdrRatio)
    }

    override fun getLayoutId(): Int {
        return R.layout.activity_gl_lut
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        if (display?.isHdrSdrRatioAvailable == true) {
            display?.registerHdrSdrRatioChangedListener({ it.run() }, hdrSdrListener)
        }
    }

    override fun initToolbar() {
        super.initToolbar()
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowTitleEnabled(true)
            setDisplayShowHomeEnabled(false)
        }
    }

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        window.colorMode = ActivityInfo.COLOR_MODE_WIDE_COLOR_GAMUT
        glView = findViewById(R.id.glView)
        sbVig = findViewById(R.id.sbVig)
        sbDither = findViewById(R.id.sbDither)
        btnSelectImg = findViewById(R.id.btnSelectImg)
    }

    override fun initEvent(savedInstanceState: Bundle?) {
        super.initEvent(savedInstanceState)
        sbVig?.setOnSeekBarChangeListener(this)
        sbDither?.setOnSeekBarChangeListener(this)
        btnSelectImg?.setOnClickListener(this)
        initRenderer()
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun initRenderer() {
        glView?.apply {
            initGlRenderer(
                EditingEglSpec(),
                {
                    loadConfig(EGLConfigAttributes.RGBA_1010102)!!
                }
            )

            initBufferRenderer(HdrModeManager(this, arrayOf(CombineImageOnlyHdrMode())))

            glView?.setDataAndRender(STABLE_NP.getKey(KEY_VIG_RATIO), sbVig!!.progress.toFloat() / sbVig!!.max.toFloat())
            glView?.setDataAndRender(STABLE_NP.getKey(KEY_DITHER_RATIO), sbDither!!.progress.toFloat() / sbDither!!.max.toFloat())

            // 设置屏幕色域
            setData(STABLE_NP.getKey(KEY_COLOR_SPACE), if (window.isWideColorGamut) ColorSpaceExt.DISPLAY_P3 else ColorSpaceExt.SRGB)

            // 添加渲染节点
            changeNode {
                it.removeChildren()
                it.addChild(Renderer.new(LutRendererGroup::class.java))
            }
        }
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.btnSelectImg -> {
                pickImageLauncher.launch(MIME_TYPE_IMAGE)
            }
        }
    }

    override fun onProgressChanged(seekBar: SeekBar, progress: Int, fromUser: Boolean) {
        if (seekBar.id == R.id.sbVig) {
            glView?.setDataAndRender(STABLE_NP.getKey(KEY_DITHER_RATIO), progress.toFloat() / seekBar.max.toFloat())
        } else if (seekBar.id == R.id.sbDither) {
            glView?.setDataAndRender(STABLE_NP.getKey(KEY_VIG_RATIO), progress.toFloat() / seekBar.max.toFloat())
        }
    }

    private suspend fun calcDisiredRatio() {
        val gainmapTexture = glView?.getData<GainmapTexture>(STABLE_NP.getKey(KEY_GAINMAP_TEXTURE))
        desiredRatio = maxOf(1.0f, (gainmapTexture?.gainmap?.displayRatioForFullHdr?.let { it * percent }) ?: 1.0f)
    }

    @SuppressLint("HalfFloat")
    private fun changeImageData(obj: Any?) {
        lifecycleScope.launch {
            val bitmap = (obj as Uri).decodeImage(this@LutActivity, 4000)
            val ditherTable = assets.open("ditherTable.bin").readBytes().toBuffer(false)
            val vigTable = assets.open("vigTable.bin").readBytes().toBuffer(false)
            val lmtLutTable = assets.open("lmt_lut_table.bin").readBytes().toBuffer(false)
            glView?.setDatasAndRender(
                mapOf(
                    STABLE_NP.getKey(KEY_IMAGE_TEXTURE) to BitmapTexture(bitmap),
                    STABLE_NP.getKey(KEY_GAINMAP_TEXTURE) to bitmap.gainmap?.let { GainmapTexture(it) },
                    STABLE_NP.getKey(KEY_DITHER_TABLE_TEXTURE) to BufferTexture(ditherTable, 64, 32, texConfig = TexConfig.R_F16).toVirtual(),
                    STABLE_NP.getKey(KEY_VIG_TABLE_TEXTURE) to BufferTexture(vigTable, 17, 13, texConfig = TexConfig.R_F16).toVirtual(),
                    STABLE_NP.getKey(KEY_3D_LUT_TEXTURE) to BufferTexture(
                        lmtLutTable,
                        16,
                        16,
                        16,
                        texConfig = TexConfig.RGB_F16,
                        texTarget = GLES30.GL_TEXTURE_3D
                    ).toVirtual(),
                    STABLE_NP.getKey(KEY_3D_LUT_SIZE) to 16.0f
                )
            )
            calcDisiredRatio()
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        display?.unregisterHdrSdrRatioChangedListener(hdrSdrListener)
    }

    override fun onDestroy() {
        super.onDestroy()
        glView?.apply {
            bufferRenderer.release(false)
            glRenderer.stop(false)
        }
    }

    private open class PickOnlyCallback(private val resultFunc: (Uri) -> Unit) : ActivityResultCallback<Uri?> {
        override fun onActivityResult(result: Uri?) {
            result ?: run {
                Log.d(TAG, "PickOnlyCallback.onActivityResult: ")
                return
            }
            Log.d(TAG, "onActivityResult: $result")
            resultFunc(result)
        }
    }

    private class PickTypeResultContract : ActivityResultContract<String, Uri?>() {
        override fun createIntent(context: Context, input: String): Intent {
            return Intent(Intent.ACTION_PICK).setType(input)
        }

        override fun parseResult(resultCode: Int, intent: Intent?): Uri? {
            return if (resultCode != Activity.RESULT_OK) null else intent?.data
        }
    }

    companion object {
        private const val TAG = "GLShowActivity"
        private const val MIME_TYPE_IMAGE = "image/*"
    }

}