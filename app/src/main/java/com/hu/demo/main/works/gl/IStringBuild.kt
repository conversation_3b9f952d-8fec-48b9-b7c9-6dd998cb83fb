/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - IStringBuild.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/11/14
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * hucanh<PERSON>@Apps.Gallery		2024/11/14		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl

/**
 * 构建toString的key-value的接口类
 */
interface IStringBuild {
    /**
     * 类标记，通常为类名
     */
    val tag: String

    /**
     * 构建toString的key-value，返回[MutableMap]类型
     *
     * @return 返回输出要打印的key-value map
     */
    fun buildString(): MutableMap<String, Any?>
}