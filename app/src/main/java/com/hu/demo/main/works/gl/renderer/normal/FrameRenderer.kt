/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - FrameRenderer.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/09/12
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * huca<PERSON><PERSON>@Apps.Gallery		2024/09/12		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.renderer.normal

import android.graphics.RectF
import android.opengl.Matrix
import androidx.graphics.lowlatency.BufferInfo
import com.hu.demo.main.works.gl.canvas.GlCanvas
import com.hu.demo.main.works.gl.canvas.SKey
import com.hu.demo.main.works.gl.canvas.ScreenGlCanvas
import com.hu.demo.main.works.gl.renderer.addToPool
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_BUFFER_INFO
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_EXTRA_MATRIX
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_HEIGHT
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_IMAGE_TEXTURE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_MIX_RATIO
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_TRANSFORM
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_WIDTH
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.getKey
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.NEXT_NP
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.PROCEDURAL_NP
import com.hu.demo.main.works.gl.renderer.param.RenderArgs
import com.hu.demo.main.works.gl.shader.GlShader
import com.hu.demo.main.works.gl.shader.ProgramShader
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.CONST_TF_PARAMS_SRGB
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.EXEC_COLOR_ALPHA_MIX
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.EXEC_SET_FRAGCOLOR
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.EXEC_SET_GL_POSITION
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.EXEC_SET_TEXCOORD
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.EXEC_TEXTURE_COLOR
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_IN_VEC2_TEX_COORD
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_LAYOUT_IN_VEC3_POSITION
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_LAYOUT_IN_VEC3_TEX_COORD
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_OUT_VEC2_TEX_COORD
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_OUT_VEC4_FRAGCOLOR
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_UNIFORM_FLOAT_MIX_RATIO
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_UNIFORM_MAT4_MODEL_M
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_UNIFORM_MAT4_PROJECTION_M
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_UNIFORM_MAT4_VIEW_M
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_UNIFORM_SAMPLER2D_TEX
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.STRUCT_TF_PARAMS
import com.hu.demo.main.works.gl.texture.ITexture
import com.hu.demo.main.works.gl.texture.Texture
import com.hu.demo.main.works.gl.utils.*
import com.hu.demo.utils.getOrLog
import kotlin.math.min

/**
 * 通用的帧渲染器，对输入的texture执行最直接的渲染
 *
 * 输入：
 *
 * | key                          | 类型           | 是否必须 | 解释               |
 * | -                            | -              | -       | -                  |
 * | [KEY_PROCEDURAL_BUFFER_INFO] | [BufferInfo]   | 是      | 当前缓冲区信息      |
 * | [KEY_PROCEDURAL_WIDTH]       | [Float]        | 是      | Surface的宽        |
 * | [KEY_PROCEDURAL_HEIGHT]      | [Float]        | 是      | Surface的高        |
 * | [KEY_NEXT_MIX_RATIO]         | [Float]        | 是      | 混合的透明的比例    |
 * | [KEY_NEXT_TEXTURE]           | [Texture]      | 是      | 输入纹理           |
 *
 * 输出：
 *
 * 无，输出到屏幕
 */
class FrameRenderer private constructor() : Renderer(TAG) {
    // 顶点着色器中attribute、uniform
    private val aPosition = AttributeShaderParam("aPosition", POSITION_SIZE)
    private val aTexCoord = AttributeShaderParam("aTexCoord", COORD_SIZE)
    private val uViewM = UniformMat4fvShaderParam("uViewM")
    private val uModelM = UniformMat4fvShaderParam("uModelM")
    private val uProjectionM = UniformMat4fvShaderParam("uProjectionM")

    // 片段着色器中attribute、uniform
    private val uTex = Uniform1iShaderParam("uTex")
    private val uMixRatio = Uniform1fShaderParam("uMixRatio")

    override val handleParams: Array<ShaderParam> = arrayOf(
        aPosition,
        aTexCoord,
        uViewM,
        uModelM,
        uProjectionM,
        uTex,
        uMixRatio,
    )

    private val viewM = FloatArray(MATRIX_SIZE).apply {
        Matrix.setIdentityM(this, 0)
    }
    private val modelM = FloatArray(MATRIX_SIZE).apply {
        Matrix.setIdentityM(this, 0)
    }
    private val projectionM = FloatArray(MATRIX_SIZE).apply {
        Matrix.setIdentityM(this, 0)
    }

    override val programShader: ProgramShader = ProgramShader(VS_SHADER_BUILDER, FS_SHADER_BUILDER)

    override fun install(shader: ProgramShader?): GlProgram {
        val glProgram = super.install(shader)!!

        glProgram.use()
        val bufferDataBinder = BufferDataBinder.obtain(vertices)
        bufferDataBinder.install()

        bufferDataBinder.use()
        aPosition.setValue(STRIDE, POSITION_OFFSET)
        aTexCoord.setValue(STRIDE, COORD_OFFSET)
        bufferDataBinder.unUse()

        Matrix.setIdentityM(viewM, 0)
        uViewM.setValue(viewM)
        glProgram.unUse()

        return glProgram
    }

    override fun render(renderArgs: RenderArgs, glProgram: GlProgram?) {
        glProgram!!

        val texture = renderArgs.get<ITexture>(NEXT_NP.getKey(KEY_IMAGE_TEXTURE))
            ?.addToPool(renderArgs).getOrLog(TAG, "[render] texture") ?: return
        val transformMatrix = renderArgs.get<FloatArray>(NEXT_NP.getKey(KEY_EXTRA_MATRIX))
            ?: FloatArray(MATRIX_SIZE).apply { Matrix.setIdentityM(this, 0) }
        texture.load()

        glProgram.use()

        val bufferInfo = renderArgs.require<BufferInfo>(PROCEDURAL_NP.getKey(KEY_BUFFER_INFO))

        val portWidth = renderArgs.require<Int>(PROCEDURAL_NP.getKey(KEY_WIDTH))
        val portHeight = renderArgs.require<Int>(PROCEDURAL_NP.getKey(KEY_HEIGHT))

        val transformM = renderArgs.require<FloatArray>(PROCEDURAL_NP.getKey(KEY_TRANSFORM))
        Matrix.setIdentityM(viewM, 0)
        Matrix.multiplyMM(viewM, 0, transformM, 0, viewM, 0)
        uViewM.setValue(viewM)

        calcModelM(texture, portWidth, portHeight, transformMatrix)
        uModelM.setValue(modelM)

        calcProjectionM(bufferInfo.width.toFloat(), bufferInfo.height.toFloat())
        uProjectionM.setValue(projectionM)

        val mixRatio = renderArgs.get<Float>(NEXT_NP.getKey(KEY_MIX_RATIO)) ?: 1.0f
        uMixRatio.setValue(mixRatio)

        val texUnitIndex0 = 0
        texture.active(texUnitIndex0)
        uTex.setValue(texUnitIndex0)

        val bufferDataBinder = BufferDataBinder.obtain(vertices)
        bufferDataBinder.bind()
        val skey = SKey(vertices.capacity() / STRIDE, bufferInfo.width, bufferInfo.height)
        val glCanvas = GlCanvas.obtain(skey) as ScreenGlCanvas
        glCanvas.draw(bufferInfo.frameBufferId)
        glCanvas.reuse()
        bufferDataBinder.unbind()

        glProgram.unUse()
    }

    private fun calcProjectionM(portWidth: Float, portHeight: Float) {
        Matrix.setIdentityM(projectionM, 0)
        Matrix.orthoM(projectionM, 0, 0f, portWidth, portHeight, 0f, -1f, 1f)
    }

    private fun calcModelM(texture: ITexture, portWidth: Int, portHeight: Int, transformMatrix: FloatArray) {
        Matrix.setIdentityM(modelM, 0)
        val displayRect = calcDisplayRect(texture.width.toFloat(), texture.height.toFloat(), portWidth.toFloat(), portHeight.toFloat())
        Matrix.translateM(modelM, 0, displayRect.left, displayRect.top, 0f)
        Matrix.scaleM(modelM, 0, displayRect.width(), displayRect.height(), 1f)

        Matrix.multiplyMM(modelM, 0, transformMatrix, 0, modelM, 0)
    }

    private fun calcDisplayRect(imageWidth: Float, imageHeight: Float, portWidth: Float, portHeight: Float): RectF {
        val ratio = min(portWidth / imageWidth, portHeight / imageHeight)
        val displayWidth = imageWidth * ratio
        val displayHeight = imageHeight * ratio
        val left = (portWidth - displayWidth) / 2
        val top = (portHeight - displayHeight) / 2
        return RectF(left, top, left + displayWidth, top + displayHeight)
    }

    override fun toString(): String {
        return name
    }

    companion object {
        const val TAG = "FrameRenderer"

        private val VS_SHADER_BUILDER by lazy {
            GlShader().apply {
                addField(FIELD_LAYOUT_IN_VEC3_POSITION)
                addField(FIELD_LAYOUT_IN_VEC3_TEX_COORD)
                addField(FIELD_UNIFORM_MAT4_VIEW_M)
                addField(FIELD_UNIFORM_MAT4_MODEL_M)
                addField(FIELD_UNIFORM_MAT4_PROJECTION_M)
                addField(FIELD_OUT_VEC2_TEX_COORD)

                addExecEnd(EXEC_SET_GL_POSITION)
                addExecEnd(EXEC_SET_TEXCOORD)
            }
        }

        private val FS_SHADER_BUILDER by lazy {
            GlShader().apply {
                addStruct(STRUCT_TF_PARAMS)
                addConst(CONST_TF_PARAMS_SRGB)

                addField(FIELD_IN_VEC2_TEX_COORD)
                addField(FIELD_OUT_VEC4_FRAGCOLOR)
                addField(FIELD_UNIFORM_SAMPLER2D_TEX)
                addField(FIELD_UNIFORM_FLOAT_MIX_RATIO)

                addExecStart(EXEC_TEXTURE_COLOR)
                addExec(EXEC_COLOR_ALPHA_MIX)
                addExecEnd(EXEC_SET_FRAGCOLOR)
            }
        }

        /**
         * 步长
         */
        private const val STRIDE = 5

        /**
         * 矩阵大小
         */
        private const val MATRIX_SIZE = 16

        /**
         * 顶点位置在buffer中的偏移
         */
        private const val POSITION_OFFSET = 0

        /**
         * 坐标在buffer中的偏移
         */
        private const val COORD_OFFSET = 3

        /**
         * 顶点位置的大小
         */
        private const val POSITION_SIZE = 3

        /**
         * 坐标的大小
         */
        private const val COORD_SIZE = 2

        /**
         * 顶点缓冲数组
         */
        private val vertices = floatArrayOf(
            // X, Y, Z, U, V
            0f, 0f, 0.0f, 0.0f, 1.0f,
            1f, 0f, 0.0f, 1.0f, 1.0f,
            0f, 1f, 0.0f, 0.0f, 0.0f,
            1f, 1f, 0.0f, 1.0f, 0.0f
        ).toBuffer()
    }
}