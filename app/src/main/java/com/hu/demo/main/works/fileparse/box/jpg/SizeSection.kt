package com.hu.demo.main.works.fileparse.box.jpg

import android.util.Log
import com.hu.demo.main.works.fileparse.box.Tree
import com.hu.demo.utils.ByteOrderedDataInputStream
import com.hu.demo.utils.toHexString

internal open class SizeSection(parent: Tree?) : Section(parent) {
    var size: Int? = null
    val allSize: Int
        get() = size!! + 2

    override val realSize: Long
        get() = allSize.toLong()

    override fun read(bis: ByteOrderedDataInputStream) {
        super.read(bis)
        size = bis.nReadUShort().toInt()
    }

    override fun skipRemain(bis: ByteOrderedDataInputStream) {
        // size中没有包含marker，所以需要加上
        val remainSize = allSize - alreadyReadSize
        Log.e("JpgFileParser", "skipRemain: ${rootTree.alreadyReadSize.toHexString()}, $remainSize")
        bis.skipFully(remainSize).apply { alreadyReadSize += (remainSize) }
    }

    override fun toString(): String {
        return "${super.toString()} size:${size?.toHexString()}"
    }
}
