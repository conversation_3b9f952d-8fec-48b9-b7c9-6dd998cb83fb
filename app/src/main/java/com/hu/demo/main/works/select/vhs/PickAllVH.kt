package com.hu.demo.main.works.select.vhs

import android.net.Uri
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.hu.demo.main.R
import com.hu.demo.main.adapter.BaseVH
import com.hu.demo.utils.setImageUriAny
import com.hu.demo.main.works.select.datas.PickAllData

class PickAllVH(parent: ViewGroup) : BaseVH<PickAllData>(parent, R.layout.item_pick) {
    private val tvTitle: TextView = findViewById(R.id.tv_item_title)
    private val ivImage: ImageView = findViewById(R.id.iv_item_image)

    override fun bind(extraData: Map<String, Any>, data: PickAllData, position: Int) {
        tvTitle.text = data.title
        ivImage.setImageUriAny(data.obj as? Uri)
        itemView.setOnClickListener {
            data.launcher.launch("*/*")
        }
    }
}