package com.hu.demo.main.works.fileparse

import com.hu.demo.utils.ByteOrderedDataInputStream
import com.hu.demo.utils.beautyToString
import com.hu.demo.utils.contentHexString
import com.hu.demo.utils.toHexString
import com.hu.demo.utils.toInt
import java.nio.ByteOrder

object ExifReader {
    private const val TAG = "ExifReader"
    private val TAG_EXIF_SPEC_IFD = 0x8769.toShort()
    private val TAG_GPS_SPEC_IFD = 0x8825.toShort()
    private val TAG_INTEROPERABILITY_SUB_IFD = 0xA005.toShort()

    fun readExifTag2(byteOrder: ByteOrder, firstIfdOffset: Int, sb: StringBuilder, contentArray: ByteArray) {
        val tiffBis = ByteOrderedDataInputStream(contentArray, 0, contentArray.size, byteOrder)
        tiffBis.skipFully(firstIfdOffset)
        try {
            val ifds = mutableListOf<Ifd>()
            while (true) {
                val ifd = Ifd(byteOrder, contentArray).apply { read(tiffBis) }
                ifds.add(ifd)
                val offset = ifd.nextIfdOffset
                if (offset != null && offset != 0) {
                    val currentPos = tiffBis.position()
                    if (offset > currentPos) {
                        tiffBis.skipFully(offset - currentPos)
                    } else if (offset < currentPos) {
                        // 如果偏移小于当前位置，说明可能有问题，但我们尝试继续
                        android.util.Log.w(TAG, "IFD offset ($offset) is less than current position ($currentPos)")
                        break
                    }
                } else break
            }
            sb.append("\n").append(ifds.beautyToString())
        } catch (e: Exception) {
            sb.append("\nError parsing EXIF IFDs: ${e.message}")
            android.util.Log.e(TAG, "Error parsing EXIF IFDs", e)
        }
    }

    class Ifd(private val byteOrder: ByteOrder, private val backupExifContent: ByteArray) : IRead {
        var number: Short? = null
        var ifdEntries: Array<IfdEntry>? = null
        var nextIfdOffset: Int? = null
        override fun read(bis: ByteOrderedDataInputStream) {
            number = bis.readShort()
            ifdEntries = Array(number!!.toInt()) {
                IfdEntry(it, byteOrder, backupExifContent).apply { read(bis) }
            }
            nextIfdOffset = bis.readInt()
        }

        override fun toString(): String {
            return "Ifd(number:${number?.toHexString()}, ifdEntries:${ifdEntries?.beautyToString()}, nextIfdOffset:${nextIfdOffset?.toHexString()})"
        }
    }

    class IfdEntry(val index: Int, private val byteOrder: ByteOrder, private val backupExifContent: ByteArray) : IRead {
        var tag: Short? = null
        var type: Short? = null
        var count: Int? = null
        var dataOrOffset: ByteArray? = null
        var value: String? = null
        override fun read(bis: ByteOrderedDataInputStream) {
            try {
                tag = bis.readShort()
                type = bis.readShort()
                count = bis.readInt()
                dataOrOffset = bis.readBytes(4)

                if (type!! < 1 || type!! > DataFormat.entries.size) {
                    value = "<<invalid type: $type>>"
                    return
                }

                val format = getFormatByteCount(type!!)
                value = if (format.byteCount * count!! <= 4) {
                    when (tag) {
                        TAG_EXIF_SPEC_IFD,
                        TAG_GPS_SPEC_IFD,
                        TAG_INTEROPERABILITY_SUB_IFD,
                            -> {
                            val offset = dataOrOffset!!.toInt(byteOrder == ByteOrder.LITTLE_ENDIAN)
                            if (offset >= 0 && offset < backupExifContent.size) {
                                ByteOrderedDataInputStream(backupExifContent, offset, byteOrder = byteOrder).use {
                                    Ifd(byteOrder, backupExifContent).apply { read(it) }
                                }
                            } else {
                                "<<invalid offset: $offset>>"
                            }
                        }

                        else -> {
                            format.func(ByteOrderedDataInputStream(dataOrOffset!!, byteOrder = byteOrder), count!!)
                        }
                    }
                } else {
                    val offset = dataOrOffset!!.toInt(byteOrder == ByteOrder.LITTLE_ENDIAN)
                    if (offset >= 0 && offset < backupExifContent.size) {
                        ByteOrderedDataInputStream(backupExifContent, offset, byteOrder = byteOrder).use {
                            if (count!! < 1000) format.func(it, count!!) else "<<too large>>"
                        }
                    } else {
                        "<<invalid offset: $offset>>"
                    }
                }.let {
                    if (it is Array<*>) it.contentToString() else it.toString()
                }
            } catch (e: Exception) {
                value = "<<error: ${e.message}>>"
                android.util.Log.e(TAG, "Error reading IFD entry", e)
            }
        }

        private fun getFormatByteCount(dataFormat: Short): DataFormat {
            return DataFormat.entries[dataFormat - 1]
        }

        override fun toString(): String {
            return "(\ntag:${tag?.toHexString()}, type:${type?.toHexString()}, count:${count?.toHexString()}, " +
                "dataOrOffset:${dataOrOffset?.contentHexString()}, \nvalue:$value\n)"
        }
    }

    enum class DataFormat(val type: Int, val byteCount: Int, val func: ByteOrderedDataInputStream.(count: Int) -> Any) {
        U_BYTE(
            1,
            1,
            {
                if (it > 1) ByteArray(it).apply(::read) else read()
            }
        ),
        ASCII(
            2,
            1,
            {
                ByteArray(it).apply(::read).toString(Charsets.US_ASCII)
            }
        ),
        U_SHORT(
            3,
            2,
            {
                if (it > 1) ShortArray(it) { readShort() } else readShort()
            }
        ),
        U_LONG(
            4,
            4,
            {
                if (it > 1) IntArray(it) { readInt() } else readInt()
            }
        ),
        U_RATIONAL(
            5,
            8,
            {
                if (it > 1) Array(it) { "${readInt()}/${readInt()}" } else "${readInt()}/${readInt()}"
            }
        ),
        SIGNED_BYTE(
            6,
            1,
            {
                if (it > 1) ByteArray(it).apply(::read) else read()
            }
        ),
        UNDEFINED(
            7,
            1,
            {
                // 可能不对
                if (it > 1) ByteArray(it).apply(::read).toString(Charsets.US_ASCII) else readByte()
            }
        ),
        SIGNED_SHORT(
            8,
            2,
            {
                if (it > 1) ShortArray(it) { readShort() } else readShort()
            }
        ),
        SIGNED_LONG(
            9,
            4,
            {
                if (it > 1) IntArray(it) { readInt() } else readInt()
            }
        ),
        SIGNED_RATIONAL(
            10,
            8,
            {
                if (it > 1) Array(it) { "${readInt()}/${readInt()}" } else "${readInt()}/${readInt()}"
            }
        ),
        SINGLE_FLOAT(
            11,
            4,
            {
                if (it > 1) FloatArray(it) { readFloat() } else readFloat()
            }
        ),
        DOUBLE_FLOAT(
            12,
            8,
            {
                if (it > 1) DoubleArray(it) { readDouble() } else readDouble()
            }
        )
    }

}