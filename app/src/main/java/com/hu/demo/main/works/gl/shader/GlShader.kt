package com.hu.demo.main.works.gl.shader

import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.EXEC_MAIN_END
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.EXEC_MAIN_START
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.GLSL_SHADER_HEAD
import java.util.LinkedList

/**
 * shader程序拼接类
 */
class GlShader {
    private var header: ShaderFrag = GLSL_SHADER_HEAD

    /**
     * 结构体存储
     */
    private val structs = LinkedHashMap<Int, ShaderFrag>()

    /**
     * 常量存储
     */
    private val consts = LinkedHashMap<Int, ShaderFrag>()

    /**
     * 成员变量的存储
     */
    private val fields = LinkedHashMap<Int, ShaderFrag>()

    /**
     * 方法的存储
     */
    private val methods = LinkedHashMap<Int, ShaderFrag>()

    /**
     * main方法执行开始内容存储
     */
    private val execStarts = LinkedHashMap<Int, ShaderFrag>()

    /**
     * main方法执行的存储
     */
    private val execs = LinkedList<ShaderFrag>()

    /**
     * main方法执行结束的存储
     */
    private val execEnds = LinkedHashMap<Int, ShaderFrag>()

    /**
     * 设置shader的头
     *
     * @param header shader的头
     */
    fun setShaderHeader(header: ShaderFrag) {
        if (header.grade > this.header.grade) {
            this.header = header
        }
    }

    /**
     * 添加结构体程序，添加时会判断是否重复：
     * 1. 如果[structs]中没有此对象，则直接添加
     * 2. 如果传入的[struct.grade]大于[structs]中存储的对象的[ShaderFrag.grade]，则替换
     *
     * @param struct 结构体程序
     */
    fun addStruct(struct: ShaderFrag) {
        val shaderStr = structs[struct.hashCode()]
        if (shaderStr == null || struct.grade > shaderStr.grade) {
            structs[struct.hashCode()] = struct
        }
    }


    /**
     * 添加常量程序，添加时会判断是否重复：
     * 1. 如果[structs]中没有此对象，则直接添加
     * 2. 如果传入的[const.grade]大于[structs]中存储的对象的[ShaderFrag.grade]，则替换
     *
     * @param const 结构体程序
     */
    fun addConst(const: ShaderFrag) {
        val shaderStr = consts[const.hashCode()]
        if (shaderStr == null || const.grade > shaderStr.grade) {
            consts[const.hashCode()] = const
        }
    }

    /**
     * 添加成员变量程序，添加时会判断是否重复：
     * 1. 如果[structs]中没有此对象，则直接添加
     * 2. 如果传入的[field.grade]大于[structs]中存储的对象的[ShaderFrag.grade]，则替换
     *
     * @param field 成员变量程序
     */
    fun addField(field: ShaderFrag) {
        val shaderStr = fields[field.hashCode()]
        if (shaderStr == null || field.grade > shaderStr.grade) {
            fields[field.hashCode()] = field
        }
    }

    /**
     * 添加方法程序，添加时会判断是否重复：
     * 1. 如果[structs]中没有此对象，则直接添加
     * 2. 如果传入的[method.grade]大于[structs]中存储的对象的[ShaderFrag.grade]，则替换
     *
     * @param method 方法程序
     */
    fun addMethod(method: ShaderFrag) {
        val shaderStr = methods[method.hashCode()]
        if (shaderStr == null || method.grade > shaderStr.grade) {
            methods[method.hashCode()] = method
        }
    }

    /**
     * 添加main方法开始程序，添加时会判断是否重复：
     * 1. 如果[structs]中没有此对象，则直接添加
     * 2. 如果传入的[exec.grade]大于[structs]中存储的对象的[ShaderFrag.grade]，则替换
     *
     * @param exec 方法程序
     */
    fun addExecStart(exec: ShaderFrag) {
        val shaderStr = execStarts[exec.hashCode()]
        if (shaderStr == null || exec.grade > shaderStr.grade) {
            execStarts[exec.hashCode()] = exec
        }
    }

    /**
     * 添加main方法结束程序，添加时不会判断是否重复
     *
     * @param exec 方法程序
     */
    fun addExec(exec: ShaderFrag) {
        execs.add(exec)
    }

    /**
     * 添加main方法结束程序，添加时会判断是否重复：
     * 1. 如果[structs]中没有此对象，则直接添加
     * 2. 如果传入的[exec.grade]大于[structs]中存储的对象的[ShaderFrag.grade]，则替换
     *
     * @param exec 方法程序
     */
    fun addExecEnd(exec: ShaderFrag) {
        val shaderStr = execEnds[exec.hashCode()]
        if (shaderStr == null || exec.grade > shaderStr.grade) {
            execEnds[exec.hashCode()] = exec
        }
    }

    /**
     * 合并传入[other]中的程序
     *
     * @param other 传入的其他程序
     */
    fun combine(other: GlShader) {
        setShaderHeader(other.header)
        other.structs.values.forEach(::addStruct)
        other.consts.values.forEach(::addConst)
        other.fields.values.forEach(::addField)
        other.methods.values.forEach(::addMethod)
        other.execStarts.values.forEach(::addExecStart)
        other.execs.forEach(::addExec)
        other.execEnds.values.forEach(::addExecEnd)
    }

    fun isEmpty(): Boolean {
        return structs.isEmpty() && consts.isEmpty() && fields.isEmpty() && methods.isEmpty() && execStarts.isEmpty() && execs.isEmpty() && execEnds.isEmpty()
    }

    /**
     * 清空shader程序
     */
    fun clean() {
        structs.clear()
        consts.clear()
        fields.clear()
        methods.clear()
        execStarts.clear()
        execs.clear()
        execEnds.clear()
    }

    /**
     * 构建shader程序成String
     */
    fun build(): String {
        val sb = StringBuilder()
        sb.appendLine(header)
        structs.values.forEach(sb::appendLine)
        consts.values.forEach(sb::appendLine)
        fields.values.forEach(sb::appendLine)
        methods.values.forEach(sb::appendLine)

        sb.appendLine(EXEC_MAIN_START)
        execStarts.values.forEach(sb::appendLine)
        execs.forEach(sb::appendLine)
        execEnds.values.forEach(sb::appendLine)
        sb.appendLine(EXEC_MAIN_END)

        return sb.toString()
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as GlShader

        if (structs != other.structs) return false
        if (consts != other.consts) return false
        if (fields != other.fields) return false
        if (methods != other.methods) return false
        if (execStarts != other.execStarts) return false
        if (execs != other.execs) return false
        if (execEnds != other.execEnds) return false

        return true
    }

    override fun hashCode(): Int {
        var result = structs.hashCode()
        result = 31 * result + consts.hashCode()
        result = 31 * result + fields.hashCode()
        result = 31 * result + methods.hashCode()
        result = 31 * result + execStarts.hashCode()
        result = 31 * result + execs.hashCode()
        result = 31 * result + execEnds.hashCode()
        return result
    }
}

