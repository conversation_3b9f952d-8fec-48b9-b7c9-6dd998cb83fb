package com.hu.demo.main.works.drawable

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.LayerDrawable
import android.os.Bundle
import android.util.Log
import android.widget.ImageView
import android.widget.SeekBar
import android.widget.TextView
import androidx.appcompat.content.res.AppCompatResources
import com.hu.demo.main.R
import com.hu.demo.base.ui.BaseActivity

class GridDrawableActivity : BaseActivity() {
    private var ivGridImage: ImageView? = null
    private var ivImage: ImageView? = null
    private var ivImageCenterCrop: ImageView? = null
    private var ivImageCenterInside: ImageView? = null
    private var tvLineWidth: TextView? = null
    private var sbLineWidth: SeekBar? = null
    private var tvLineRadius: TextView? = null
    private var sbLineRadius: SeekBar? = null
    private var tvEdge: TextView? = null
    private var sbEdge: SeekBar? = null
    private var tvGap: TextView? = null
    private var sbGap: SeekBar? = null
    private var tvAngle: TextView? = null
    private var sbAngle: SeekBar? = null

    override fun getLayoutId(): Int = R.layout.activity_grid_drawable

    override fun initToolbar() {
        super.initToolbar()
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowTitleEnabled(true)
            setDisplayShowHomeEnabled(false)
        }
    }

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        ivGridImage = findViewById(R.id.ivGridImage)
        ivImage = findViewById(R.id.ivImage)
        ivImageCenterCrop = findViewById(R.id.ivImageCenterCrop)
        ivImageCenterInside = findViewById(R.id.ivImageCenterInside)

        tvLineWidth = findViewById(R.id.tvLineWidth)
        sbLineWidth = findViewById(R.id.sbLineWidth)

        tvLineRadius = findViewById(R.id.tvLineRadius)
        sbLineRadius = findViewById(R.id.sbLineRadius)

        tvEdge = findViewById(R.id.tvEdge)
        sbEdge = findViewById(R.id.sbEdge)

        tvGap = findViewById(R.id.tvGap)
        sbGap = findViewById(R.id.sbGap)

        tvAngle = findViewById(R.id.tvAngle)
        sbAngle = findViewById(R.id.sbAngle)
    }

    override fun initEvent(savedInstanceState: Bundle?) {
        super.initEvent(savedInstanceState)
        val seekBarCallback = object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                when (seekBar?.id) {
                    R.id.sbLineWidth -> {
                        tvLineWidth?.text = progress.toFloat().toString()
                        applyDrawable(
                            progress.toFloat(),
                            sbLineRadius!!.progress.toFloat(),
                            sbEdge!!.progress.toFloat(),
                            sbGap!!.progress.toFloat(),
                            sbAngle!!.progress.toFloat()
                        )
                    }

                    R.id.sbLineRadius -> {
                        tvLineRadius?.text = progress.toFloat().toString()
                        applyDrawable(
                            sbLineWidth!!.progress.toFloat(),
                            progress.toFloat(),
                            sbEdge!!.progress.toFloat(),
                            sbGap!!.progress.toFloat(),
                            sbAngle!!.progress.toFloat()
                        )
                    }

                    R.id.sbEdge -> {
                        tvEdge?.text = progress.toFloat().toString()
                        applyDrawable(
                            sbLineWidth!!.progress.toFloat(),
                            sbLineRadius!!.progress.toFloat(),
                            progress.toFloat(),
                            sbGap!!.progress.toFloat(),
                            sbAngle!!.progress.toFloat()
                        )
                    }

                    R.id.sbGap -> {
                        tvGap?.text = progress.toFloat().toString()
                        applyDrawable(
                            sbLineWidth!!.progress.toFloat(),
                            sbLineRadius!!.progress.toFloat(),
                            sbEdge!!.progress.toFloat(),
                            progress.toFloat(),
                            sbAngle!!.progress.toFloat()
                        )
                    }

                    R.id.sbAngle -> {
                        tvAngle?.text = progress.toFloat().toString()
                        applyDrawable(
                            sbLineWidth!!.progress.toFloat(),
                            sbLineRadius!!.progress.toFloat(),
                            sbEdge!!.progress.toFloat(),
                            sbGap!!.progress.toFloat(),
                            progress.toFloat(),
                        )
                    }
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) = Unit

            override fun onStopTrackingTouch(seekBar: SeekBar?) = Unit
        }
        sbLineWidth?.setOnSeekBarChangeListener(seekBarCallback)
        sbLineRadius?.setOnSeekBarChangeListener(seekBarCallback)
        sbEdge?.setOnSeekBarChangeListener(seekBarCallback)
        sbGap?.setOnSeekBarChangeListener(seekBarCallback)
        sbAngle?.setOnSeekBarChangeListener(seekBarCallback)
        tvLineWidth?.text = sbLineWidth!!.progress.toFloat().toString()
        tvLineRadius?.text = sbLineRadius!!.progress.toFloat().toString()
        tvEdge?.text = sbEdge!!.progress.toFloat().toString()
        tvGap?.text = sbGap!!.progress.toFloat().toString()
        tvAngle?.text = sbAngle!!.progress.toFloat().toString()
        applyDrawable(
            sbLineWidth!!.progress.toFloat(),
            sbLineRadius!!.progress.toFloat(),
            sbEdge!!.progress.toFloat(),
            sbGap!!.progress.toFloat(),
            sbAngle!!.progress.toFloat()
        )
    }

    fun applyDrawable(lineWidth: Float, radius: Float, edge: Float, gap: Float, angle: Float) {
        Log.d(TAG, "applyDrawable: lineWidth: $lineWidth, radius: $radius")
        ivGridImage?.apply {
            setImageDrawable(
                GridDrawable(
                    gridItems = arrayOf(
                        arrayOf(
                            RoundDrawable(
                                drawable = AppCompatResources.getDrawable(this@GridDrawableActivity, R.drawable.ic_launcher_foreground),
                                radius = radius,
                                rotate = angle,
                                lineWidth = lineWidth,
                                lineColor = Color.BLACK
                            ),
                            RoundDrawable(
                                drawable = AppCompatResources.getDrawable(this@GridDrawableActivity, R.drawable.ic_launcher_foreground),
                                rotate = angle,
                                radius = radius,
                                lineWidth = lineWidth,
                                lineColor = Color.BLACK
                            )
                        ),
                        arrayOf(
                            RoundDrawable(
                                drawable = AppCompatResources.getDrawable(this@GridDrawableActivity, R.drawable.ic_launcher_background),
                                radius = radius,
                                rotate = angle,
                                lineWidth = lineWidth,
                                lineColor = Color.BLACK
                            ),
                            LayerDrawable(
                                arrayOf(
                                    CircleDrawable(
                                        drawable = AppCompatResources.getDrawable(this@GridDrawableActivity, R.drawable.ic_launcher_background),
                                        rotate = angle,
                                        lineWidth = lineWidth,
                                        lineColor = Color.BLACK
                                    ),
                                    TextDrawable("+12")
                                )
                            )

                        )
                    ),
                    background = ColorDrawable(Color.GRAY),
                    edge = edge,
                    gap = gap,
                    radius = radius,
                    lineWidth = lineWidth,
                    lineColor = Color.BLACK
                )
            )
        }
        ivImage?.setImageDrawable(
            RoundDrawable(
                drawable = AppCompatResources.getDrawable(this@GridDrawableActivity, R.drawable.ic_launcher_background),
                radius = radius,
                rotate = angle,
                lineWidth = lineWidth,
                lineColor = Color.BLACK,
                drawType = DrawType.CENTER_INSIDE
            )
        )
        ivImageCenterCrop?.setImageDrawable(
            RoundDrawable(
                drawable = AppCompatResources.getDrawable(this@GridDrawableActivity, R.drawable.acve5787),
                radius = radius,
                rotate = angle,
                lineWidth = lineWidth,
                lineColor = Color.BLACK,
                drawType = DrawType.CENTER_CROP
            )
        )
        ivImageCenterInside?.setImageDrawable(
            RoundDrawable(
                drawable = AppCompatResources.getDrawable(this@GridDrawableActivity, R.drawable.acve5787),
                radius = radius,
                rotate = angle,
                lineWidth = lineWidth,
                lineColor = Color.BLACK,
                drawType = DrawType.CENTER_INSIDE
            )
        )
    }

    companion object {
        private const val TAG = "GridDrawableActivity"
    }
}