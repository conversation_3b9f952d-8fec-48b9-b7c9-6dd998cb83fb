/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - RenderPath.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/08/27
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * huca<PERSON><PERSON>@Apps.Gallery		2024/08/27		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.renderer.param

import android.util.ArrayMap
import com.hu.demo.main.works.gl.renderer.normal.Renderer
import com.hu.demo.main.works.gl.renderer.RendererAdapter
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.toNodePath

/**
 * 渲染器路径。用户可通过构造[NodePath]来查找在[RendererAdapter]中对应的[Renderer]。
 * 为了避免每次生成对象，对构造方法私有，用户需要通过[Array.toNodePath]来生成[NodePath]。
 *
 * 路径以`/`来分割，表示层级关系。
 *
 * 例：`ImageRenderer/FrameRenderer`
 */
class NodePath private constructor(
    /**
     * 层级节点数组，数组需要按真实顺序表示
     */
    val nodes: Array<String>,
) {
    /**
     * 当前渲染器路径的[String]类型的名称
     */
    val pathName by lazy { buildPath(nodes) }

    init {
        if (nodes.isEmpty()) throw IllegalArgumentException("nodes must not empty!")
    }

    /**
     * 获取层级节点中的最顶端节点
     *
     * @param 返回最顶端节点
     */
    fun top(): String {
        return nodes.first()
    }

    /**
     * 去除最顶端节点，并返回去除后剩余节点组成的[NodePath]。
     * 如果节点个数为1，说明当前节点已是最终查询的节点，没有更下一级节点，在此场景下会返回null。
     *
     * @return 返回去除顶端节点后剩余节点组成的[NodePath]
     */
    fun minusTop(): NodePath? {
        if (nodes.size == 1) {
            return null
        }
        return nodes.copyOfRange(1, nodes.size).toNodePath()
    }

    /**
     * 去除最底端节点，并返回去除后剩余节点组成的[NodePath]
     * 如果节点个数为1，说明当前节点已是最终查询的节点，没有更上一级节点，在此场景下会返回null。
     *
     * @return 返回去除底端节点后剩余节点组成的[NodePath]
     */
    fun minusEnd(): NodePath? {
        if (nodes.size == 1) {
            return null
        }
        return nodes.copyOfRange(0, nodes.size - 1).toNodePath()
    }

    /**
     * 根据输入的节点[node]，生成在当前节点数组下的下级节点数组对应的[NodePath]
     *
     * @param node 下一级[NodePath]
     *
     * @return 返回生成的渲染器路径[NodePath]
     */
    operator fun plus(node: String): NodePath {
        return (nodes + node).toNodePath()
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as NodePath

        return nodes.contentEquals(other.nodes)
    }

    override fun hashCode(): Int {
        return pathName.hashCode()
    }

    override fun toString(): String {
        return pathName
    }

    companion object {
        private const val SYMBOL_SLASH = "/"

        /**
         * 渲染器路径的缓存池
         */
        private val nodePathCache = ArrayMap<String, NodePath>()

        /**
         * 持久的参数集，此类参数会在渲染器中一直保存，在每次渲染循环中都会使用
         */
        val STABLE_NP = arrayOf("stable").toNodePath()

        /**
         * 临时的参数集，此类参数是当前渲染循环中的参数，仅在当此渲染循环中使用
         */
        val PROCEDURAL_NP = arrayOf("procedural").toNodePath()

        /**
         * 上一步的参数集，此类参数是上一步[Renderer]的产物，当前[Renderer]会（可能）使用此参数进行处理
         */
        val NEXT_NP = arrayOf("next").toNodePath()

        /**
         * 通过节点数组，生成渲染器路径[NodePath]
         */
        fun Array<String>.toNodePath(): NodePath {
            return nodePathCache.getOrPut(buildPath(this)) { NodePath(this) }
        }

        private fun buildPath(nodes: Array<String>): String {
            return nodes.joinToString(SYMBOL_SLASH)
        }
    }
}
