package com.hu.demo.main.works.recyclerview.longclick

import android.graphics.Canvas
import android.os.SystemClock
import android.view.GestureDetector
import android.view.HapticFeedbackConstants
import android.view.MotionEvent
import android.view.ViewGroup
import androidx.core.view.GestureDetectorCompat
import com.hu.demo.main.works.recyclerview.view.IAttachView
import com.hu.demo.main.works.recyclerview.view.IItemTouchProcessor
import com.hu.demo.main.works.recyclerview.view.ViewTouchAdapter

class ItemLongClickAddition<V : ViewGroup> : IItemTouchProcessor<V>, IAttachView<V> {

    val longClicks = mutableSetOf<LongClickCallback>()

    private var touchAdapter: ViewTouchAdapter<V>? = null

    private var gestureDetector: GestureDetectorCompat? = null

    private val listener = object : GestureDetector.SimpleOnGestureListener() {
        override fun onLongPress(e: MotionEvent) {
            val touchAdapter = touchAdapter ?: return
            val pos = touchAdapter.findChildView(e.x, e.y, false)?.let { touchAdapter.findChildPosition(it) } ?: INVALID_POS
            if (pos == INVALID_POS) return
            touchAdapter.view.performHapticFeedback(HapticFeedbackConstants.LONG_PRESS)
            for (longClick in longClicks) {
                if (longClick.onLongClick(e, pos)) return
            }
        }
    }

    override fun onInterceptTouchEvent(e: MotionEvent): Boolean {
        return gestureDetector?.onTouchEvent(e) ?: false
    }

    override fun onTouchEvent(e: MotionEvent) {
        gestureDetector?.onTouchEvent(e)
    }

    override fun onDraw(c: Canvas, view: V) = Unit

    override fun onRequestDisallowInterceptTouchEvent(disallowIntercept: Boolean) {
        if (disallowIntercept) {
            val now = SystemClock.uptimeMillis()
            gestureDetector?.onTouchEvent(MotionEvent.obtain(now, now, MotionEvent.ACTION_CANCEL, 0f, 0f, 0))
        }
    }

    override fun attachToView(viewTouchAdapter: ViewTouchAdapter<V>?) {
        this.touchAdapter?.removeCallback(this)
        this.touchAdapter = viewTouchAdapter?.apply {
            viewTouchAdapter.addCallback(this@ItemLongClickAddition)
            gestureDetector = GestureDetectorCompat(view.context, listener)
        }
    }

    companion object {
        private const val INVALID_POS = -1
    }
}

fun interface LongClickCallback {
    fun onLongClick(e: MotionEvent, pos: Int): Boolean
}