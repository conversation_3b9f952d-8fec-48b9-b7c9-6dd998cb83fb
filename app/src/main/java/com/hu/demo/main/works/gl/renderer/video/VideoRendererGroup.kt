/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - VideoRenderer.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/08/29
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * huca<PERSON><PERSON>@Apps.Gallery		2024/08/29		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.renderer.video

import com.hu.demo.main.works.gl.renderer.normal.FrameRenderer
import com.hu.demo.main.works.gl.renderer.normal.GamutRenderer
import com.hu.demo.main.works.gl.renderer.normal.RendererGroup
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_COLOR_SPACE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_IMAGE_TEXTURE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_MIX_RATIO
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_VIDEO_TEXTURE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.VALUE_NEED_RENDER_THRESHOLD
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.getKey
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.NEXT_NP
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.PROCEDURAL_NP
import com.hu.demo.main.works.gl.renderer.param.RenderArgs
import com.hu.demo.main.works.gl.shader.ProgramShader
import com.hu.demo.main.works.gl.texture.ITexture
import com.hu.demo.main.works.gl.texture.Texture
import com.hu.demo.main.works.gl.utils.GlProgram

/**
 * 图片的渲染器，其作为父级，整合参数
 *
 * 输入：
 *
 * | key                                  | 类型        | 是否必须    | 解释        |
 * | -                                    | -           | -          | -           |
 * | [KEY_STABLE_VIDEO_TEXTURE]           | [Texture]   | 否         | 视频纹理 |
 * | [KEY_STABLE_MIX_RATIO]               | [Float]     | 否         | 混合比例     |
 *
 * 输出：
 *
 * | key                  | 类型        | 是否必须    | 解释      |
 * | -                    | -           | -          | -         |
 * | [KEY_NEXT_TEXTURE]   | [Texture]   | 否         | 输出的纹理 |
 * | [KEY_NEXT_MIX_RATIO] | [Float]     | 否         | 混合比例   |
 */
class VideoRendererGroup private constructor() : RendererGroup(TAG) {
    init {
        addChild(new(OesToRgbRenderer::class.java))
        addChild(new(GamutRenderer::class.java))
        addChild(new(FrameRenderer::class.java))
    }

    override fun install(shader: ProgramShader?): GlProgram? {
        return super.install(programShader.apply(ProgramShader::clean))
    }

    override fun render(renderArgs: RenderArgs, glProgram: GlProgram?) {
        renderArgs.remove(NEXT_NP)

        val mixRatio = 1.0f - renderArgs.require<Float>(PROCEDURAL_NP.getKey(KEY_MIX_RATIO))
        renderArgs[NEXT_NP.getKey(KEY_MIX_RATIO)] = mixRatio

        val videoTexture = renderArgs.get<ITexture>(PROCEDURAL_NP.getKey(KEY_VIDEO_TEXTURE))
        if (mixRatio > VALUE_NEED_RENDER_THRESHOLD && videoTexture != null) {

            renderArgs[PROCEDURAL_NP.getKey(KEY_COLOR_SPACE)] = videoTexture.colorSpace

            renderArgs[NEXT_NP.getKey(KEY_IMAGE_TEXTURE)] = videoTexture
            renderArgs[NEXT_NP.getKey(KEY_COLOR_SPACE)] = videoTexture.colorSpace

            super.render(renderArgs, glProgram)
        }
    }

    override fun toString(): String {
        return name
    }

    companion object {
        const val TAG = "VideoRenderer"
    }
}