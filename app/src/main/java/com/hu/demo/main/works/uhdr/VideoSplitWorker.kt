package com.hu.demo.main.works.uhdr

import android.annotation.SuppressLint
import android.content.Context
import android.media.MediaCodec
import android.media.MediaExtractor
import android.media.MediaFormat
import android.media.MediaMuxer
import android.media.MediaMuxer.OutputFormat.MUXER_OUTPUT_MPEG_4
import android.util.Log
import androidx.work.Data
import androidx.work.WorkerParameters
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.runBlocking
import java.io.Closeable
import java.io.File
import java.nio.ByteBuffer
import kotlin.time.Duration.Companion.microseconds

class VideoSplitWorker(context: Context, workerParams: WorkerParameters) : BaseWorker(context, workerParams) {
    @SuppressLint("WrongConstant")
    override fun doWork(): Result {
        val inputFile = inputData.getString(INPUT_FILE)?.let { File(it) } ?: run {
            Log.e(TAG, "doWork: 输入文件路径为空")
            return Result.failure()
        }

        if (!inputFile.exists() || !inputFile.isFile) {
            Log.e(TAG, "doWork: 输入文件不存在或不是文件: ${inputFile.absolutePath}")
            return Result.failure()
        }

        val outputDir = inputData.getString(OUTPUT_DIR)?.let { File(it) } ?: inputFile.parentFile ?: run {
            Log.e(TAG, "doWork: 输出目录路径为空")
            return Result.failure()
        }

        val maxSliceCount = inputData.getInt(MAX_SLICE_COUNT, 3)

        if (!outputDir.exists()) {
            outputDir.mkdirs()
        }

        return try {
            val successCount = runBlocking { doSplit(inputFile, outputDir, maxSliceCount) }

            if (successCount > 0) {
                Log.i(TAG, "doWork: 视频分割完成，成功创建 $successCount 个片段")
                Result.success(Data.Builder().putAll(mapOf(OUTPUT_COUNT to successCount)).build())
            } else {
                Log.e(TAG, "doWork: 所有片段创建失败")
                Result.failure()
            }
        } catch (e: Exception) {
            Log.e(TAG, "doWork: 视频分割过程中发生未知异常", e)
            Result.failure()
        }
    }

    private suspend fun doSplit(inputFile: File, outputDir: File, maxSliceCount: Int): Int {
        var successCount = -1
        // 初始化提取器
        val extractor = MediaExtractor()
        try {
            extractor.setDataSource(inputFile.absolutePath)

            // 获取视频轨道信息
            val videoTrackIndex = extractor.getVideoTrack()
            if (videoTrackIndex < 0) {
                Log.e(TAG, "doWork: 未找到视频轨道")
                extractor.release()
                return 0
            }

            // 使用流式处理模式进行视频分割
            val list = split(extractor, inputFile, outputDir, maxSliceCount).toList()
            successCount = list.size
        } finally {
            extractor.release()
        }
        return successCount
    }


    private fun MediaExtractor.getDuration(): Long {
        return getTrackFormat(0).getLong(MediaFormat.KEY_DURATION)
    }

    private fun MediaExtractor.seek(timeUs: Long): Boolean {
        Log.d(TAG, "seek: 尝试定位到时间:${timeUs}us")

        val mode = arrayOf(
            MediaExtractor.SEEK_TO_CLOSEST_SYNC,
            MediaExtractor.SEEK_TO_NEXT_SYNC,
            MediaExtractor.SEEK_TO_PREVIOUS_SYNC
        ).firstOrNull {
            seekTo(timeUs, it)
            sampleTime >= 0
        }

        return mode != null
    }

    /**
     * 执行流式视频分割，支持Open GOP处理
     * 边扫描边分割，减少内存占用，确保分片独立播放
     * @param extractor 媒体提取器
     * @param inputFile 输入文件
     * @param outputDir 输出目录
     * @param maxSliceCount 最大分片数量
     * @return 成功分割的片段数量
     */
    @SuppressLint("WrongConstant")
    private fun split(extractor: MediaExtractor, inputFile: File, outputDir: File, maxSliceCount: Int = 3) = flow {
        val videoTrackIndex = extractor.getVideoTrack()
        if (videoTrackIndex < 0) {
            Log.e(TAG, "split: 未找到视频轨道")
            return@flow
        }

        val duration = extractor.getDuration()
        val targetSegmentDuration = duration / (maxSliceCount + 1)
        Log.d(TAG, "split: 视频总时长=${duration}us, 目标分片时长=${targetSegmentDuration}us")

        var segmentIndex = 0
        val buffer = ByteBuffer.allocateDirect(BUFFER_SIZE) // 4MB缓冲区，提高性能
        val bufferInfo = MediaCodec.BufferInfo()

        var mediaMuxer: MuxerObj? = null

        try {
            var segmentStartTimeUs = 0L
            var frameCount = 0

            extractor.selectTrack(videoTrackIndex)

            // 确保正确定位到文件开始
            if (!extractor.seek(0)) {
                Log.e(TAG, "split: 无法定位到文件开始")
                return@flow
            }

            Log.d(TAG, "split: 开始扫描视频，初始位置=${extractor.sampleTime}us")

            val segKeyFrames = mutableListOf<Long>()
            // 流式扫描和分割
            var sampleTime: Long = 0L
            var canSplit = false
            while (true) {
                if (extractor.sampleTime < 0) {
                    Log.d(TAG, "split: 到达文件末尾，完成最后一个分片")
                    break
                }
                sampleTime = extractor.sampleTime

                val isKeyFrame = (extractor.sampleFlags and MediaExtractor.SAMPLE_FLAG_SYNC) != 0

                Log.w(TAG, "split: isKey:$isKeyFrame, pts:$sampleTime, time:${sampleTime.microseconds}")
                // 如果还没有创建muxer，创建新的分片
                if (mediaMuxer == null) {
                    mediaMuxer = createMuxer(extractor, outputDir, inputFile.nameWithoutExtension, segmentIndex)
                    mediaMuxer.start()
                    segmentStartTimeUs = sampleTime
                    frameCount = 0
                    canSplit = false
                    segKeyFrames.clear()
                    Log.d(TAG, "split: 开始分片:$segmentIndex, 起始时间:${segmentStartTimeUs}us")
                }

                // 更新关键帧信息
                if (isKeyFrame) {
                    segKeyFrames.add(sampleTime)
                }

                if (!canSplit &&
                    sampleTime - segmentStartTimeUs >= targetSegmentDuration &&
                    frameCount > 30 && // 确保分片有足够的帧
                    segmentIndex + 1 < maxSliceCount && // 不超过最大分片数
                    isKeyFrame
                ) {
                    canSplit = true
                }

                if (canSplit && segKeyFrames.size > 1 && sampleTime > segKeyFrames.last()) {
                    Log.d(TAG, "split: 结束分片:$segmentIndex, 结束时间:${sampleTime}us, 帧数:${frameCount}, 关键帧数:${segKeyFrames.size}")
                    mediaMuxer.close()
                    emit(mediaMuxer.outputFile)
                    // 准备下一个分片
                    segmentIndex++
                    extractor.seek(segKeyFrames.last())
                    segKeyFrames.clear()
                    mediaMuxer = null
                    continue // 重新处理当前帧作为下一个分片的开始
                }

                // 读取并写入帧数据
                buffer.clear()
                val sampleSize = extractor.readSampleData(buffer, 0)
                if (sampleSize > 0) {
                    // 计算相对于分片开始的时间戳，确保从0开始
                    val relativeTimeUs = sampleTime - segmentStartTimeUs
                    if (relativeTimeUs >= 0) { // 只写入时间戳为正的帧
                        bufferInfo.set(0, sampleSize, relativeTimeUs, extractor.sampleFlags)
                        mediaMuxer.writeSampleData(buffer, bufferInfo)
                        frameCount++
                    }
                }
                if (!extractor.advance()) {
                    break
                }
            }

            // 关闭最后一个分片
            mediaMuxer?.apply {
                Log.d(TAG, "split: 结束分片$segmentIndex, 结束时间:${sampleTime}us, 帧数:${frameCount}, 关键帧数:${segKeyFrames.size}")
                close()
                emit(outputFile)
            }
        } catch (e: Exception) {
            Log.e(TAG, "split: 分割过程中发生异常", e)
            mediaMuxer?.close()
        } finally {
            extractor.unselectTrack(videoTrackIndex)
        }

        Log.i(TAG, "split: 视频分割完成，共创建${segmentIndex + 1}个分片")
    }

    private fun MediaExtractor.getRotation(): Int {
        return getTrackFormat(getVideoTrack()).getInteger(MediaFormat.KEY_ROTATION, 0)
    }

    private fun createMuxer(extractor: MediaExtractor, outputDir: File, fileName: String, segmentIndex: Int): MuxerObj {
        val outputFile = File(outputDir, "${fileName}_segment_${segmentIndex}.mp4")
        outputFile.delete()
        outputFile.createNewFile()
        val mediaMuxer = MediaMuxer(outputFile.absolutePath, MUXER_OUTPUT_MPEG_4)
        val track = mediaMuxer.addTrack(extractor.getTrackFormat(extractor.getVideoTrack()))
        mediaMuxer.setOrientationHint(extractor.getRotation())
        return MuxerObj(mediaMuxer, track, outputFile)
    }

    class MuxerObj(private val mediaMuxer: MediaMuxer, private val track: Int, val outputFile: File) : Closeable {
        private var isStoped = false

        fun start() {
            mediaMuxer.start()
        }

        fun writeSampleData(buffer: ByteBuffer, bufferInfo: MediaCodec.BufferInfo) {
            mediaMuxer.writeSampleData(track, buffer, bufferInfo)
        }

        override fun close() {
            if (isStoped) return
            try {
                mediaMuxer.stop()
            } catch (e: Exception) {
                Log.e(TAG, "close: 停止MediaMuxer时发生未知异常", e)
            } finally {
                try {
                    mediaMuxer.release()
                } catch (e: Exception) {
                    Log.w(TAG, "close: 释放MediaMuxer资源时发生异常", e)
                } finally {
                    isStoped = true
                }
            }
        }
    }

    private fun MediaExtractor.getVideoTrack(): Int {
        for (index in 0 until trackCount) {
            val format = getTrackFormat(index)
            val mime = format.getString(MediaFormat.KEY_MIME)!!
            if (mime.startsWith(MIME_VIDEO_START)) {
                return index
            }
        }
        return -1
    }

    companion object {
        private const val TAG = "VideoSplitWorker"
        private const val MIME_VIDEO_START = "video/"
        private const val BUFFER_SIZE = 4 * 1024 * 1024
    }
}