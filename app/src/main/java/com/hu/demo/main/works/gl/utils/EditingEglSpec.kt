/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - EditingEglSpec.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/09/05
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * huca<PERSON><PERSON>@Apps.Gallery		2024/09/05		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.utils

import android.opengl.EGL14
import android.opengl.EGLConfig
import android.opengl.EGLContext
import androidx.graphics.opengl.egl.EGLSpec
import androidx.graphics.opengl.egl.EGLSpec.Companion.V14

class EditingEglSpec(private val sharedContextGetter: ISharedContextGetter = DefaultContextGetter) : EGLSpec by V14 {

    private val contextAttributes = intArrayOf(
        EGL14.EGL_CONTEXT_CLIENT_VERSION, EGL_VERSION,
        EGL14.EGL_NONE
    )

    override fun eglCreateContext(config: EGLConfig): EGLContext {
        return EGL14.eglCreateContext(
            EGL14.eglGetDisplay(EGL14.EGL_DEFAULT_DISPLAY),
            config,
            sharedContextGetter.getSharedEglContext(),
            contextAttributes,
            0
        )
    }

    fun interface ISharedContextGetter {
        fun getSharedEglContext(): EGLContext
    }

    object DefaultContextGetter : ISharedContextGetter {
        override fun getSharedEglContext(): EGLContext {
            return EGL14.EGL_NO_CONTEXT
        }
    }

    companion object {
        private const val EGL_VERSION = 3
    }
}