package com.hu.demo.main.works.recyclerview

import android.os.Bundle
import android.util.Log
import android.view.ViewGroup
import android.widget.Button
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.hu.demo.main.R
import com.hu.demo.main.adapter.BaseData
import com.hu.demo.main.adapter.BaseVH
import com.hu.demo.main.adapter.DefaultAdapter
import com.hu.demo.main.adapter.DiffCallback
import com.hu.demo.main.adapter.DraggableAdapter
import com.hu.demo.base.ui.BaseActivity
import com.hu.demo.main.works.main.TYPE_DEFAULT
import com.hu.demo.main.works.main.TYPE_TITLE
import com.hu.demo.main.works.recyclerview.datas.RecyclerData
import com.hu.demo.main.works.recyclerview.datas.RecyclerTitleData
import com.hu.demo.main.works.recyclerview.drag.IDragCallback
import com.hu.demo.main.works.recyclerview.drag.ItemDragHelper
import com.hu.demo.main.works.recyclerview.longclick.ItemLongClickAddition
import com.hu.demo.main.works.recyclerview.longclick.ItemLongClickProcessor
import com.hu.demo.main.works.recyclerview.slidingselect.ISelectable
import com.hu.demo.main.works.recyclerview.slidingselect.ISlidingCallback
import com.hu.demo.main.works.recyclerview.slidingselect.SlidingSelectProcessor
import com.hu.demo.main.works.recyclerview.vhs.RecyclerTitleVH
import com.hu.demo.main.works.recyclerview.vhs.RecyclerVH
import com.hu.demo.main.works.recyclerview.view.IEditable
import com.hu.demo.main.works.recyclerview.view.RecycleItemTouchAdapter
import java.util.UUID

class RecyclerActivity : BaseActivity() {

    private var rvRecycler: RecyclerView? = null
    private var btnDeleteTitleLast: Button? = null
    private var btnAddTitleLast: Button? = null
    private var btnReset: Button? = null
    private val selectMap = mutableMapOf<BaseData, Boolean>()

    private var titleIndex = -1

    override fun getLayoutId(): Int {
        return R.layout.activity_recycler
    }

    override fun initToolbar() {
        super.initToolbar()
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowTitleEnabled(true)
            setDisplayShowHomeEnabled(false)
        }
    }

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        rvRecycler = findViewById(R.id.rvRecycler)
        btnReset = findViewById(R.id.btnReset)
        btnDeleteTitleLast = findViewById(R.id.btnDeleteTitleLast)
        btnAddTitleLast = findViewById(R.id.btnAddTitleLast)

        rvRecycler?.apply {
            setItemViewCacheSize(3)
            // 当Item的高度如是固定的，设置这个属性为true可以提高性能
            setHasFixedSize(true)
            recycledViewPool.setMaxRecycledViews(TYPE_DEFAULT, 100)
            layoutManager = GridLayoutManager(this@RecyclerActivity, 3, RecyclerView.VERTICAL, false).also {
                it.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                    override fun getSpanSize(position: Int): Int {
                        return (adapter as? DefaultAdapter<*, *>)?.dataList?.getOrNull(position)?.span ?: 1
                    }
                }
            }
            adapter = InnerAdapter(this, selectMap).also {
                it.isEditMode = true
                it.setHasStableIds(true)
                it.resetData(generateDatas())
            }

            val recycleItemTouchAdapter = RecycleItemTouchAdapter(this)
            val itemLongClickHelper = ItemLongClickProcessor<RecyclerView> {
                Log.d(TAG, "initView: $it")
                true
            }
            val slidingSelectHelper = SlidingSelectProcessor<RecyclerView>(object : ISlidingCallback {
                var firstSelect = false
                override fun canSliding(): Boolean {
                    return (adapter as InnerAdapter).isEditMode
                }

                override fun onSlidingStart(position: Int) {
                    firstSelect = selectMap[(adapter as InnerAdapter).dataList[position]]?.not() ?: true
                    selectMap[(adapter as InnerAdapter).dataList[position]] = firstSelect
                    (findViewHolderForAdapterPosition(position) as? ISelectable)?.isSelect = firstSelect
                }

                override fun onSlidingChange(range: IntProgression, correct: Boolean, autoScrolling: Boolean) {
                    range.forEach { index ->
                        selectMap[(adapter as InnerAdapter).dataList[index]] = if (correct) firstSelect else !firstSelect
                        (findViewHolderForAdapterPosition(index) as? ISelectable)?.isSelect = if (correct) firstSelect else !firstSelect
                    }
                }

                override fun onSlidingEnd() = Unit

            })
            val itemDragHelper = ItemDragHelper<RecyclerView>((adapter as? IDragCallback))
            val itemLongClickAddition = ItemLongClickAddition<RecyclerView>().also {
                it.longClicks.add(itemDragHelper)
                it.longClicks.add(slidingSelectHelper)
                it.longClicks.add(itemLongClickHelper)
            }
            itemLongClickAddition.attachToView(recycleItemTouchAdapter)
            slidingSelectHelper.attachToView(recycleItemTouchAdapter)
            itemDragHelper.attachToView(recycleItemTouchAdapter)
            itemLongClickHelper.attachToView(recycleItemTouchAdapter)
            recycleItemTouchAdapter.attach()
        }
    }

    private fun generateDatas(): MutableList<BaseData> {
        val localDatas = mutableListOf<BaseData>()
        for (index in 0..100) {
            if (index == 5) {
                titleIndex = index
                localDatas.add(RecyclerTitleData("标题$index", index))
            } else {
                localDatas.add(RecyclerData(R.mipmap.ic_launcher, "名称$index", index))
            }
        }
        return localDatas
    }

    @Suppress("UNCHECKED_CAST")
    override fun initEvent(savedInstanceState: Bundle?) {
        super.initEvent(savedInstanceState)
        btnDeleteTitleLast?.setOnClickListener {
            (rvRecycler?.adapter as? DefaultAdapter<BaseData, BaseVH<BaseData>>)?.apply {
                val datas = dataList.toList()
                datas.indexOfFirst {
                    it is RecyclerTitleData
                }.takeIf {
                    it > 0
                }?.also { index ->
                    titleIndex -= 1
                    DiffUtil.calculateDiff(
                        InnerDiffCallback(
                            datas,
                            datas.toMutableList().also {
                                it.removeAt(index - 1)
                                resetData(it)
                            }
                        )
                    ).dispatchUpdatesTo(this)
                }
            }
        }
        btnAddTitleLast?.setOnClickListener {
            (rvRecycler?.adapter as? DefaultAdapter<BaseData, BaseVH<BaseData>>)?.apply {
                val datas = dataList.toList()
                datas.toMutableList().add(RecyclerData(R.mipmap.ic_launcher, "标题${UUID.randomUUID()}", titleIndex))
                DiffUtil.calculateDiff(
                    InnerDiffCallback(
                        datas,
                        datas.toMutableList().also {
                            it.add(titleIndex, RecyclerData(R.mipmap.ic_launcher, "标题${UUID.randomUUID()}", titleIndex))
                            resetData(it)
                        })
                ).dispatchUpdatesTo(this)
                titleIndex += 1
            }
        }
        btnReset?.setOnClickListener {
            (rvRecycler?.adapter as? DefaultAdapter<BaseData, BaseVH<BaseData>>)?.apply {
                val datas = dataList.toList()
                DiffUtil.calculateDiff(
                    InnerDiffCallback(
                    datas,
                    generateDatas().also {
                        resetData(it)
                    }
                )).dispatchUpdatesTo(this)
            }
        }
    }

    class InnerDiffCallback(oldList: List<BaseData>? = null, newList: List<BaseData>? = null) : DiffCallback<BaseData>(oldList, newList) {
        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return oldList?.getOrNull(oldItemPosition) == newList?.getOrNull(newItemPosition)
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return true
        }
    }

    class InnerAdapter(recyclerView: RecyclerView, private val selectMap: Map<BaseData, Boolean>) :
        DraggableAdapter<BaseData, BaseVH<BaseData>>(recyclerView) {
        @Suppress("UNCHECKED_CAST")
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH<BaseData> {
            return when (viewType) {
                TYPE_DEFAULT -> RecyclerVH(parent)
                TYPE_TITLE -> RecyclerTitleVH(parent)
                else -> throw IllegalArgumentException("no type $viewType")
            } as BaseVH<BaseData>
        }

        override fun onBindViewHolder(holder: BaseVH<BaseData>, position: Int) {
            holder.bind(
                mapOf(
                    IEditable.KEY_IS_EDIT to isEditMode,
                    ISelectable.KEY_IS_SELECT to (selectMap.getOrDefault(dataList[position], false))
                ),
                dataList[position],
                position
            )
        }
    }

    companion object {
        private const val TAG = "RecyclerActivity"
    }

}