/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - DefaultRenderer.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/08/24
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery		2024/08/24		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.renderer.normal

class DefaultRenderer private constructor() : RendererGroup(TAG) {
    override fun toString(): String {
        return name
    }

    companion object {
        private const val TAG = "DefaultRender"
    }
}