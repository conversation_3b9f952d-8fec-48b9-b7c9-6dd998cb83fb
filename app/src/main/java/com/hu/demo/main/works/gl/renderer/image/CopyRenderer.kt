/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - CopyRenderer.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/09/24
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * huca<PERSON><PERSON>@Apps.Gallery		2024/09/24		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.renderer.image

import android.graphics.RectF
import android.opengl.GLES30
import android.opengl.Matrix
import com.hu.demo.main.works.gl.canvas.FboGlCanvas
import com.hu.demo.main.works.gl.canvas.FboKey
import com.hu.demo.main.works.gl.canvas.GlCanvas
import com.hu.demo.main.works.gl.renderer.addToPool
import com.hu.demo.main.works.gl.renderer.normal.Renderer
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_IMAGE_TEXTURE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.KEY_OUTPUT_TEXTURE
import com.hu.demo.main.works.gl.renderer.param.KeyName.Companion.getKey
import com.hu.demo.main.works.gl.renderer.param.NodePath.Companion.NEXT_NP
import com.hu.demo.main.works.gl.renderer.param.RenderArgs
import com.hu.demo.main.works.gl.shader.GlShader
import com.hu.demo.main.works.gl.shader.ProgramShader
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.EXEC_SET_FRAGCOLOR
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.EXEC_SET_GL_POSITION
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.EXEC_SET_TEXCOORD
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.EXEC_TEXTURE_COLOR
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_IN_VEC2_TEX_COORD
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_LAYOUT_IN_VEC3_POSITION
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_LAYOUT_IN_VEC3_TEX_COORD
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_OUT_VEC2_TEX_COORD
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_OUT_VEC4_FRAGCOLOR
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_UNIFORM_MAT4_MODEL_M
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_UNIFORM_MAT4_PROJECTION_M
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_UNIFORM_MAT4_VIEW_M
import com.hu.demo.main.works.gl.shader.ShaderFrag.Companion.FIELD_UNIFORM_SAMPLER2D_TEX
import com.hu.demo.main.works.gl.texture.ITexture
import com.hu.demo.main.works.gl.texture.MsgTexture
import com.hu.demo.main.works.gl.texture.RawTexture
import com.hu.demo.main.works.gl.texture.Texture
import com.hu.demo.main.works.gl.utils.*
import com.hu.demo.utils.getOrLog
import kotlin.math.min

/**
 * 执行渲染到新纹理
 *
 * 输入：
 *
 * | key                 | 类型       | 是否必须 | 解释            |
 * | -                   | -          | -       | -               |
 * | [KEY_NEXT_MIX_RATIO]| [Float]    | 否      | 混合的透明的比例 |
 * | [KEY_NEXT_TEXTURE]  | [Texture]  | 是      | 输入纹理         |
 *
 * 输出：
 *
 * | key                | 类型       | 是否必须 | 解释      |
 * | -                  | -          | -       | -         |
 * | [KEY_NEXT_TEXTURE] | [Texture]  | 是      | 输出纹理  |
 */
class CopyRenderer : Renderer(TAG) {
    // 顶点着色器中attribute、uniform
    private val aPosition = AttributeShaderParam("aPosition", POSITION_SIZE)
    private val aTexCoord = AttributeShaderParam("aTexCoord", COORD_SIZE)
    private val uViewM = UniformMat4fvShaderParam("uViewM")
    private val uModelM = UniformMat4fvShaderParam("uModelM")
    private val uProjectionM = UniformMat4fvShaderParam("uProjectionM")

    // 片段着色器中attribute、uniform
    private val uTex = Uniform1iShaderParam("uTex")

    override val handleParams: Array<ShaderParam> = arrayOf(
        aPosition,
        aTexCoord,
        uViewM,
        uModelM,
        uProjectionM,
        uTex
    )

    private val viewM = FloatArray(MATRIX_SIZE).apply {
        Matrix.setIdentityM(this, 0)
    }
    private val modelM = FloatArray(MATRIX_SIZE).apply {
        Matrix.setIdentityM(this, 0)
    }
    private val projectionM = FloatArray(MATRIX_SIZE).apply {
        Matrix.setIdentityM(this, 0)
    }
    override val programShader by lazy { ProgramShader(VS_SHADER_BUILDER, FS_SHADER_BUILDER) }

    override fun install(shader: ProgramShader?): GlProgram {
        val glProgram = super.install(shader)!!
        // 如果shader为null，则代表program是当前Renderer内部通过programShader生成的，需要设定相关参数
        if (shader == null) {
            glProgram.use()
            val bufferDataBinder = BufferDataBinder.obtain(vertices)
            bufferDataBinder.install()

            bufferDataBinder.use()
            aPosition.setValue(STRIDE, POSITION_OFFSET)
            aTexCoord.setValue(STRIDE, COORD_OFFSET)
            bufferDataBinder.unUse()

            Matrix.setIdentityM(viewM, 0)
            uViewM.setValue(viewM)
            glProgram.unUse()
        }

        return glProgram
    }

    override fun render(renderArgs: RenderArgs, glProgram: GlProgram?) {
        glProgram!!

        val texture = renderArgs.get<ITexture>(NEXT_NP.getKey(KEY_IMAGE_TEXTURE))
            ?.addToPool(renderArgs).getOrLog(TAG, "[render] texture") ?: return
        texture.load()

        glProgram.use()

        val portWidth = texture.width
        val portHeight = texture.height

        Matrix.setIdentityM(viewM, 0)
        uViewM.setValue(viewM)

        calcModelM(texture, portWidth, portHeight)
        uModelM.setValue(modelM)

        calcProjectionM(portWidth, portHeight)
        uProjectionM.setValue(projectionM)

        val texUnitIndex0 = 0
        texture.active(texUnitIndex0)
        uTex.setValue(texUnitIndex0)

        val outTexture: ITexture
        if (glProgram.programShader == programShader) {
            val bufferDataBinder = BufferDataBinder.obtain(vertices)
            bufferDataBinder.bind()

            // 由于 OpenGL的状态机特性，以下的纹理active后的bind操作都会绑定到刚刚的纹理单元上，故需要提前创建输出纹理
            outTexture = (renderArgs.get<ITexture>(NEXT_NP.getKey(KEY_OUTPUT_TEXTURE))
                ?: RawTexture(portWidth, portHeight, texture.colorSpace, texture.texConfig))
                .addToPool(renderArgs)
            GLES30.glActiveTexture(GLES30.GL_TEXTURE31)
            outTexture.load()

            val fboKey = FboKey(vertices.capacity() / STRIDE, portWidth, portHeight)
            val glCanvas = GlCanvas.obtain(fboKey) as FboGlCanvas
            glCanvas.draw(outTexture)
            GlUtil.checkGlError()
            bufferDataBinder.unbind()
            glCanvas.reuse()

        } else {
            outTexture = MsgTexture(portWidth, portHeight, 0,texture.colorSpace, texture.texConfig)
        }
        glProgram.unUse()
        renderArgs[NEXT_NP.getKey(KEY_IMAGE_TEXTURE)] = outTexture
    }

    private fun calcProjectionM(portWidth: Int, portHeight: Int) {
        Matrix.setIdentityM(projectionM, 0)
        Matrix.orthoM(projectionM, 0, 0f, portWidth.toFloat(), portHeight.toFloat(), 0f, -1f, 1f)
    }

    private fun calcModelM(texture: ITexture, portWidth: Int, portHeight: Int) {
        Matrix.setIdentityM(modelM, 0)
        val displayRect = calcDisplayRect(texture.width.toFloat(), texture.height.toFloat(), portWidth.toFloat(), portHeight.toFloat())
        Matrix.translateM(modelM, 0, displayRect.left, displayRect.top, 0f)
        Matrix.scaleM(modelM, 0, displayRect.width(), displayRect.height(), 1f)
    }

    private fun calcDisplayRect(imageWidth: Float, imageHeight: Float, portWidth: Float, portHeight: Float): RectF {
        val ratio = min(portWidth / imageWidth, portHeight / imageHeight)
        val displayWidth = imageWidth * ratio
        val displayHeight = imageHeight * ratio
        val left = (portWidth - displayWidth) / 2
        val top = (portHeight - displayHeight) / 2
        return RectF(left, top, left + displayWidth, top + displayHeight)
    }

    override fun toString(): String {
        return name
    }

    companion object {
        const val TAG = "CopyRenderer"

        private val VS_SHADER_BUILDER by lazy {
            GlShader().apply {
                addField(FIELD_LAYOUT_IN_VEC3_POSITION)
                addField(FIELD_LAYOUT_IN_VEC3_TEX_COORD)
                addField(FIELD_UNIFORM_MAT4_VIEW_M)
                addField(FIELD_UNIFORM_MAT4_MODEL_M)
                addField(FIELD_UNIFORM_MAT4_PROJECTION_M)
                addField(FIELD_OUT_VEC2_TEX_COORD)

                addExecEnd(EXEC_SET_GL_POSITION)
                addExecEnd(EXEC_SET_TEXCOORD)
            }
        }

        private val FS_SHADER_BUILDER by lazy {
            GlShader().apply {
                addField(FIELD_IN_VEC2_TEX_COORD)
                addField(FIELD_OUT_VEC4_FRAGCOLOR)
                addField(FIELD_UNIFORM_SAMPLER2D_TEX)

                addExecStart(EXEC_TEXTURE_COLOR)
                addExecEnd(EXEC_SET_FRAGCOLOR)
            }
        }

        /**
         * 步长
         */
        private const val STRIDE = 5

        /**
         * 矩阵大小
         */
        private const val MATRIX_SIZE = 16

        /**
         * 顶点位置在buffer中的偏移
         */
        private const val POSITION_OFFSET = 0

        /**
         * 坐标在buffer中的偏移
         */
        private const val COORD_OFFSET = 3

        /**
         * 顶点位置的大小
         */
        private const val POSITION_SIZE = 3

        /**
         * 坐标的大小
         */
        private const val COORD_SIZE = 2

        /**
         * 顶点缓冲数组
         */
        private val vertices = floatArrayOf(
            // X, Y, Z, U, V
            0f, 0f, 0.0f, 0.0f, 1.0f,
            1f, 0f, 0.0f, 1.0f, 1.0f,
            0f, 1f, 0.0f, 0.0f, 0.0f,
            1f, 1f, 0.0f, 1.0f, 0.0f
        ).toBuffer()
    }
}
