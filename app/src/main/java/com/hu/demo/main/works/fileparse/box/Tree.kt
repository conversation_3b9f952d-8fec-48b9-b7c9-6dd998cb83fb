package com.hu.demo.main.works.fileparse.box

import com.hu.demo.utils.ByteOrderedDataInputStream
import com.hu.demo.utils.readUtf8
import okio.utf8Size

abstract class Tree(val nodeName: String, val level: Int = 0, val parent: Tree? = null) {
    val children = mutableListOf<Tree>()
    var alreadyReadSize: Long = 0L
        internal set(value) {
            val old = field
            field = value
            parent?.also {
                it.alreadyReadSize += value - old
            }
        }

    abstract val realSize: Long

    val rootTree: Tree by lazy {
        var tree = this
        while (tree.parent != null) {
            tree = tree.parent!!
        }
        tree
    }

    init {
        parent?.children?.add(this)
    }

    fun hasNumByteLeft(): <PERSON><PERSON>an {
        return alreadyReadSize < realSize
    }

    protected fun ByteOrderedDataInputStream.nReadInt(): Int {
        return readInt().apply { alreadyReadSize += 4 }
    }

    protected fun ByteOrderedDataInputStream.nReadUInt(): UInt {
        return readUnsignedInt().apply { alreadyReadSize += 4 }.toUInt()
    }

    protected fun ByteOrderedDataInputStream.nReadShort(): Short {
        return readShort().apply { alreadyReadSize += 2 }
    }

    protected fun ByteOrderedDataInputStream.nReadUShort(): UShort {
        return readUnsignedShort().apply { alreadyReadSize += 2 }.toUShort()
    }

    protected fun ByteOrderedDataInputStream.nReadByte(): Byte {
        return readByte().apply { alreadyReadSize += 1 }
    }

    protected fun ByteOrderedDataInputStream.nReadUByte(): UByte {
        return readUnsignedByte().apply { alreadyReadSize += 1 }.toUByte()
    }

    protected fun ByteOrderedDataInputStream.nReadLong(): Long {
        return readLong().apply { alreadyReadSize += 8 }
    }

    protected fun ByteOrderedDataInputStream.nReadULong(): ULong {
        return readLong().apply { alreadyReadSize += 8 }.toULong()
    }

    protected fun ByteOrderedDataInputStream.nReadBytes(length: Int): ByteArray {
        return ByteArray(length) { nReadByte() }
    }

    protected fun ByteOrderedDataInputStream.nReadInts(length: Int): IntArray {
        return IntArray(length) { nReadInt() }
    }

    protected fun ByteOrderedDataInputStream.nReadUTF(): String {
        return readUtf8().apply { alreadyReadSize += utf8Size() + 1 }
    }

    fun ByteOrderedDataInputStream.nSkipFully(size: Int) {
        skipFully(size).apply { alreadyReadSize += size }
    }
}