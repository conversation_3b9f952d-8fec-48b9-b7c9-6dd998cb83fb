/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - GlUtil.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/09/05
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * huca<PERSON><PERSON>@Apps.Gallery		2024/09/05		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.utils

import android.opengl.GLES30
import android.opengl.GLU
import android.util.Log
import com.hu.demo.utils.toHexString

object GlUtil {
    private const val TAG = "GlUtil"

    @JvmStatic
    fun checkGlError() {
        val errorMsgSb = StringBuilder()
        var foundError = false
        var error: Int
        while ((GLES30.glGetError().also { error = it }) != GLES30.GL_NO_ERROR) {
            if (foundError) {
                errorMsgSb.append('\n')
            }
            errorMsgSb.append("glError: ${error.toHexString()}, ").append(GLU.gluErrorString(error))
            foundError = true
        }
        if (foundError) {
            Log.e(TAG, errorMsgSb.toString(), GlException(errorMsgSb.toString()))
        }
    }

    @JvmStatic
    fun checkGlException(expression: Boolean, errorMessage: String?) {
        if (!expression) {
            Log.e(TAG, "" + errorMessage)
        }
    }

    class GlException(message: String? = null) : Exception(message)
}