/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - ISelectable.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2022/01/18
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * huca<PERSON><PERSON>@Apps.Gallery		2022/01/18		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.recyclerview.slidingselect

interface ISelectable {
    var isSelect: Boolean
    fun isSelectable(): Boolean = true

    companion object {
        const val KEY_IS_SELECT = "isSelect.key"
    }
}