package com.hu.demo.main.works.decode

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.ImageDecoder
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.EditText
import android.widget.ImageView
import android.widget.Spinner
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.contract.ActivityResultContract
import androidx.appcompat.widget.SwitchCompat
import androidx.core.graphics.drawable.toBitmap
import androidx.heifwriter.HeifWriter
import androidx.heifwriter.HeifWriter.INPUT_MODE_BITMAP
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.hu.demo.main.R
import com.hu.demo.main.adapter.BaseData
import com.hu.demo.main.adapter.BaseVH
import com.hu.demo.main.adapter.DefaultAdapter
import com.hu.demo.utils.setImageUriAny
import com.hu.demo.base.ui.BaseActivity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.lang.Float.min

class DecodeImageActivity : BaseActivity(), View.OnClickListener {
    private var selectUri: Uri? = null
    private var ivImage: ImageView? = null
    private var etMaxWidth: EditText? = null
    private var etMaxCount: EditText? = null
    private var etInterval: EditText? = null
    private var switchLog: SwitchCompat? = null
    private var btnDecode: Button? = null
    private var spFormat: Spinner? = null
    private var rvRecycler: RecyclerView? = null
    private var logAdapter: LogAdapter? = null
    private var job: Job? = null
    private val pickTypeResultContract = PickTypeResultContract()
    private val pickImageLauncher = registerForActivityResult(
        pickTypeResultContract,
        PickOnlyCallback { changeData(it) }
    )

    override fun getLayoutId(): Int {
        return R.layout.activity_decode_image
    }

    override fun initToolbar() {
        super.initToolbar()
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowTitleEnabled(true)
            setDisplayShowHomeEnabled(false)
        }
    }

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        ivImage = findViewById(R.id.ivImage)
        etMaxWidth = findViewById(R.id.etMaxWidth)
        etMaxCount = findViewById(R.id.etMaxCount)
        etInterval = findViewById(R.id.etInterval)
        switchLog = findViewById(R.id.switchLog)
        btnDecode = findViewById(R.id.btnDecode)
        spFormat = findViewById(R.id.spFormat)
        rvRecycler = findViewById(R.id.rvRecycler)
    }

    override fun initEvent(savedInstanceState: Bundle?) {
        super.initEvent(savedInstanceState)
        ivImage?.setOnClickListener(this)
        btnDecode?.setOnClickListener(this)
        rvRecycler?.apply {
            addItemDecoration(DividerItemDecoration(this@DecodeImageActivity, RecyclerView.VERTICAL))
            adapter = LogAdapter(this).apply {
                resetData(emptyList())
                logAdapter = this
            }
            layoutManager = LinearLayoutManager(this@DecodeImageActivity, RecyclerView.VERTICAL, false)
        }
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.ivImage -> {
                pickImageLauncher.launch(MIME_TYPE)
            }

            R.id.btnDecode -> {
                val uri = selectUri ?: return
                val maxWidth = etMaxWidth?.text?.toString()?.toInt() ?: return
                val count = etMaxCount?.text?.toString()?.toInt() ?: return
                val interval = etInterval?.text?.toString()?.toLong() ?: return
                lifecycleScope.launch(Dispatchers.IO) {
                    logStart(maxWidth, count, interval)
                    var avgDecodeTime = 0f
                    var avgEncodeTime = 0f
                    (0 until count).forEach { index ->
                        val lastTime1 = System.currentTimeMillis()
                        val bitmap = decode(uri, maxWidth)
                        val time1 = (System.currentTimeMillis() - lastTime1).toFloat()
                        avgDecodeTime = (avgDecodeTime * index + time1) / (index + 1)
                        addLog(LogData("decode index: $index, cost: $time1 ms"))
                        val lastTime2 = System.currentTimeMillis()
                        encode(bitmap)
                        val time2 = (System.currentTimeMillis() - lastTime2).toFloat()
                        avgEncodeTime = (avgEncodeTime * index + time2) / (index + 1)
                        addLog(LogData("encode index: $index, cost: $time2 ms"))
                        delay(interval)
                    }
                    addLog(
                        LogData(
                            "解码完成, decode avg: ${String.format("%.2f", avgDecodeTime)} ms, encode avg: ${String.format("%.2f", avgEncodeTime)} ms"
                        )
                    )
                    logEnd()
                }
            }
        }
    }

    private fun encode(bitmap: Bitmap) {
        Log.d(TAG, "encode: start <${bitmap.width}, ${bitmap.height}>")
        val lastTime = System.currentTimeMillis()
        if (spFormat!!.selectedItemPosition == 0) {
            val file = File(cacheDir, "${System.currentTimeMillis()}.jpg")
            FileOutputStream(file).use {
                bitmap.compress(Bitmap.CompressFormat.JPEG, 95, it)
            }
        } else {
            val file = File(cacheDir, "${System.currentTimeMillis()}.heic")
            FileOutputStream(file).use {
                HeifWriter.Builder(it.fd, bitmap.width, bitmap.height, INPUT_MODE_BITMAP)
                    .build()
                    .use { writer ->
                        writer.start()
                        writer.addBitmap(bitmap)
                        writer.stop(HEIF_WRITE_TIME_OUT)
                    }
            }
        }
        Log.d(TAG, "encode: time: ${System.currentTimeMillis() - lastTime}")
    }

    private suspend fun logStart(maxWidth: Int, count: Int, interval: Long) = withContext(Dispatchers.Main) {
        logAdapter?.apply {
            resetData(emptyList())
            notifyDataSetChanged()
            addLog(LogData("解码开始"))
        }
        Toast.makeText(this@DecodeImageActivity, "maxWidth: $maxWidth, count: $count, interval: $interval", Toast.LENGTH_SHORT).show()
    }

    private suspend fun logEnd() = withContext(Dispatchers.Main) {
        Toast.makeText(this@DecodeImageActivity, "解码完成", Toast.LENGTH_SHORT).show()
    }

    private suspend fun addLog(logData: LogData) = withContext(Dispatchers.Main) {
        if (switchLog?.isChecked == true) {
            logAdapter?.apply {
                dataList.add(logData)
                notifyItemInserted(dataList.lastIndex)
                rvRecycler?.scrollToPosition(dataList.lastIndex)
            }
        }
    }

    private fun changeData(obj: Any?) {
        ivImage?.also {
            this.selectUri = obj as Uri
            it.setImageUriAny(selectUri)
        }
    }

    private fun writeCache(view: ImageView, maxWh: Int): Bitmap {
        Log.d(TAG, "writeCache: start maxWh: $maxWh")
        val lastTime = System.currentTimeMillis()
        val drawable = view.drawable
        val scale = min(maxWh / drawable.intrinsicWidth.toFloat(), maxWh / drawable.intrinsicHeight.toFloat())
        val bitmap = view.drawable.let { it.toBitmap((it.intrinsicWidth * scale).toInt(), (it.intrinsicHeight * scale).toInt()) }
        Log.d(TAG, "writeCache: time: ${System.currentTimeMillis() - lastTime}, ${bitmap.width to bitmap.height}")
        return bitmap
    }

    private fun decode(uri: Uri, maxWh: Int): Bitmap {
        Log.d(TAG, "decode: start maxWh: $maxWh")
        val lastTime = System.currentTimeMillis()
        val bitmap: Bitmap = ImageDecoder.decodeBitmap(ImageDecoder.createSource(contentResolver, uri)) { decoder, imageInfo, _ ->
            decoder.allocator = ImageDecoder.ALLOCATOR_SOFTWARE
            val scale = minOf(imageInfo.size.width.toFloat() / maxWh, imageInfo.size.height.toFloat() / maxWh)
            decoder.setTargetSampleSize((scale.toInt() shr 1 shl 1).coerceAtLeast(1))
            decoder.setTargetSize((imageInfo.size.width / scale).toInt(), (imageInfo.size.height / scale).toInt())
        }
        Log.d(TAG, "decode: time: ${System.currentTimeMillis() - lastTime}, ${bitmap.width to bitmap.height}")
        return bitmap
    }

    class LogVH(parent: ViewGroup) : BaseVH<LogData>(parent, R.layout.item_decode_log) {
        private val tvItem: TextView = findViewById(R.id.tv_item)

        override fun bind(extraData: Map<String, Any>, data: LogData, position: Int) {
            tvItem.text = data.name
        }
    }

    data class LogData(val name: String?) : BaseData(TYPE_DEFAULT)

    class LogAdapter(recyclerView: RecyclerView) : DefaultAdapter<BaseData, BaseVH<BaseData>>(recyclerView) {
        @Suppress("UNCHECKED_CAST")
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH<BaseData> {
            return LogVH(parent) as BaseVH<BaseData>
        }

        override fun onBindViewHolder(holder: BaseVH<BaseData>, position: Int) {
            holder.bind(emptyMap(), dataList[position], position)
        }
    }

    private open class PickOnlyCallback(private val resultFunc: (Uri) -> Unit) : ActivityResultCallback<Uri?> {
        override fun onActivityResult(result: Uri?) {
            result ?: run {
                Log.d(TAG, "PickOnlyCallback.onActivityResult: ")
                return
            }
            Log.d(TAG, "onActivityResult: $result")
            resultFunc(result)
        }
    }

    private class PickTypeResultContract : ActivityResultContract<String, Uri?>() {
        override fun createIntent(context: Context, input: String): Intent {
            return Intent(Intent.ACTION_PICK).setType(input)
        }

        override fun parseResult(resultCode: Int, intent: Intent?): Uri? {
            return if (resultCode != Activity.RESULT_OK) null else intent?.data
        }
    }


    companion object {
        private const val TAG = "DecodeActivity"
        private const val MIME_TYPE = "image/*"
        private const val HEIF_WRITE_TIME_OUT = 5000L

        private const val TYPE_DEFAULT = 0
    }
}