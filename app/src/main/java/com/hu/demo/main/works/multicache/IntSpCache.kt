package com.hu.demo.main.works.multicache

import android.content.SharedPreferences
import android.util.Log

class IntSpCache(private val sp: SharedPreferences) : ICache<String, Int> {
    override val name: String
        get() = NAME

    override fun get(k: String): Int? {
        return sp.getInt(k, Int.MIN_VALUE).takeIf { it != Int.MIN_VALUE }.apply {
            Log.d(TAG, "get: key: $k, value: $this")
        }
    }

    override fun set(k: String, v: Int) {
        return sp.edit().putInt(k, v).apply()
    }

    companion object {
        const val NAME = "IntSpCache"
        private const val TAG = "IntSpCache"
    }
}