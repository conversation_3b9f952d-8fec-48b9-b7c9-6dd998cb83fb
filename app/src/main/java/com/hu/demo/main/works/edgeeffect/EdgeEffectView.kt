package com.hu.demo.main.works.edgeeffect

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BlurMaskFilter
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.RectF
import android.util.AttributeSet
import android.view.View


class EdgeEffectView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    var bitmap: Bitmap? = null
        set(value) {
            field = value?.let { bitmap ->
                Bitmap.createBitmap(bitmap.width, bitmap.height, Bitmap.Config.ARGB_8888).also {
                    Canvas(it).drawBitmap(bitmap.extractAlpha(), 0f, 0f, bitmapPaint)
                }
            }
//            field = value
            invalidate()
        }

    private val cacheSrcRect = Rect(0, 0, 0, 0)
    private val cacheDstRectF = RectF(50f, 200f, 1000f, 1500f)
    private val bitmapPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.RED
        isFilterBitmap = true
        maskFilter = BlurMaskFilter(50f, BlurMaskFilter.Blur.OUTER);
    }
    private val normalPaint = Paint(Paint.ANTI_ALIAS_FLAG)

    override fun onDraw(canvas: Canvas) {
        cacheDstRectF.also {
            it.set(left.toFloat(),top.toFloat(),right.toFloat(),bottom.toFloat())
            it.inset(PADDING, PADDING)
        }
        bitmap?.also {
            cacheSrcRect.set(0, 0, it.width, it.height)
            canvas.drawBitmap(it, cacheSrcRect, cacheDstRectF, normalPaint)
//            canvas.drawBitmap(it.extractAlpha(), 0f,0f, bitmapPaint)
        }
    }

    companion object{
        private const val PADDING = 100f
    }
}