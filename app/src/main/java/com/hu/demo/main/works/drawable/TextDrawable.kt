package com.hu.demo.main.works.drawable

import android.annotation.SuppressLint
import android.graphics.*
import android.graphics.drawable.Drawable
import android.text.TextPaint
import kotlin.properties.Delegates

@SuppressLint("RestrictedApi")
class TextDrawable(text: String = "", textSize: Float = DEFAULT_SIZE, textColor: Int = DEFAULT_COLOR) : Drawable() {
    private val textPaint = TextPaint(Paint.ANTI_ALIAS_FLAG).apply {
        this.textSize = textSize
        this.color = textColor
    }
    var text: String = text
        set(value) {
            field = value
            invalidateSelf()
        }

    var textSize: Float by Delegates.observable(textSize) { _, _, new ->
        textPaint.textSize = new
        invalidateSelf()
    }

    var textColor: Int by Delegates.observable(textColor) { _, _, new ->
        textPaint.color = new
        invalidateSelf()
    }

    override fun draw(canvas: Canvas) {
        val textBounds = Rect()
        textPaint.getTextBounds(text, 0, text.length, textBounds)
        canvas.drawText(text, (bounds.width() - textBounds.width()) / 2f, (bounds.height() + textBounds.height()) / 2f, textPaint)
    }

    override fun getAlpha(): Int {
        return textPaint.alpha
    }

    override fun setAlpha(alpha: Int) {
        textPaint.alpha = alpha
        invalidateSelf()
    }

    override fun setColorFilter(colorFilter: ColorFilter?) {
        textPaint.colorFilter = colorFilter
        invalidateSelf()
    }

    @Deprecated("Deprecated in Java")
    override fun getOpacity(): Int {
        return PixelFormat.TRANSLUCENT
    }

    companion object {
        private const val DEFAULT_SIZE = 36f
        private const val DEFAULT_COLOR = Color.BLACK
    }
}
