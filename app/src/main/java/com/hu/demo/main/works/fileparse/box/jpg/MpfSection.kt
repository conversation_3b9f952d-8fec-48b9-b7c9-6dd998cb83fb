package com.hu.demo.main.works.fileparse.box.jpg

import android.util.Log
import com.hu.demo.main.works.fileparse.box.Tree
import com.hu.demo.utils.ByteOrderedDataInputStream
import com.hu.demo.utils.mark
import com.hu.demo.utils.readByteOrder
import com.hu.demo.utils.toHexString
import com.hu.demo.utils.toInt
import com.hu.demo.utils.toString
import java.nio.ByteOrder
import kotlin.text.Charsets.US_ASCII

internal class MpfSection(parent: Tree) : IdentifierSection(parent) {
    private val sb = StringBuilder()

    override fun read(bis: ByteOrderedDataInputStream) {
        super.read(bis)
        val contentSize = allSize - alreadyReadSize.toInt()

        val mpfBis = bis.mark(contentSize) {
            ByteOrderedDataInputStream(ByteArray(contentSize).apply(::read))
        }
        val byteOrder = mpfBis.readByteOrder()
        sb.append(" $byteOrder")
        mpfBis.byteOrder = byteOrder
        val offset = mpfBis.readInt()
        sb.append("\n\t offset: $offset")
        mpfBis.skipFully(offset - mpfBis.position())

        val count = mpfBis.readShort()
        sb.append(" count: $count")
        var version: String?
        var num: Int? = null
        var entryOffset: Int? = null
        for (index in 0 until count) {
            sb.append("\n\t\t")
            val mpIndex = mpfBis.readShort()
            if (mpIndex == MPF_IFD_TAG_VERSION) {
                val byteArray = ByteArray(10)
                mpfBis.read(byteArray)
                version = byteArray.toString(byteArray.size - 4, 4, US_ASCII)
                sb.append(" version: $version")
            } else if (mpIndex == MPF_IFD_TAG_NUMBER) {
                val byteArray = ByteArray(10)
                mpfBis.read(byteArray)
                num = byteArray.sliceArray(byteArray.size - 4 until byteArray.size).toInt(byteOrder == ByteOrder.LITTLE_ENDIAN)
                sb.append(" num: $num")
            } else if (mpIndex == MPF_IFD_TAG_MP_ENTRY) {
                val byteArray = ByteArray(10)
                mpfBis.read(byteArray)
                entryOffset = byteArray.sliceArray(byteArray.size - 4 until byteArray.size).toInt(byteOrder == ByteOrder.LITTLE_ENDIAN)
                sb.append(" entryOffset: ${entryOffset.toHexString()}")
            } else {
                break
            }
        }

        mpfBis.skipFully(entryOffset!! - mpfBis.position())

        for (index in 0 until num!!) {
            sb.append("\n\t\t<\n\t\t")
            val mpType = mpfBis.readUnsignedInt()
            sb.append(" TypeCode: ${mpType.toHexString()}")
            if (mpType == MPF_MP_TYPE_CODE_BASE_IMAGE) { // 主图
                sb.append(" | baseImage")
            } else if (mpType == MPF_MP_TYPE_CODE_UNDEFINED) { // 未定义
                sb.append(" | undefined")
            }
            sb.append("\n\t\t")
            val imageSize = mpfBis.readInt()
            sb.append(" imageSize: $imageSize")
            sb.append("\n\t\t")
            val imageOffset = mpfBis.readInt()
            sb.append(" imageOffset: $imageOffset")

            val originOffset = imageOffset + IDENTIFIER_MPF_APP2.size + 1
            bis.mark(originOffset + CHECK_SIZE) {
                read(ByteArray(originOffset))
                Log.d("JpgFileParser", "readMpf: offset:${originOffset.toHexString()}")
                val byte1 = readByte()
                val byte2 = readByte()
                val byte3 = readByte()
                val byte4 = readByte()
                sb.append(" | checkNaxtData: ${byte1.toHexString()} ${byte2.toHexString()} ${byte3.toHexString()} ${byte4.toHexString()}")
            }
            sb.append("\n\t\t")
            val entry1No = mpfBis.readShort()
            sb.append(" entry1No: $entry1No")
            val entry2No = mpfBis.readShort()
            sb.append(" entry2No: $entry2No")
            sb.append("\n\t\t>")
        }
    }

    override fun toString(): String {
        return "${super.toString()}\n$sb"
    }

    companion object {
        private const val MPF_IFD_TAG_VERSION = 0xB000.toShort()
        private const val MPF_IFD_TAG_NUMBER = 0xB001.toShort()
        private const val MPF_IFD_TAG_MP_ENTRY = 0xB002.toShort()
        private const val MPF_MP_TYPE_CODE_BASE_IMAGE = 0x030000.toLong()
        private const val MPF_MP_TYPE_CODE_UNDEFINED = 0x000000.toLong()
        private val IDENTIFIER_MPF_APP2 = "MPF".toByteArray(US_ASCII)
        private const val CHECK_SIZE = 10
    }
}
