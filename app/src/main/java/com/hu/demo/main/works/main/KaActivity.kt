package com.hu.demo.main.works.main

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.Environment
import android.provider.Settings
import android.util.Log
import android.view.ViewGroup
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.contract.ActivityResultContracts.RequestMultiplePermissions
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.hu.demo.base.ui.BaseActivity
import com.hu.demo.base.ui.DeepLink
import com.hu.demo.main.R
import com.hu.demo.main.adapter.BaseVH
import com.hu.demo.main.adapter.DefaultAdapter
import com.hu.demo.main.works.main.datas.UiItemData
import com.hu.demo.main.works.main.vhs.UiItemPageVH
import com.hu.demo.utils.ApiLevelUtil

class KaActivity : BaseActivity() {
    private var recycler: RecyclerView? = null
    private val datas: MutableList<UiItemData> = mutableListOf()
    private val permissionsLauncher = registerForActivityResult(
        RequestMultiplePermissions(),
        PermissionsCallback()
    )

    override fun getLayoutId(): Int {
        return R.layout.activity_ka
    }

    override fun initData(savedInstanceState: Bundle?) {
        datas.add(UiItemData(DeepLink.buildUiLink("PickActivity"), "选图测试"))
        datas.add(UiItemData(DeepLink.buildUiLink("ClipboardActivity"), "剪贴板测试"))
        datas.add(UiItemData(DeepLink.buildUiLink("LiveDataActivity"), "LiveData测试"))
        datas.add(UiItemData(DeepLink.buildUiLink("RecyclerActivity"), "Recycler测试"))
        datas.add(UiItemData(DeepLink.buildUiLink("NotificationActivity"), "通知测试"))
        datas.add(UiItemData(DeepLink.buildUiLink("GridDrawableActivity"), "GridDrawable测试"))
        datas.add(UiItemData(DeepLink.buildUiLink("FlowActivity"), "Flow测试"))
        datas.add(UiItemData(DeepLink.buildUiLink("ShareActivity"), "Share测试"))
        datas.add(UiItemData(DeepLink.buildUiLink("EdgeEffectActivity"), "边缘效果测试"))
        datas.add(UiItemData(DeepLink.buildUiLink("ClipboardActivity"), "剪贴板测试"))
        datas.add(UiItemData(DeepLink.buildUiLink("DragActivity"), "Drag测试"))
        datas.add(UiItemData(DeepLink.buildUiLink("UhdrActivity"), "UHDR测试"))
        datas.add(UiItemData(DeepLink.buildUiLink("PagingActivity"), "Paging测试"))
        datas.add(UiItemData(DeepLink.buildUiLink("DecodeImageActivity"), "图片解码测试"))
        datas.add(UiItemData(DeepLink.buildUiLink("DecodeVideoActivity"), "视频解码测试"))
        datas.add(UiItemData(DeepLink.buildUiLink("ExifActivity"), "Exif测试"))
        datas.add(UiItemData(DeepLink.buildUiLink("SensorActivity"), "Sensor测试"))
        datas.add(UiItemData(DeepLink.buildUiLink("ExifImageActivity"), "Exif Image测试"))
        datas.add(UiItemData(DeepLink.buildUiLink("FileParseActivity"), "File Parse 测试"))
        datas.add(UiItemData(DeepLink.buildUiLink("ImageSizeActivity"), "ImageSize测试"))
        datas.add(UiItemData(DeepLink.buildUiLink("VideoPlayerActivity"), "视频播放器测试"))
        datas.add(UiItemData(DeepLink.buildUiLink("PdfActivity"), "PDF测试"))
        datas.add(UiItemData(DeepLink.buildUiLink("GlActivity"), "GL测试"))
        datas.add(UiItemData(DeepLink.buildUiLink("MixActivity"), "MIX测试"))
        datas.add(UiItemData(DeepLink.buildUiLink("LutActivity"), "LUT测试"))
        datas.add(UiItemData(DeepLink.buildUiLink("CanvasActivity"), "Canvas测试"))
        datas.add(UiItemData(DeepLink.buildUiLink("ColorActivity"), "Color测试"))
        datas.add(UiItemData(DeepLink.buildUiLink("VideoInfoActivity"), "视频信息测试"))
    }

    override fun initView(savedInstanceState: Bundle?) {
        recycler = findViewById(R.id.recycler)
    }

    override fun initEvent(savedInstanceState: Bundle?) {
        recycler?.apply {
            addItemDecoration(DividerItemDecoration(this@KaActivity, RecyclerView.VERTICAL))
            adapter = KaAdapter(this).apply {
                resetData(datas)
            }
            layoutManager = LinearLayoutManager(this@KaActivity, RecyclerView.VERTICAL, false)
        }
    }

    override fun onResume() {
        super.onResume()
        if (ApiLevelUtil.isAtLeastAndroidR() && !Environment.isExternalStorageManager()) {
            val intent = Intent()
            intent.setAction(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION);
            intent.setData(Uri.parse("package:$packageName"));
            intent.setPackage("com.android.settings");
            startActivity(intent)
        }
    }

    private open class PermissionsCallback() : ActivityResultCallback<Map<String, Boolean>> {
        override fun onActivityResult(result: Map<String, Boolean>) {
            Log.d(TAG, "onActivityResult: $result")
        }
    }

    class KaAdapter(recyclerView: RecyclerView) : DefaultAdapter<UiItemData, BaseVH<UiItemData>>(recyclerView) {
        @Suppress("UNCHECKED_CAST")
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH<UiItemData> {
            return UiItemPageVH(parent)
        }

        override fun onBindViewHolder(holder: BaseVH<UiItemData>, position: Int) {
            holder.bind(emptyMap(), dataList[position], position)
        }
    }

    companion object {
        private const val TAG = "KaActivity"
    }
}