/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - GainmapTexture.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/09/19
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery		2024/09/19		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.texture

import android.graphics.Bitmap
import android.graphics.Gainmap
import android.opengl.GLES30
import android.os.Build
import androidx.annotation.RequiresApi
import com.hu.demo.main.works.gl.texture.ITexture.Companion.getFormat
import com.hu.demo.main.works.gl.texture.ITexture.Companion.getInternalFormat
import com.hu.demo.main.works.gl.texture.ITexture.Companion.getType
import com.hu.demo.main.works.gl.utils.GlUtil
import com.hu.demo.utils.requireColorSpace
import com.hu.demo.utils.toBuffer

/**
 * 增益图的Texture，其中要包含增益纹理以及增益参数
 *
 * @param maskImage 增益图
 * @param metadataPack 增益参数
 * @param canRecycle 上传纹理后是否可以回收[maskImage]
 */
@RequiresApi(Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
class GainmapTexture(
    override val gainmap: Gainmap,
    private val canRecycle: Boolean = false,
) : Texture(
    TexKey(
        gainmap.gainmapContents.width,
        gainmap.gainmapContents.height,
        texConfig = gainmap.gainmapContents.config!!.toTexConfig()
    ),
    gainmap.gainmapContents.requireColorSpace
), IGainTexture {
    override val tag: String = TAG
    private var maskImage: Bitmap? = gainmap.gainmapContents

    override fun load() {
        if (hasUpload) return
        val markImage = maskImage ?: return

        realTexture.value.load()
        GLES30.glTexSubImage2D(texTarget, 0, 0, 0, width, height, getFormat(texConfig.getInternalFormat()), texConfig.getType(), markImage.toBuffer())
        GlUtil.checkGlError()
        GLES30.glFlush()

        hasUpload = true

        if (canRecycle) {
            markImage.recycle()
            this.maskImage = null
        }
    }

    override fun buildString(): MutableMap<String, Any?> {
        return super.buildString().apply {
            this["canRecycle"] = canRecycle
        }
    }

    private companion object {
        private const val TAG = "GainmapTexture"
    }
}