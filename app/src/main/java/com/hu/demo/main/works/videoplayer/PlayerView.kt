package com.hu.demo.main.works.videoplayer

import android.content.Context
import android.graphics.SurfaceTexture
import android.graphics.drawable.ColorDrawable
import android.media.MediaPlayer
import android.net.Uri
import android.util.AttributeSet
import android.util.Log
import android.view.Surface
import android.view.SurfaceHolder
import android.view.SurfaceView
import android.view.TextureView
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import kotlinx.coroutines.Job

class PlayerView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0,
) : FrameLayout(context, attrs, defStyleAttr) {
    private var task: Job? = null
    private val seekCallbacks = mutableSetOf<ISeekCallback>()
    private var mediaPlayer: MediaPlayer = MediaPlayer().apply {
        setOnPreparedListener {
            Log.d(TAG, "prepare: is prepared")
            it.start()
        }
        setOnErrorListener { mp, what, extra ->
            Log.e(TAG, "onError: what: $what, extra: $extra")
            true
        }
        setOnSeekCompleteListener { player ->
            val percent = player.currentPosition.toFloat() / player.duration
            seekCallbacks.forEach { it.onSeek(percent) }
        }
    }

    fun setVideo(uri: Uri) {
        mediaPlayer.reset()
        mediaPlayer.setDataSource(context, uri)
        mediaPlayer.prepareAsync()
    }

    private val imageView: ImageView = ImageView(context).apply {
        layoutParams = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT)
    }
    var videoView: View? = null
        private set

    init {
        addView(imageView)

        imageView.setImageDrawable(ColorDrawable(0x66FFFFFF))
    }

    fun addOnSeekCallback(seekCallback: ISeekCallback) {
        seekCallbacks.add(seekCallback)
    }

    fun rotationSurface(rotation: Float) {
        videoView?.rotation = rotation
    }

    fun scaleSurface(scale: Float) {
        videoView?.scaleX = scale
        videoView?.scaleY = scale
    }

    fun pause() {
        if (mediaPlayer.isPlaying) {
            mediaPlayer.pause()
        }
    }

    fun play() {
        mediaPlayer.start()
    }

    fun seek(percent: Float) {
        mediaPlayer.seekTo((mediaPlayer.duration * percent).toInt())
    }

    fun changeVideoView(type: Int) {
        if (type == 0) {
            removeView(videoView)
            val surfaceView = SurfaceView(context)
            videoView = surfaceView
            surfaceView.holder.addCallback(object : SurfaceHolder.Callback {
                override fun surfaceCreated(holder: SurfaceHolder) {
                    Log.e(TAG, "surfaceCreated: ")
                    mediaPlayer.setSurface(holder.surface)
                }

                override fun surfaceChanged(holder: SurfaceHolder, format: Int, width: Int, height: Int) {
                    mediaPlayer.setSurface(holder.surface)
                    Log.e(TAG, "surfaceChanged: format: $format, width: $width, height: $height")
                }

                override fun surfaceDestroyed(holder: SurfaceHolder) {
                    mediaPlayer.setSurface(null)
                    Log.e(TAG, "surfaceDestroyed: ")
                }
            })
            val layout = FrameLayout(context)
            layout.addView(surfaceView, LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT))
            addView(layout, LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT))
            Log.e(TAG, "changeVideoView: surfaceView add")
        } else {
            removeView(videoView)
            val textureView = TextureView(context)
            videoView = textureView
            textureView.surfaceTextureListener = object : TextureView.SurfaceTextureListener {
                override fun onSurfaceTextureAvailable(surface: SurfaceTexture, width: Int, height: Int) {
                    mediaPlayer.setSurface(Surface(surface))
                    Log.e(TAG, "onSurfaceTextureAvailable: $width, $height")
                }

                override fun onSurfaceTextureSizeChanged(surface: SurfaceTexture, width: Int, height: Int) {
                    Log.e(TAG, "onSurfaceTextureSizeChanged: $width, $height")
                }

                override fun onSurfaceTextureDestroyed(surface: SurfaceTexture): Boolean {
                    Log.e(TAG, "onSurfaceTextureDestroyed: ")
                    return true
                }

                override fun onSurfaceTextureUpdated(surface: SurfaceTexture) {
                }
            }
            val layout = FrameLayout(context)
            layout.addView(textureView, LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT))
            addView(layout, LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT))
            Log.e(TAG, "changeVideoView: textureView add")
        }
    }

    fun interface ISeekCallback {
        fun onSeek(percent: Float)
    }

    companion object {
        private const val TAG = "PlayerView"
    }
}