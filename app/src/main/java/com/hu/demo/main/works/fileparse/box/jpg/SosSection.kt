package com.hu.demo.main.works.fileparse.box.jpg

import com.hu.demo.main.works.fileparse.IRead
import com.hu.demo.main.works.fileparse.box.Tree
import com.hu.demo.utils.ByteOrderedDataInputStream
import com.hu.demo.utils.beautyToString
import com.hu.demo.utils.contentHexString
import com.hu.demo.utils.getLow
import com.hu.demo.utils.getSenior
import com.hu.demo.utils.toHexString

internal class SosSection(parent: Tree) : SizeSection(parent) {
    private var componentSize: Byte? = null
    private var components: Array<HuffmanEntry>? = null
    private var remain: ByteArray? = null
    private var dataSize: Long = 0

    override fun read(bis: ByteOrderedDataInputStream) {
        super.read(bis)
        componentSize = bis.nReadByte()
        components = Array(componentSize!!.toInt()) {
            HuffmanEntry().apply { read(bis) }
        }
        remain = bis.nReadBytes(3)

        val lastSize = alreadyReadSize
        while (bis.nReadByte() != MARKER || bis.nReadByte() != MARKER_EOI) {; }
        dataSize = alreadyReadSize - lastSize
    }

    override fun toString(): String {
        return "${super.toString()} SOS\ncomponentSize:$componentSize, " +
                "components:${components?.beautyToString()}, \nremain:${remain?.contentHexString()}, \ncompressSize:${dataSize.toHexString()}"
    }

    inner class HuffmanEntry() : IRead {
        private var componentId: Byte? = null
        private val comStr: String
            get() {
                return when (componentId?.toInt()) {
                    1 -> "(Y)"
                    2 -> "(Cb)"
                    3 -> "(Cr)"
                    else -> ""
                }
            }

        private var huffmanNum: Byte? = null

        private val tableStr: String?
            get() = huffmanNum?.toHexString()
        private val acNum: Byte?
            get() = huffmanNum?.getLow()
        private val dcNum: Byte?
            get() = huffmanNum?.getSenior()

        override fun read(bis: ByteOrderedDataInputStream) {
            componentId = bis.nReadByte()
            huffmanNum = bis.nReadByte()
        }

        override fun toString(): String {
            return "(componentId:${componentId?.toHexString()}$comStr, num:$tableStr(AC:${acNum?.toHexString()}, DC:${dcNum?.toHexString()})"
        }
    }

    companion object {
        private const val MARKER = 0xFF.toByte()
        private const val MARKER_EOI = 0xD9.toByte()
    }
}
