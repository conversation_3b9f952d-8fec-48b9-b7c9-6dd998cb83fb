/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - RendererGroup.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/08/24
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery		2024/08/24		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.hu.demo.main.works.gl.renderer.normal

import com.hu.demo.main.works.gl.renderer.param.NodePath
import com.hu.demo.main.works.gl.renderer.param.RenderArgs
import com.hu.demo.main.works.gl.shader.ProgramShader
import com.hu.demo.main.works.gl.utils.GlProgram
import com.hu.demo.main.works.gl.utils.ShaderParam
import kotlinx.coroutines.NonCancellable.children
import java.util.LinkedList

/**
 * 渲染器组
 *
 * @param rendererName 渲染器的名称，默认以TAG为名称
 */
open class RendererGroup(rendererName: String) : Renderer(rendererName) {
    /**
     * 子节点集
     */
    internal val children: LinkedList<Renderer> = LinkedList()

    /**
     * shader程序
     */
    override val programShader: ProgramShader = ProgramShader()

    override val handleParams: Array<ShaderParam> = emptyArray()

    /**
     * 合并shader。合并孩子节点的shader
     *
     * @param shader 父shader
     *
     * @return 返回合并后的shader
     */
    private fun combineShader(shader: ProgramShader?): ProgramShader? {
        shader ?: return null
        for (child in children) {
            shader.combine(child.programShader)
        }
        return shader
    }

    /**
     * 编译安装shsder。
     *
     * @param shader 需要安装的shder程序。如果为null，则使用当前[RendererGroup]的[programShader]
     *
     * @return 返回gl program程序
     */
    override fun install(shader: ProgramShader?): GlProgram? {
        combineShader(shader)
        val glProgram = super.install(shader)
        children.forEach {
            it.install(shader)
        }
        return glProgram
    }

    override fun render(renderArgs: RenderArgs, glProgram: GlProgram?) {
        children.forEach {
            it.performRender(renderArgs, glProgram)
        }
    }

    /**
     * 使用指定[nodePath]的渲染器路径，获取对应的[Renderer]渲染器。
     *
     * 如果[nodePath]的顶层节点[NodePath.top]可以在[children]中找到，则标记为发现，反之为未发现，直接返回`null`。
     * - 如果当前[nodePath]只有一个节点，则表示发现，返回结果。
     * - 如果当前[nodePath]不是一个节点，则需要通过[children]继续查找
     *
     * @param nodePath 要查找的渲染器路径，此路径的[NodePath.top]需是[children]中任意一个[Renderer]的[Renderer.name]
     *
     * @return 返回发现的节点[Renderer]
     */
    fun get(nodePath: NodePath): Renderer? {
        val nodeName = nodePath.top()
        val find = children.find { it.name == nodeName }
        if (find == null) {
            return null
        }
        val path = nodePath.minusTop()
        return if (path == null) {
            find
        } else {
            (find as? RendererGroup)?.get(path)
        }
    }

    /**
     * 替换子渲染器，可指定渲染器的[index]位置
     *
     * @param renderer 需要添加的子节点
     * @param index 需要添加的位置，如果没有指定，模式是[children]的末尾
     */
    fun setChild(renderer: Renderer, index: Int) {
        children[index] = renderer
    }

    /**
     * 添加子渲染器，可指定渲染器的[index]位置
     *
     * @param renderer 需要添加的子节点
     * @param index 需要添加的位置，如果没有指定，模式是[children]的末尾
     */
    fun addChild(renderer: Renderer, index: Int? = null) {
        children.add(index ?: children.size, renderer)
    }

    /**
     * 从当前group中移除指定渲染器
     *
     * @param renderer 节点
     */
    fun removeChild(renderer: Renderer) {
        children.remove(renderer)
    }

    /**
     * 通过给定的[nodePath]，在[RendererGroup]的树中寻找并将[renderer]添加到[nodePath]下
     *
     * @param nodePath 需要添加到[nodePath]下，做为子节点
     * @param renderer 需要添加的节点
     * @param index 节点在[nodePath]的children中第几个位置，如果指定null或不指定则表示添加的末尾
     */
    fun deepAddChild(nodePath: NodePath, renderer: Renderer, index: Int? = null) {
        (get(nodePath) as? RendererGroup)?.addChild(renderer, index)
    }

    /**
     * 删除指定节点
     *
     * @param nodePath 要删除的节点的路径
     */
    fun deepRemoveChild(nodePath: NodePath) {
        get(nodePath)?.apply { this.parent?.removeChild(this) }
    }

    /**
     * 删除所有孩子节点
     */
    fun removeChildren() {
        children.clear()
    }

    companion object {
        private const val TAG = "RendererGroup"
    }
}