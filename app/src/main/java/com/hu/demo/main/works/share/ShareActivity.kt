package com.hu.demo.main.works.share

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Button
import android.widget.ImageView
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.contract.ActivityResultContract
import com.hu.demo.main.R
import com.hu.demo.utils.setImageUriAny
import com.hu.demo.base.ui.BaseActivity

class ShareActivity : BaseActivity(), View.OnClickListener {
    private var selectUri: Uri? = null
    private var ivImage: ImageView? = null
    private var btnSelectImg: Button? = null
    private var btnShare: Button? = null
    private val pickTypeResultContract = PickTypeResultContract()
    private val pickImageLauncher = registerForActivityResult(
        pickTypeResultContract,
        PickOnlyCallback { changeData(it) }
    )

    override fun getLayoutId(): Int {
        return R.layout.activity_share
    }

    override fun initToolbar() {
        super.initToolbar()
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowTitleEnabled(true)
            setDisplayShowHomeEnabled(false)
        }
    }

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        ivImage = findViewById(R.id.ivImage)
        btnSelectImg = findViewById(R.id.btnSelectImg)
        btnShare = findViewById(R.id.btnShare)
    }

    override fun initEvent(savedInstanceState: Bundle?) {
        super.initEvent(savedInstanceState)
        btnSelectImg?.setOnClickListener(this)
        btnShare?.setOnClickListener(this)
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.btnSelectImg -> {
                pickImageLauncher.launch(MIME_TYPE)
            }
            R.id.btnShare -> {
                startActivity(Intent(SHARE_ACTION).setDataAndType(selectUri, MIME_TYPE))
            }
        }
    }

    private fun changeData(obj: Any?) {
        ivImage?.also {
            this.selectUri = obj as Uri
            it.setImageUriAny(obj)
            btnShare?.isEnabled = true
        }
    }

    private open class PickOnlyCallback(private val resultFunc: (Uri) -> Unit) : ActivityResultCallback<Uri?> {
        override fun onActivityResult(result: Uri?) {
            result ?: run {
                Log.d(TAG, "PickOnlyCallback.onActivityResult: ")
                return
            }
            Log.d(TAG, "onActivityResult: $result")
            resultFunc(result)
        }
    }

    private class PickTypeResultContract : ActivityResultContract<String, Uri?>() {
        override fun createIntent(context: Context, input: String): Intent {
            return Intent(Intent.ACTION_PICK).setType(input)
        }

        override fun parseResult(resultCode: Int, intent: Intent?): Uri? {
            return if (resultCode != Activity.RESULT_OK) null else intent?.data
        }
    }

    companion object {
        private const val TAG = "ShareActivity"
        private const val SHARE_ACTION = "oplus.intent.action.GALLERY_SHARE"
        private const val MIME_TYPE = "image/*"
    }
}