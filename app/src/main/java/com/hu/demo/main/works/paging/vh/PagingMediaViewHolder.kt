package com.hu.demo.main.works.paging.vh

import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import com.bumptech.glide.Glide
import com.hu.demo.main.databinding.ItemPagingMediaBinding
import com.hu.demo.main.works.paging.data.MediaData

class PagingMediaViewHolder(private val binding: ItemPagingMediaBinding) : BaseViewHolder(binding.root) {

    override fun bind(mediaData: MediaData) {
        binding.tvTitle.text = mediaData.displayName
        binding.tvMediaId.text = mediaData.id.toString()
        binding.tvDatetaken.text = mediaData.dateTaken
        Log.d(TAG, "bind: $absoluteAdapterPosition, ${itemView.width}")
        Glide.with(binding.root).load(mediaData.uri).centerCrop().into(binding.ivImage)
    }

    companion object {
        private const val TAG = "MediaViewHolder"
        fun create(parent: ViewGroup): PagingMediaViewHolder {
            return PagingMediaViewHolder(
                ItemPagingMediaBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            )
        }
    }
}