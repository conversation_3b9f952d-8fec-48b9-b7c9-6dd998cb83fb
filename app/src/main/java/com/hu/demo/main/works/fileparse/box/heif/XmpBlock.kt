package com.hu.demo.main.works.fileparse.box.heif

import com.hu.demo.main.works.fileparse.IRead
import com.hu.demo.main.works.fileparse.box.Tree
import com.hu.demo.utils.ByteOrderedDataInputStream
import okio.ByteString.Companion.readByteString

internal class XmpBlock(private val ilocItem: ItemLocationBox.Item, level: Int, parent: Tree?) : Tree("exif", level, parent), IRead {
    override val realSize: Long
        get() = ilocItem.extentItem?.get(0)?.extentLength?.toLong() ?: 0L
    var sb: StringBuilder? = null
    override fun read(bis: ByteOrderedDataInputStream) {
        sb = StringBuilder()
        sb?.append(bis.readByteString(realSize.toInt()).string(Charsets.US_ASCII))
    }

    override fun toString(): String {
        return "$sb"
    }
}
