/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IccProfile
 ** Description: 用于写iccProfile的java接口，该包名需要用这个固定的包名，不能修改
 **              ICC色彩特性文件（ICC Profile）是一组用来描述色彩输入、输出设备或者某种色彩空间的特性的数据集合
 **              该类文件被广泛用于色彩管理，以实现让颜色在设备和文档之间保持一致，从而在目标设备上提供最佳的色彩表现、或者在其他设备上模拟文档在目标设备上的色彩表现。
 ** Version: 1.0
 ** Date : 2023/4/7
 ** Author: youpeng@Apps.Gallery3D
 ** OPLUS Java File Skip Rule:PackageName
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  youpeng@Apps.Gallery3D      2023/04/07    1.0         first created
 ********************************************************************************/
package com.oplus.camera.jni;

import android.graphics.Bitmap;

public class IccProfile {

    public static final int WRITE_DISPLAY_ICC_PROFILE_SUCCESS = 1;

    public static final int SUCCESS = 0;

    static {
        System.loadLibrary("IccProfileJniForGallery");
    }

    public static native void writeHeifIccProfile(int fd);

    public static native void writeJpegIccProfile(int fd);

    public static native byte[] convertYuv2JpegWithColorProfile(byte[] inputBuffer, int quality, int width, int height);

    public static native byte[] compressBitmap(Bitmap srcBitmap, int quality);

    /**
     * 图片保存，将bitmap按照指定的quality压缩到目标文件中
     * 不支持UHDR图片的保存，此接口不涉及Gainmap保存逻辑
     * @param srcBitmap 需要处理的图片
     * @param quality 图片质量
     * @param destFile 目标文件
     * @return 值为1代表成功，否则为失败
     */
    public static native int compressBitmapToFile(Bitmap srcBitmap, int quality, String destFile);

    /**
     * UHDR图片保存
     * 除保存主图外，还包括了Gainmap保存逻辑
     * @param srcBitmap 需要处理的图片
     * @param quality 图片质量
     * @param scale  hdr提亮值
     * @param destFile 目标文件
     * @return 值为0代表成功，否则为失败
     */
    public static native int compressBitmapToFileForJpegr(Bitmap srcBitmap, int quality, float scale, String destFile);
}