<?xml version="1.0" encoding="utf-8"?>
<manifest
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.VIBRATE" />

    <uses-feature
        android:glEsVersion="0x00020000"
        android:required="true" />

    <permission
        android:name="DatabaseProvider._READ_PERMISSION"
        android:protectionLevel="normal" />

    <application
        android:name="com.hu.demo.base.app.App"
        android:allowBackup="false"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:manageSpaceActivity="com.hu.demo.main.works.cachemanage.CacheManageActivity"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.HuDemo.NoActionBar">
        <provider
            android:name="com.hu.demo.main.InfoProvider"
            android:authorities="com.hu.demo.infoProvider"
            android:enabled="true"
            android:exported="true"
            tools:ignore="ExportedContentProvider" />

        <activity
            android:name="com.hu.demo.main.works.main.KaActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.hu.demo.main.works.drawable.GridDrawableActivity"
            android:exported="true"
            android:label="GridDrawable">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="demo" />
                <data android:host="hu.com" />
                <data android:path="/GridDrawableActivity" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.hu.demo.main.works.notification.NotificationActivity"
            android:exported="true"
            android:label="通知测试">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="demo" />
                <data android:host="hu.com" />
                <data android:path="/NotificationActivity" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.hu.demo.main.works.recyclerview.RecyclerActivity"
            android:exported="true"
            android:label="RecyclerView测试">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="demo" />
                <data android:host="hu.com" />
                <data android:path="/RecyclerActivity" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.hu.demo.main.works.select.PickActivity"
            android:exported="true"
            android:label="选图测试">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="demo" />
                <data android:host="hu.com" />
                <data android:path="/PickActivity" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.hu.demo.main.works.livedata.LiveDataActivity"
            android:exported="true"

            android:label="LiveData测试">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="demo" />
                <data android:host="hu.com" />
                <data android:path="/LiveDataActivity" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.hu.demo.main.works.flow.FlowActivity"

            android:exported="true"
            android:label="Flow测试">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="demo" />
                <data android:host="hu.com" />
                <data android:path="/FlowActivity" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.hu.demo.main.works.share.ShareActivity"

            android:exported="true"
            android:label="Share测试">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="demo" />
                <data android:host="hu.com" />
                <data android:path="/ShareActivity" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.hu.demo.main.works.edgeeffect.EdgeEffectActivity"
            android:exported="true"
            android:label="边缘效果测试">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="demo" />
                <data android:host="hu.com" />
                <data android:path="/EdgeEffectActivity" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.hu.demo.main.works.clip.ClipboardActivity"

            android:exported="true"
            android:label="剪贴板测试">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="demo" />
                <data android:host="hu.com" />
                <data android:path="/ClipboardActivity" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.hu.demo.main.works.drag.DragActivity"

            android:exported="true"
            android:label="Drag测试">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="demo" />
                <data android:host="hu.com" />
                <data android:path="/DragActivity" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.hu.demo.main.works.uhdr.UhdrActivity"
            android:exported="true"
            android:label="UHDR测试"
            tools:targetApi="34">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="demo" />
                <data android:host="hu.com" />
                <data android:path="/UhdrActivity" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.hu.demo.main.works.paging.PagingActivity"

            android:exported="true"
            android:label="Paging测试">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="demo" />
                <data android:host="hu.com" />
                <data android:path="/PagingActivity" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.hu.demo.main.works.decode.DecodeImageActivity"
            android:exported="true"

            android:label="图片解码测试">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="demo" />
                <data android:host="hu.com" />
                <data android:path="/DecodeImageActivity" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.hu.demo.main.works.decode.DecodeVideoActivity"
            android:exported="true"

            android:label="视频解码测试">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="demo" />
                <data android:host="hu.com" />
                <data android:path="/DecodeVideoActivity" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.hu.demo.main.works.exif.ExifActivity"
            android:exported="true"
            android:label="Exif测试">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="demo" />
                <data android:host="hu.com" />
                <data android:path="/ExifActivity" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.hu.demo.main.works.sensor.SensorActivity"
            android:exported="true"

            android:label="Sensor测试">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="demo" />
                <data android:host="hu.com" />
                <data android:path="/SensorActivity" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.hu.demo.main.works.exifimage.ExifImageActivity"
            android:exported="true"

            android:label="Exif Image测试">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="demo" />
                <data android:host="hu.com" />
                <data android:path="/ExifImageActivity" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.hu.demo.main.works.fileparse.FileParseActivity"
            android:exported="true"
            android:label="File Parse 测试">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="demo" />
                <data android:host="hu.com" />
                <data android:path="/FileParseActivity" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.hu.demo.main.works.imagesize.ImageSizeActivity"
            android:exported="true"

            android:label="Image Size 测试">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="demo" />
                <data android:host="hu.com" />
                <data android:path="/ImageSizeActivity" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.hu.demo.main.works.videoplayer.VideoPlayerActivity"
            android:configChanges="orientation"
            android:exported="true"

            android:label="视频播放器测试">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="demo" />
                <data android:host="hu.com" />
                <data android:path="/VideoPlayerActivity" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.hu.demo.main.works.cachemanage.CacheManageActivity"
            android:configChanges="orientation"
            android:exported="true"

            android:label="缓存管理测试">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="demo" />
                <data android:host="hu.com" />
                <data android:path="/CacheManageActivity" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.hu.demo.main.works.pdf.PdfActivity"
            android:configChanges="orientation"
            android:exported="true"

            android:label="PDF测试">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="demo" />
                <data android:host="hu.com" />
                <data android:path="/PdfActivity" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.hu.demo.main.works.gl.GlActivity"
            android:configChanges="orientation"
            android:exported="true"
            android:label="GL测试"
            tools:targetApi="upside_down_cake">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="demo" />
                <data android:host="hu.com" />
                <data android:path="/GlActivity" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.hu.demo.main.works.mix.MixActivity"
            android:configChanges="orientation"
            android:exported="true"
            android:label="MIX测试"
            tools:targetApi="upside_down_cake">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="demo" />
                <data android:host="hu.com" />
                <data android:path="/MixActivity" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.hu.demo.main.works.lut.LutActivity"
            android:configChanges="orientation"
            android:exported="true"
            android:label="LUT测试"
            tools:targetApi="upside_down_cake">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="demo" />
                <data android:host="hu.com" />
                <data android:path="/LutActivity" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.hu.demo.main.works.canvas.CanvasActivity"
            android:configChanges="orientation"
            android:exported="true"
            android:label="Canvas测试"
            tools:targetApi="upside_down_cake">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="demo" />
                <data android:host="hu.com" />
                <data android:path="/CanvasActivity" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.hu.demo.main.works.color.ColorActivity"
            android:configChanges="orientation"
            android:exported="true"
            android:label="Color测试"
            tools:targetApi="upside_down_cake">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="demo" />
                <data android:host="hu.com" />
                <data android:path="/ColorActivity" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.hu.demo.main.works.videoinfo.VideoInfoActivity"
            android:configChanges="orientation"
            android:exported="true"
            android:label="视频信息测试">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="demo" />
                <data android:host="hu.com" />
                <data android:path="/VideoInfoActivity" />
            </intent-filter>
        </activity>

        <provider
            android:name="com.hu.demo.base.provider.DFileProvider"
            android:authorities="com.hu.demo.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <receiver
            android:name="com.hu.demo.main.ActionReceiver"
            android:exported="true"
            tools:ignore="ExportedReceiver">
            <intent-filter>
                <action android:name="com.hu.demo.splitUhdr" />
            </intent-filter>
        </receiver>
    </application>

</manifest>