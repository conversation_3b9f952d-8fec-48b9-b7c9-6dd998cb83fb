<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".works.livedata.LiveDataActivity">

    <include
        layout="@layout/layout_toolbar"
        app:layout_constraintTop_toTopOf="parent" />

    <ScrollView
        android:id="@+id/svScroll"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/btnEmit1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar">

        <TextView
            android:id="@+id/tvText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </ScrollView>

    <Button
        android:id="@+id/btnEmit1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="发送1"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/btnEmit2"
        app:layout_constraintStart_toStartOf="parent" />

    <Button
        android:id="@+id/btnEmit2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="发送2"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/btnEmit1" />


</androidx.constraintlayout.widget.ConstraintLayout>