<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".works.notification.NotificationActivity">

    <include
        layout="@layout/layout_toolbar"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvChanelName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Chanel名称"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar" />

    <EditText
        android:id="@+id/etText"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="测试通道"
        app:layout_constraintBottom_toBottomOf="@id/tvChanelName"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvChanelName"
        app:layout_constraintTop_toTopOf="@+id/tvChanelName" />

    <Button
        android:id="@+id/btnSend"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="发送通知"
        app:layout_constraintEnd_toStartOf="@+id/btnClearChanelNotification"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/etText" />

    <Button
        android:id="@+id/btnClearChanelNotification"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="清理同Chanel的通知"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/btnSend"
        app:layout_constraintTop_toBottomOf="@+id/etText" />

</androidx.constraintlayout.widget.ConstraintLayout>