<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".works.share.ShareActivity">

    <include
        layout="@layout/layout_toolbar"
        app:layout_constraintTop_toTopOf="parent" />

    <com.hu.demo.main.works.gl.EditingSurfaceView
        android:id="@+id/glView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/sbSeek"
        app:layout_constraintTop_toBottomOf="@+id/toolbar" />

    <SeekBar
        android:id="@+id/sbSeek"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:max="100"
        android:progress="50"
        app:layout_constraintBottom_toTopOf="@id/btnSelectImg"
        app:layout_constraintEnd_toStartOf="@id/btnSelectVideo"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/glView" />

    <Button
        android:id="@+id/btnSelectImg1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="图片1"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/btnSelectImg2"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/sbSeek" />

    <Button
        android:id="@+id/btnSelectImg2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="图片2"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/btnSelectImg1"
        app:layout_constraintTop_toBottomOf="@id/sbSeek" />

</androidx.constraintlayout.widget.ConstraintLayout>