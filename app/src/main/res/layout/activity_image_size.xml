<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".works.share.ShareActivity">

    <include
        layout="@layout/layout_toolbar"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ivImage"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/green_light"
        android:src="@drawable/ic_add"
        app:layout_constraintBottom_toTopOf="@+id/tvViewWidthTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar" />

    <TextView
        android:id="@+id/tvViewWidthTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginVertical="24dp"
        android:text="View 宽"
        app:layout_constraintBottom_toTopOf="@+id/tvBitmapMaxWh"
        app:layout_constraintStart_toStartOf="parent" />

    <EditText
        android:id="@+id/etViewWidth"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginVertical="8dp"
        android:ems="5"
        android:inputType="numberSigned"
        android:text="-1"
        app:layout_constraintBottom_toBottomOf="@+id/tvViewWidthTitle"
        app:layout_constraintEnd_toStartOf="@+id/tvViewHeightTitle"
        app:layout_constraintStart_toEndOf="@id/tvViewWidthTitle"
        app:layout_constraintTop_toTopOf="@+id/tvViewWidthTitle" />

    <TextView
        android:id="@+id/tvViewHeightTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="8dp"
        android:text="View 高"
        app:layout_constraintBottom_toBottomOf="@id/etViewWidth"
        app:layout_constraintEnd_toStartOf="@+id/etViewHeight"
        app:layout_constraintStart_toEndOf="@+id/etViewWidth"
        app:layout_constraintTop_toTopOf="@id/etViewWidth" />

    <EditText
        android:id="@+id/etViewHeight"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginVertical="8dp"
        android:ems="5"
        android:inputType="numberSigned"
        android:text="0"
        app:layout_constraintBottom_toBottomOf="@+id/tvViewHeightTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvViewHeightTitle"
        app:layout_constraintTop_toTopOf="@id/tvViewHeightTitle" />

    <TextView
        android:id="@+id/tvBitmapMaxWh"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginVertical="20dp"
        android:text="Bitmap最大(px)"
        android:ems="6"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <SeekBar
        android:id="@+id/sbMaxWh"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:max="4000"
        android:min="1"
        android:progress="300"
        app:layout_constraintBottom_toBottomOf="@id/tvBitmapMaxWh"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvBitmapMaxWh"
        app:layout_constraintTop_toTopOf="@+id/tvBitmapMaxWh" />


</androidx.constraintlayout.widget.ConstraintLayout>