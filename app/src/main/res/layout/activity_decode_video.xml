<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".works.decode.DecodeVideoActivity">

    <include
        layout="@layout/layout_toolbar"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ivImage"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_margin="8dp"
        android:src="@drawable/ic_add"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar" />

    <TextView
        android:id="@+id/tvMaxCount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="8dp"
        android:text="解码次数"
        app:layout_constraintBottom_toBottomOf="@id/etMaxCount"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/etMaxCount" />

    <EditText
        android:id="@+id/etMaxCount"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginVertical="8dp"
        android:inputType="number"
        android:text="45"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/tvInterval"
        app:layout_constraintStart_toEndOf="@id/tvMaxCount"
        app:layout_constraintTop_toBottomOf="@id/ivImage" />

    <TextView
        android:id="@+id/tvInterval"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="8dp"
        android:text="间隔(ms)"
        app:layout_constraintBottom_toBottomOf="@id/etMaxCount"
        app:layout_constraintEnd_toStartOf="@+id/etInterval"
        app:layout_constraintStart_toEndOf="@id/etMaxCount"
        app:layout_constraintTop_toTopOf="@id/etMaxCount" />

    <EditText
        android:id="@+id/etInterval"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginVertical="8dp"
        android:inputType="number"
        android:text="100"
        app:layout_constraintStart_toEndOf="@id/tvInterval"
        app:layout_constraintBottom_toBottomOf="@+id/tvInterval"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvInterval" />

    <Spinner
        android:id="@+id/spinner_method"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@color/yellow_light"
        android:entries="@array/video_decode_method"
        android:textSize="6sp"
        app:layout_constraintEnd_toStartOf="@+id/spinner_option"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/etInterval" />

    <Spinner
        android:id="@+id/spinner_option"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@color/yellow_light"
        android:entries="@array/video_decode_option"
        android:textSize="6sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/spinner_method"
        app:layout_constraintTop_toBottomOf="@+id/etInterval" />

    <Spinner
        android:id="@+id/spinner_retriever"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@color/yellow_light"
        android:entries="@array/video_decode_retriever"
        android:textSize="6sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/spinner_method" />

    <androidx.appcompat.widget.SwitchCompat
        android:id="@+id/switchLog"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:checked="true"
        android:text="日志开关"
        app:layout_constraintBottom_toBottomOf="@id/btnDecode"
        app:layout_constraintEnd_toStartOf="@id/btnDecode"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/btnDecode" />

    <Button
        android:id="@+id/btnDecode"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:maxLength="4"
        android:maxLines="1"
        android:text="解码"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/switchLog"
        app:layout_constraintTop_toBottomOf="@id/spinner_retriever" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvRecycler"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btnDecode" />

</androidx.constraintlayout.widget.ConstraintLayout>