<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".works.share.ShareActivity">

    <include
        layout="@layout/layout_toolbar"
        app:layout_constraintTop_toTopOf="parent"/>

    <com.hu.demo.main.works.gl.EditingSurfaceView
        android:id="@+id/glView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/sbDither"
        app:layout_constraintTop_toBottomOf="@+id/toolbar"/>

    <TextView
        android:layout_width="wrap_content"
        android:id="@+id/tvDither"
        android:text="dither"
        android:ems="3"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/sbDither"
        app:layout_constraintTop_toTopOf="@+id/sbDither"
        app:layout_constraintBottom_toBottomOf="@+id/sbDither"
        android:layout_height="wrap_content"/>

    <SeekBar
        android:id="@+id/sbDither"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:max="100"
        android:progress="50"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@id/sbVig"
        app:layout_constraintStart_toEndOf="@+id/tvDither"/>

    <TextView
        android:layout_width="wrap_content"
        android:id="@+id/tvVig"
        android:text="vig"
        android:ems="3"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/sbVig"
        app:layout_constraintTop_toTopOf="@+id/sbVig"
        app:layout_constraintBottom_toBottomOf="@+id/sbVig"
        android:layout_height="wrap_content"/>

    <SeekBar
        android:id="@+id/sbVig"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:max="100"
        android:progress="50"
        app:layout_constraintStart_toEndOf="@+id/tvVig"
        app:layout_constraintBottom_toTopOf="@id/btnSelectImg"
        app:layout_constraintEnd_toEndOf="parent"/>

    <Button
        android:id="@+id/btnSelectImg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="图片"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/sbSeek"/>

</androidx.constraintlayout.widget.ConstraintLayout>