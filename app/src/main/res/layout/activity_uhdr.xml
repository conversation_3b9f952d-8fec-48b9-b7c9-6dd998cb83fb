<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".works.uhdr.UhdrActivity">

    <include
        layout="@layout/layout_toolbar"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvSdrHdrRatio"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/purple_200"
        app:layout_constraintTop_toBottomOf="@+id/toolbar" />

    <com.hu.demo.main.works.uhdr.ColorGridView
        android:id="@+id/cgGrid"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        app:layout_constraintTop_toBottomOf="@+id/tvSdrHdrRatio" />

    <Button
        android:id="@+id/btnSelect"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="选图"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cgGrid" />

    <Spinner
        android:id="@+id/spMode"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginVertical="10dp"
        android:entries="@array/uhdr_color_mode"
        app:layout_constraintBottom_toBottomOf="@id/btnSelect"
        app:layout_constraintStart_toEndOf="@id/btnSelect"
        app:layout_constraintTop_toTopOf="@id/btnSelect" />


    <Spinner
        android:id="@+id/spDrawMode"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:entries="@array/colormode_draw_mode"
        app:layout_constraintBottom_toBottomOf="@id/spMode"
        app:layout_constraintStart_toEndOf="@id/spMode"
        app:layout_constraintTop_toTopOf="@id/spMode" />

    <com.hu.demo.main.works.uhdr.ImageShowView
        android:id="@+id/ivImage"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btnSelect" />

    <Button
        android:id="@+id/btnSelectGain"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="增益"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@id/ivImage"
        app:layout_constraintTop_toTopOf="@id/ivImage" />

</androidx.constraintlayout.widget.ConstraintLayout>