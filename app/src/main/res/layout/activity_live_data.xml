<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".works.livedata.LiveDataActivity">

    <include
        layout="@layout/layout_toolbar"
        app:layout_constraintTop_toTopOf="parent" />

    <ScrollView
        android:id="@+id/svScroll"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/btnUpdate"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar">

        <TextView
            android:id="@+id/tvText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </ScrollView>

    <Button
        android:id="@+id/btnCreateNoData"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="创建无初值"
        app:layout_constraintBottom_toTopOf="@+id/btnCreateNoDataAndUpdate"
        app:layout_constraintEnd_toStartOf="@id/btnCreateHaveData"
        app:layout_constraintStart_toStartOf="parent" />

    <Button
        android:id="@+id/btnCreateHaveData"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="创建有初值"
        app:layout_constraintBottom_toTopOf="@+id/btnCreateHaveDataAndUpdate"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/btnCreateNoData" />

    <Button
        android:id="@+id/btnCreateNoDataAndUpdate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="创建无初值并观察前更新"
        app:layout_constraintBottom_toTopOf="@+id/btnUpdate"
        app:layout_constraintEnd_toStartOf="@+id/btnCreateHaveDataAndUpdate"
        app:layout_constraintStart_toStartOf="parent" />

    <Button
        android:id="@+id/btnCreateHaveDataAndUpdate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="创建有初值并观察前更新"
        app:layout_constraintBottom_toTopOf="@+id/btnClear"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/btnCreateNoDataAndUpdate" />

    <Button
        android:id="@+id/btnUpdate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="更新"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/btnClear"
        app:layout_constraintStart_toStartOf="parent" />

    <Button
        android:id="@+id/btnClear"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="清除"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/btnUpdate" />

</androidx.constraintlayout.widget.ConstraintLayout>