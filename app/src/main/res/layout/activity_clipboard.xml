<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".works.clip.ClipboardActivity">

    <include
        layout="@layout/layout_toolbar"
        app:layout_constraintTop_toTopOf="parent" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvImages"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                app:layout_constraintTop_toTopOf="parent" />

            <Button
                android:id="@+id/btnSelect"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="选择照片"
                app:layout_constraintEnd_toStartOf="@+id/btnCopyToClipboard"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/rvImages" />

            <Button
                android:id="@+id/btnCopyToClipboard"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:enabled="false"
                android:text="复制到剪贴板"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/btnSelect"
                app:layout_constraintTop_toBottomOf="@+id/rvImages" />


            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvParseImages"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                app:layout_constraintBottom_toTopOf="@id/btnPaste"
                app:layout_constraintTop_toBottomOf="@id/btnCopyToClipboard" />


            <Button
                android:id="@+id/btnPaste"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="粘贴"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/btnSaveToFile"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/rvParseImages" />

            <Button
                android:id="@+id/btnSaveToFile"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:enabled="false"
                android:text="保存成文件"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/btnPaste"
                app:layout_constraintTop_toBottomOf="@id/rvParseImages" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>


</androidx.constraintlayout.widget.ConstraintLayout>