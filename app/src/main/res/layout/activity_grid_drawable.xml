<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".works.drawable.GridDrawableActivity">

    <include
        layout="@layout/layout_toolbar"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ivGridImage"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginLeft="24dp"
        android:layout_marginTop="8dp"
        android:layout_marginRight="24dp"
        android:scaleType="centerCrop"
        app:layout_constraintDimensionRatio="w,1:1"
        app:layout_constraintEnd_toStartOf="@+id/ivImage"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar" />

    <ImageView
        android:id="@+id/ivImage"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginLeft="24dp"
        android:layout_marginRight="24dp"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="@+id/ivGridImage"
        app:layout_constraintDimensionRatio="w,1:1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ivGridImage"
        app:layout_constraintTop_toTopOf="@id/ivGridImage" />

    <ImageView
        android:id="@+id/ivImageCenterCrop"
        android:layout_width="0dp"
        android:layout_height="300dp"
        android:layout_marginLeft="8dp"
        android:layout_marginTop="8dp"
        android:layout_marginRight="8dp"
        android:scaleType="centerCrop"
        app:layout_constraintEnd_toStartOf="@id/ivImageCenterInside"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivGridImage" />

    <ImageView
        android:id="@+id/ivImageCenterInside"
        android:layout_width="0dp"
        android:layout_height="300dp"
        android:layout_marginLeft="8dp"
        android:layout_marginTop="8dp"
        android:layout_marginRight="8dp"
        android:scaleType="centerCrop"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/ivImageCenterCrop"
        app:layout_constraintTop_toBottomOf="@id/ivImage" />

    <TextView
        android:id="@+id/tvLineWidthTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="线宽："
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivImageCenterCrop" />

    <TextView
        android:id="@+id/tvLineWidth"
        android:layout_width="30dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@id/tvLineWidthTitle"
        app:layout_constraintStart_toEndOf="@id/tvLineWidthTitle"
        app:layout_constraintTop_toTopOf="@id/tvLineWidthTitle" />

    <SeekBar
        android:id="@+id/sbLineWidth"
        style="@style/Widget.AppCompat.SeekBar.Discrete"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:max="99"
        android:progress="0"
        app:layout_constraintBottom_toBottomOf="@+id/tvLineWidth"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvLineWidth"
        app:layout_constraintTop_toTopOf="@id/tvLineWidth" />

    <TextView
        android:id="@+id/tvLineRadiusTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="角度："
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvLineWidthTitle" />

    <TextView
        android:id="@+id/tvLineRadius"
        android:layout_width="30dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@id/tvLineRadiusTitle"
        app:layout_constraintStart_toEndOf="@id/tvLineRadiusTitle"
        app:layout_constraintTop_toTopOf="@id/tvLineRadiusTitle" />

    <SeekBar
        android:id="@+id/sbLineRadius"
        style="@style/Widget.AppCompat.SeekBar.Discrete"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:max="200"
        android:progress="0"
        app:layout_constraintBottom_toBottomOf="@+id/tvLineRadius"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvLineRadius"
        app:layout_constraintTop_toTopOf="@id/tvLineRadius" />

    <TextView
        android:id="@+id/tvEdgeTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="边距："
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvLineRadiusTitle" />

    <TextView
        android:id="@+id/tvEdge"
        android:layout_width="30dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@id/tvEdgeTitle"
        app:layout_constraintStart_toEndOf="@id/tvEdgeTitle"
        app:layout_constraintTop_toTopOf="@id/tvEdgeTitle" />

    <SeekBar
        android:id="@+id/sbEdge"
        style="@style/Widget.AppCompat.SeekBar.Discrete"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:max="99"
        android:progress="0"
        app:layout_constraintBottom_toBottomOf="@+id/tvEdge"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvEdge"
        app:layout_constraintTop_toTopOf="@id/tvEdge" />

    <TextView
        android:id="@+id/tvGapTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="间距："
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvEdge" />

    <TextView
        android:id="@+id/tvGap"
        android:layout_width="30dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@id/tvGapTitle"
        app:layout_constraintStart_toEndOf="@id/tvGapTitle"
        app:layout_constraintTop_toTopOf="@id/tvGapTitle" />

    <SeekBar
        android:id="@+id/sbGap"
        style="@style/Widget.AppCompat.SeekBar.Discrete"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:max="99"
        android:progress="0"
        app:layout_constraintBottom_toBottomOf="@+id/tvGap"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvGap"
        app:layout_constraintTop_toTopOf="@id/tvGap" />

    <TextView
        android:id="@+id/tvAngleTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="角度："
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvGap" />

    <TextView
        android:id="@+id/tvAngle"
        android:layout_width="30dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@id/tvAngleTitle"
        app:layout_constraintStart_toEndOf="@id/tvAngleTitle"
        app:layout_constraintTop_toTopOf="@id/tvAngleTitle" />

    <SeekBar
        android:id="@+id/sbAngle"
        style="@style/Widget.AppCompat.SeekBar.Discrete"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:max="360"
        android:progress="0"
        app:layout_constraintBottom_toBottomOf="@+id/tvAngle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvAngle"
        app:layout_constraintTop_toTopOf="@id/tvAngle" />

</androidx.constraintlayout.widget.ConstraintLayout>