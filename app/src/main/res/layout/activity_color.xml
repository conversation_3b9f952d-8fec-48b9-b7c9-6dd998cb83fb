<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".works.color.ColorActivity">

    <include
        layout="@layout/layout_toolbar"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/viewR"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_margin="8dp"
        app:layout_constraintBottom_toTopOf="@id/viewG"
        app:layout_constraintTop_toBottomOf="@+id/toolbar" />

    <View
        android:id="@+id/viewG"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_margin="8dp"
        app:layout_constraintBottom_toTopOf="@id/viewB"
        app:layout_constraintTop_toBottomOf="@+id/viewR" />

    <View
        android:id="@+id/viewB"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_margin="8dp"
        app:layout_constraintBottom_toTopOf="@id/viewMix"
        app:layout_constraintTop_toBottomOf="@+id/viewG" />

    <View
        android:id="@+id/viewMix"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_margin="8dp"
        app:layout_constraintBottom_toTopOf="@id/sbR"
        app:layout_constraintTop_toBottomOf="@+id/viewB" />

    <TextView
        android:id="@+id/tvR"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:ems="4"
        android:text="R"
        app:layout_constraintBottom_toBottomOf="@+id/sbR"
        app:layout_constraintEnd_toStartOf="@+id/sbR"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/sbR" />

    <SeekBar
        android:id="@+id/sbR"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:max="255"
        android:progress="255"
        app:layout_constraintBottom_toTopOf="@id/sbG"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvR" />

    <TextView
        android:id="@+id/tvG"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:ems="4"
        android:text="G"
        app:layout_constraintBottom_toBottomOf="@+id/sbG"
        app:layout_constraintEnd_toStartOf="@+id/sbG"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/sbG" />

    <SeekBar
        android:id="@+id/sbG"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:max="255"
        android:progress="255"
        app:layout_constraintBottom_toTopOf="@id/sbB"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvG" />

    <TextView
        android:id="@+id/tvB"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:ems="4"
        android:text="B"
        app:layout_constraintBottom_toBottomOf="@+id/sbB"
        app:layout_constraintEnd_toStartOf="@+id/sbB"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/sbB" />

    <SeekBar
        android:id="@+id/sbB"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:max="255"
        android:progress="255"
        app:layout_constraintBottom_toTopOf="@id/sbLight"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvB" />

    <TextView
        android:id="@+id/tvLight"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:ems="4"
        android:text="L"
        app:layout_constraintBottom_toBottomOf="@+id/sbLight"
        app:layout_constraintEnd_toStartOf="@+id/sbLight"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/sbLight" />

    <SeekBar
        android:id="@+id/sbLight"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:max="255"
        android:progress="255"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvLight" />
</androidx.constraintlayout.widget.ConstraintLayout>