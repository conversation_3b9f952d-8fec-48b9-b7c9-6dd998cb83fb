<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/ivImage"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scaleType="centerInside"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvName"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textAlignment="textStart"
        android:textDirection="locale"
        android:textSize="14sp"
        app:layout_constraintTop_toBottomOf="@id/ivImage" />

    <TextView
        android:id="@+id/tvSize"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textDirection="locale"
        android:textSize="14sp"
        app:layout_constraintTop_toBottomOf="@id/tvName" />

    <CheckBox
        android:id="@+id/cbCheck"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>