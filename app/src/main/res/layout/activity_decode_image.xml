<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".works.decode.DecodeImageActivity">

    <include
        layout="@layout/layout_toolbar"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ivImage"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_margin="8dp"
        android:src="@drawable/ic_add"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar" />

    <TextView
        android:id="@+id/tvLittleTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="8dp"
        android:text="最大宽度（px）"
        app:layout_constraintBottom_toBottomOf="@id/etMaxWidth"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivImage"
        app:layout_constraintTop_toTopOf="@id/etMaxWidth" />

    <EditText
        android:id="@+id/etMaxWidth"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginVertical="8dp"
        android:inputType="number"
        android:text="1000"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvLittleTitle"
        app:layout_constraintTop_toBottomOf="@id/ivImage" />

    <TextView
        android:id="@+id/tvMaxCount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="8dp"
        android:text="解码次数"
        app:layout_constraintBottom_toBottomOf="@id/etMaxCount"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/etMaxCount"
        app:layout_constraintTop_toTopOf="@id/etMaxCount" />

    <EditText
        android:id="@+id/etMaxCount"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginVertical="8dp"
        android:inputType="number"
        android:text="100"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvLittleTitle"
        app:layout_constraintTop_toBottomOf="@id/etMaxWidth" />

    <TextView
        android:id="@+id/tvInterval"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="8dp"
        android:text="解码间隔（ms）"
        app:layout_constraintBottom_toBottomOf="@id/etInterval"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/etInterval"
        app:layout_constraintTop_toTopOf="@id/etInterval" />

    <EditText
        android:id="@+id/etInterval"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginVertical="8dp"
        android:inputType="number"
        android:text="1000"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvLittleTitle"
        app:layout_constraintTop_toBottomOf="@id/etMaxCount" />

    <androidx.appcompat.widget.SwitchCompat
        android:id="@+id/switchLog"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:checked="true"
        android:text="日志开关"
        app:layout_constraintBottom_toBottomOf="@id/btnDecode"
        app:layout_constraintEnd_toStartOf="@id/btnDecode"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/btnDecode" />

    <Button
        android:id="@+id/btnDecode"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:maxLength="4"
        android:maxLines="1"
        android:text="解码"
        app:layout_constraintEnd_toStartOf="@+id/spFormat"
        app:layout_constraintStart_toEndOf="@id/switchLog"
        app:layout_constraintTop_toBottomOf="@id/etInterval" />

    <androidx.appcompat.widget.AppCompatSpinner
        android:id="@+id/spFormat"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:entries="@array/image_encode_format"
        app:layout_constraintBottom_toBottomOf="@+id/btnDecode"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/btnDecode"
        app:layout_constraintTop_toTopOf="@id/btnDecode" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvRecycler"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btnDecode" />


</androidx.constraintlayout.widget.ConstraintLayout>