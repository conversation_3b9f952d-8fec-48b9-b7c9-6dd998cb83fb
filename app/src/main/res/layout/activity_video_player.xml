<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".works.decode.DecodeVideoActivity">

    <include
        layout="@layout/layout_toolbar"
        app:layout_constraintTop_toTopOf="parent" />

    <com.hu.demo.main.works.videoplayer.PlayerView
        android:id="@+id/pvPlayer"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_margin="8dp"
        app:layout_constraintBottom_toTopOf="@+id/tvTrim"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar" />

    <TextView
        android:id="@+id/tvCurrentVelocity"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="8dp"
        android:textColor="#F44336"
        app:layout_constraintStart_toStartOf="@id/pvPlayer"
        app:layout_constraintTop_toTopOf="@id/pvPlayer" />

    <com.hu.demo.main.works.videoplayer.TrimView
        android:id="@+id/tvTrim"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="#A19D9C"
        app:layout_constraintBottom_toTopOf="@+id/sbRange" />

    <TextView
        android:id="@+id/tvRange"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ems="4"
        android:text="范围:20"
        app:layout_constraintBottom_toBottomOf="@id/sbRange"
        app:layout_constraintEnd_toStartOf="@+id/sbRange"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/sbRange" />

    <SeekBar
        android:id="@+id/sbRange"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginVertical="8dp"
        android:max="100"
        android:min="1"
        android:progress="20"
        app:layout_constraintBottom_toTopOf="@id/sbVelocity"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvRange" />

    <TextView
        android:id="@+id/tvVelocity"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ems="4"
        android:text="速度:500"
        app:layout_constraintBottom_toBottomOf="@id/sbVelocity"
        app:layout_constraintEnd_toStartOf="@+id/sbVelocity"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/sbVelocity" />

    <SeekBar
        android:id="@+id/sbVelocity"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginVertical="8dp"
        android:max="1000"
        android:min="1"
        android:progress="500"
        app:layout_constraintBottom_toTopOf="@id/sbRotation"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvRotation" />

    <TextView
        android:id="@+id/tvRotation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ems="4"
        android:text="旋转"
        app:layout_constraintBottom_toBottomOf="@id/sbRotation"
        app:layout_constraintEnd_toStartOf="@+id/sbRotation"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/sbRotation" />

    <SeekBar
        android:id="@+id/sbRotation"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginVertical="8dp"
        android:max="360"
        app:layout_constraintBottom_toTopOf="@id/sbScale"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvRotation" />

    <TextView
        android:id="@+id/tvScale"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ems="4"
        android:text="缩放"
        app:layout_constraintBottom_toBottomOf="@id/sbScale"
        app:layout_constraintEnd_toStartOf="@+id/sbScale"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/sbScale" />

    <SeekBar
        android:id="@+id/sbScale"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginVertical="8dp"
        android:max="100"
        app:layout_constraintBottom_toTopOf="@id/btnSelect"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvScale" />

    <Spinner
        android:id="@+id/spPinner"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:entries="@array/video_play_view_type"
        app:layout_constraintBottom_toBottomOf="@id/btnSelect"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/btnSelect" />

    <Button
        android:id="@+id/btnSelect"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="选择视频"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <ToggleButton
        android:id="@+id/tbColorMode"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@id/btnSelect"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/btnSelect" />


</androidx.constraintlayout.widget.ConstraintLayout>