<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_item_title"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_marginStart="@dimen/item_margin"
        android:layout_marginTop="@dimen/item_margin"
        android:layout_marginEnd="@dimen/item_margin"
        android:textSize="18sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_item_image1"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_marginStart="@dimen/item_margin"
        android:layout_marginTop="@dimen/item_margin"
        android:layout_marginBottom="@dimen/item_margin"
        android:scaleType="centerInside"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="W,1:1"
        app:layout_constraintEnd_toStartOf="@id/iv_item_image2"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_item_title" />

    <ImageView
        android:id="@+id/iv_item_image2"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_marginTop="@dimen/item_margin"
        android:layout_marginBottom="@dimen/item_margin"
        android:scaleType="centerInside"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="W,1:1"
        app:layout_constraintEnd_toStartOf="@id/iv_item_image3"
        app:layout_constraintStart_toEndOf="@id/iv_item_image1"
        app:layout_constraintTop_toBottomOf="@id/tv_item_title" />

    <ImageView
        android:id="@+id/iv_item_image3"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_marginTop="@dimen/item_margin"
        android:layout_marginBottom="@dimen/item_margin"
        android:scaleType="centerInside"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="W,1:1"
        app:layout_constraintEnd_toStartOf="@id/iv_item_image4"
        app:layout_constraintStart_toEndOf="@id/iv_item_image2"
        app:layout_constraintTop_toBottomOf="@id/tv_item_title" />

    <ImageView
        android:id="@+id/iv_item_image4"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_marginTop="@dimen/item_margin"
        android:layout_marginBottom="@dimen/item_margin"
        android:scaleType="centerInside"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="W,1:1"
        app:layout_constraintEnd_toStartOf="@id/iv_item_image5"
        app:layout_constraintStart_toEndOf="@id/iv_item_image3"
        app:layout_constraintTop_toBottomOf="@id/tv_item_title" />

    <ImageView
        android:id="@+id/iv_item_image5"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_marginTop="@dimen/item_margin"
        android:layout_marginBottom="@dimen/item_margin"
        android:scaleType="centerInside"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="W,1:1"
        app:layout_constraintEnd_toStartOf="@id/iv_item_image6"
        app:layout_constraintStart_toEndOf="@id/iv_item_image4"
        app:layout_constraintTop_toBottomOf="@id/tv_item_title" />

    <ImageView
        android:id="@+id/iv_item_image6"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_marginTop="@dimen/item_margin"
        android:layout_marginEnd="@dimen/item_margin"
        android:layout_marginBottom="@dimen/item_margin"
        android:scaleType="centerInside"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="W,1:1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_item_image5"
        app:layout_constraintTop_toBottomOf="@id/tv_item_title" />
</androidx.constraintlayout.widget.ConstraintLayout>