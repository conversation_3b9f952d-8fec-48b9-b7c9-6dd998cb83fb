<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".works.exif.ExifActivity">

    <include
        layout="@layout/layout_toolbar"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/tvHasThumbTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Exif是否包含缩略图："
        app:layout_constraintEnd_toStartOf="@+id/tvHasThumbValue"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar" />

    <TextView
        android:id="@+id/tvHasThumbValue"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvHasThumbTitle"
        app:layout_constraintTop_toBottomOf="@+id/toolbar" />

    <TextView
        android:id="@+id/tvHasThumbWidthTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="缩略图Width："
        app:layout_constraintEnd_toStartOf="@+id/tvHasThumbValue"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvHasThumbTitle" />

    <TextView
        android:id="@+id/tvHasThumbWidthValue"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvHasThumbWidthTitle"
        app:layout_constraintTop_toBottomOf="@id/tvHasThumbValue" />

    <TextView
        android:id="@+id/tvHasThumbHeightTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="缩略图Height："
        app:layout_constraintEnd_toStartOf="@+id/tvHasThumbHeightValue"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvHasThumbWidthTitle" />

    <TextView
        android:id="@+id/tvHasThumbHeightValue"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvHasThumbHeightTitle"
        app:layout_constraintTop_toBottomOf="@id/tvHasThumbWidthValue" />

    <TextView
        android:id="@+id/tvThumbColorSpaceTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="exif图ColorSpace："
        app:layout_constraintEnd_toStartOf="@+id/tvThumbColorSpaceValue"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvHasThumbHeightTitle" />

    <TextView
        android:id="@+id/tvThumbColorSpaceValue"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvThumbColorSpaceTitle"
        app:layout_constraintTop_toBottomOf="@id/tvHasThumbHeightValue" />

    <TextView
        android:id="@+id/tvImageColorSpaceTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="图片ColorSpace："
        app:layout_constraintEnd_toStartOf="@+id/tvHasThumbHeightValue"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvThumbColorSpaceTitle" />

    <TextView
        android:id="@+id/tvImageColorSpaceValue"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvImageColorSpaceTitle"
        app:layout_constraintTop_toBottomOf="@id/tvThumbColorSpaceValue" />
    <ImageView
        android:id="@+id/ivImage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toTopOf="@+id/btnSelectImg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvHasThumbHeightTitle" />

    <Button
        android:id="@+id/btnSelectImg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="选图"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>