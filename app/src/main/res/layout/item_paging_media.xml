<?xml version="1.0" encoding="utf-8"?>
<com.hu.demo.main.works.paging.ui.MediaItemView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv_image"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/tv_media_id"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#7F000000"
        android:textColor="@color/white"
        android:textSize="6sp"
        app:layout_constraintBottom_toBottomOf="@id/iv_image"
        app:layout_constraintTop_toTopOf="@id/iv_image"/>

    <TextView
        android:id="@+id/tv_media_id"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="8sp"
        app:layout_constraintBottom_toTopOf="@id/tv_datetaken"
        app:layout_constraintTop_toBottomOf="@id/iv_image"/>

    <TextView
        android:id="@+id/tv_datetaken"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="8sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_media_id"/>

</com.hu.demo.main.works.paging.ui.MediaItemView>