<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".works.share.ShareActivity">

    <include
        layout="@layout/layout_toolbar"
        app:layout_constraintTop_toTopOf="parent" />

    <com.hu.demo.main.works.gl.EditingSurfaceView
        android:id="@+id/glView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/sbSeek"
        app:layout_constraintTop_toBottomOf="@+id/toolbar" />

    <TextView
        android:id="@+id/tvSdrHdrRatio"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/green"
        android:textColor="@color/purple_200"
        app:layout_constraintStart_toStartOf="@id/glView"
        app:layout_constraintTop_toTopOf="@id/glView" />

    <RadioGroup
        android:id="@+id/rbBrightnessGroup"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@color/green"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@+id/tvSdrHdrRatio">

        <RadioButton
            android:id="@+id/opengl_brightness_0"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="0" />

        <RadioButton
            android:id="@+id/opengl_brightness_30"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="30" />

        <RadioButton
            android:id="@+id/opengl_brightness_70"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="70" />

        <RadioButton
            android:id="@+id/opengl_brightness_100"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="100" />
    </RadioGroup>

    <SeekBar
        android:id="@+id/sbSeek"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:max="100"
        android:progress="80"
        app:layout_constraintBottom_toTopOf="@id/btnSelectImg"
        app:layout_constraintEnd_toStartOf="@id/btnSelectVideo"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/glView" />

    <Button
        android:id="@+id/btnSelectImg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="图片"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/btnSelectVideo"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/sbSeek" />

    <Button
        android:id="@+id/btnSelectVideo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="视频"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/ibPlay"
        app:layout_constraintStart_toEndOf="@+id/btnSelectImg"
        app:layout_constraintTop_toBottomOf="@id/sbSeek" />

    <ImageButton
        android:id="@+id/ibPlay"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:background="@null"
        android:clickable="false"
        android:enabled="false"
        android:src="@drawable/ic_play"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/btnSelectVideo"
        app:layout_constraintTop_toBottomOf="@id/sbSeek" />


</androidx.constraintlayout.widget.ConstraintLayout>