plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
}

android {
    namespace = "com.hu.demo.main"
    compileSdk = libs.versions.compileSdk.get().toInt()

    defaultConfig {
        applicationId = "com.hu.demo"
        minSdk = libs.versions.minSdk.get().toInt()
        versionCode = 1
        targetSdk = libs.versions.targetSdk.get().toInt()
        versionName = "1.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }
    signingConfigs {
        register("release") {
            storeFile = file("D:\\Users\\80253712\\hu_release.jks")
            storePassword = "hucanhua"
            keyAlias = "release_test"
            keyPassword = "hucanhua"
        }
    }
    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
            signingConfig = signingConfigs.getByName("release")
        }
        debug {
            isJniDebuggable = true
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = "17"
    }
    testOptions {
        unitTests.isReturnDefaultValues = true
    }
    packaging {
        resources {
            excludes.add("META-INF/LICENSE.md")
        }
    }
    buildFeatures {
        viewBinding = true
    }
}

dependencies {
    testImplementation(libs.junit4)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.test.espresso.core)

    implementation(libs.androidx.activity.ktx)
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.androidx.activity.ktx)
    implementation(libs.material)
    implementation(libs.androidx.constraintlayout)
    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation(libs.androidx.lifecycle.viewmodel.ktx)
    implementation(libs.androidx.lifecycle.livedata.ktx)
    implementation(libs.androidx.recyclerview)
    implementation(libs.androidx.fragment.ktx)
    implementation(libs.okhttp)
    implementation(libs.androidx.paging.runtime.ktx)
    implementation(libs.androidx.navigation.fragment.ktx)
    implementation(libs.androidx.navigation.ui.ktx)
    // optional - Guava ListenableFuture support
    implementation(libs.androidx.paging.guava)
    implementation(libs.glide)
    implementation(libs.androidx.window)
    implementation(libs.androidx.asynclayoutinflater)
    // Kotlin
    implementation(libs.androidx.collection.ktx)
    implementation(libs.androidx.concurrent.futures)
    // Kotlin
    implementation(libs.androidx.concurrent.futures.ktx)
    implementation(libs.androidx.contentpager)
    implementation(libs.androidx.draganddrop)
    implementation(libs.androidx.exifinterface)
    implementation(libs.androidx.graphics.core)
    implementation(libs.androidx.gridlayout)
    implementation(libs.androidx.heifwriter)
    implementation(libs.commonplayer.retriever)
    implementation(libs.commons.imaging)
    implementation(libs.xmpcore)
    implementation(libs.gson)
    // TBL player 播放
    implementation("com.oplus.TBLPlayer",  "CommonPlayer",  libs.versions.commonplayer.get()) {
        exclude("com.squareup.okhttp3")
    }
    implementation(libs.itext7.core)
    implementation(libs.androidx.work.runtime.ktx)
    implementation(libs.androidx.startup)
    implementation(libs.org.mp4parser)

    implementation(project(":base"))
    implementation(project(":utils"))
    implementation(project(":buffer"))
}